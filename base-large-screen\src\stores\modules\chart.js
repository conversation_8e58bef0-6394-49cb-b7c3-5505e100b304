import { ref } from "vue";
import { defineStore } from "pinia";
import { getChartData } from "../../api";

export const useChartStore = defineStore("chart", () => {
  const chartData = ref({});

  async function getData(query) {
    try {
      const params = {
        ...query,
      };
      const data = await getChartData(params);
      chartData.value = data;
    } catch (error) {
      console.log(error);
    }
  }

  return { chartData, getData };
});
