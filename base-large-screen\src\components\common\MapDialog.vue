<template>
  <template v-if="dialogStore.activateName === props.name">
    <Teleport v-if="props.mountMap" :to="teleportDOM">
      <slot></slot>
    </Teleport>
    <UDialog v-else @close="dialogClose">
      <slot></slot>
    </UDialog>
  </template>
</template>
<script setup>
import { onMounted, onUnmounted, computed } from "vue";
import { useMapDialogStore, mountDialog, unmountDialog } from "../../stores/modules/mapDialog";

const props = defineProps({
  name: {
    type: String,
    required: true
  },
  mountMap: {
    type: Boolean,
    default: true
  }
})


const dialogStore = useMapDialogStore();

const teleportDOM = computed(() => {
  if (dialogStore.mapType === 'ol') {
    return '#olMap-overlay-container'
  } else if (dialogStore.mapType === 'cesium') {
    return '#cesium-overlay-container'
  }
})

function dialogClose() {
  dialogStore.close()
}

onMounted(() => {
  mountDialog(props.name, props.mountMap)
})
onUnmounted(() => {
  unmountDialog(props.name)
})
</script>
