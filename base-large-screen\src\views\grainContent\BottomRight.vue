<template>
  <div class="box">
    <GrainPartTitle title="环境监测预警" :position="2" />
    <div class="body">
      <EchartsContainer :option="chartOption" style="width:374px;height:194px" />
      <div class="line">
        <div class="text">严重污染</div>
        <div class="value" style="background-color: #a70069;">＞300</div>
        <div class="text">重度污染</div>
        <div class="value" style="background-color: #d62670;">201-300</div>
        <div class="text">中度污染</div>
        <div class="value" style="background-color: #f85c58;">151-200</div>
        <div class="text">轻度污染</div>
        <div class="value" style="background-color: #ff8a1b;">101-150</div>
        <div class="text">良</div>
        <div class="value" style="background-color: #f7b307;">51-100</div>
        <div class="text">优</div>
        <div class="value" style="background-color: #19b863;">0-50</div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { shallowRef, inject, onMounted } from "vue";
import GrainPartTitle from "../../components/common/GrainPartTitle.vue"
import EchartsContainer from "../../components/common/EchartsContainer.vue"
import { useGrainStore } from "../../stores/modules/grain";

const { pageData } = useGrainStore()
const chartOption = shallowRef({})

const pageTl = inject('pageTl')
const isEnter = inject('isEnter')

function initTl() {
  if (pageTl) {
    pageTl.add(setOption, 2)
  } else {
    setOption()
  }
}

function setOption() {
  if (isEnter && !isEnter.value) {
    return
  }
  const xAxisData = []
  const seriesData = []
  if (pageData.environment && pageData.environment.length) {
    pageData.environment.forEach(item => {
      xAxisData.push(item.day)
      seriesData.push(item.value)
    })
  }
  chartOption.value = {
    backgroundColor: {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 1,
      y2: 0,
      colorStops: [{
        offset: 0, color: "rgba(0,94,161,0.34)" // 0% 处的颜色
      }, {
        offset: 1, color: "rgba(60,124,189,0)" // 100% 处的颜色
      }],
      global: false // 缺省为 false
    },
    title: {
      text: "pm2.5实时数据（ug/m3）",
      textStyle: {
        color: "#CEDCE6",
        fontSize: 16,
      },
      left: "center",
      top: "5"
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    grid: {
      left: '20',
      right: '20',
      top: '40',
      bottom: '10',
      containLabel: true
    },
    xAxis: [
      {
        show: true,
        type: 'category',
        boundaryGap: false,
        axisLabel: {
          color: "#ADDDFF",
          fontSize: 12,
        },
        data: xAxisData
      }
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          color: "#ADDDFF",
          fontSize: 12,
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: '#245085'
          }
        }
      }
    ],
    series: {
      name: "空气质量指数",
      type: 'line',
      symbol: 'circle',
      symbolSize: 7,
      itemStyle: {
        color: "#4CD8FF",
        borderColor: "#0E9CFF",
        borderWidth: 2
      },
      lineStyle: {
        color: "#0E9CFF",
        wdith: 1
      },
      data: seriesData
    }
  }
}

onMounted(() => {
  initTl()
})
</script>
<style scoped lang="scss">
.box {
  height: 252px;
  width: 450px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.body {
  width: 450px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 17px;

  .line {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 62px;

    .text {
      font-size: 12px;
      line-height: 16px;
      font-family: Adobe Heiti Std;
      font-weight: normal;
      color: #FFFFFF;
    }

    .value {
      width: 62px;
      height: 18px;
      border-radius: 3px;
      text-align: center;
      font-size: 12px;
      line-height: 18px;
      font-family: Adobe Heiti Std;
      font-weight: normal;
      color: #FFFFFF;
    }
  }
}
</style>