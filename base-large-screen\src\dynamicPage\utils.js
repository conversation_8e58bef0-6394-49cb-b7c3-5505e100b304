import { defineAsyncComponent } from "vue";
import { getDynamicPage, getLocalDynamicPage } from "@/api";
import { CompsConfig, Modules } from "./config";
import DynamicPage from "./index.vue";
import DynamicPageEdit from "./edit.vue";
import { useDynamicStore } from "@/stores/system/dynamicPage";

const PageTemplate = {
  name: "",
  routePath: "",
  layout: "adapt",
  pageSize: {
    width: 1920,
    height: 1080,
  },
  layers: {},
};

let pagesConfig = [JSON.parse(JSON.stringify(PageTemplate))];

const Components = {};

let lsid;

async function dynamicPageInit(router, query) {
  if (import.meta.env.MODE === "localDynamic") {
    return new Promise((resolve, reject) => {
      getLocalDynamicPage()
        .then((res) => {
          if (res && res.length) {
            pagesConfig = res;
            generateRoutes(res, router);
          }
          resolve();
        })
        .catch((e) => {
          console.log(e);
          resolve();
        });
    });
  }
  if (!query.lsid) {
    return;
  }
  lsid = query.lsid;
  return new Promise((resolve, reject) => {
    getDynamicPage(lsid)
      .then((res) => {
        if (res.title) {
          document.title = res.title;
        }
        if (res.pages && res.pages.length) {
          pagesConfig = res.pages;
          generateRoutes(res.pages, router);
        }
        resolve();
      })
      .catch((e) => {
        console.log(e);
        resolve();
      });
  });
}

function generateRoutes(pages, router) {
  for (let index = 0; index < pages.length; index++) {
    const { name, disabled, routePath, layout, pageSize, layers = {} } = pages[index];
    if (disabled) {
      continue;
    }
    const routeComponents = {};
    const storeKeys = [];
    for (const key in layers) {
      if (Object.hasOwnProperty.call(layers, key)) {
        const comps = layers[key];
        if (comps && comps.length) {
          routeComponents[key] = DynamicPage;
          createComponents(comps);
          createStoreKeys(storeKeys, comps);
        }
      }
    }
    const route = {
      path: routePath,
      components: routeComponents,
      meta: {
        layout,
        pageSize,
        layers,
        storeKeys,
        stores: [useDynamicStore],
      },
    };
    router.addRoute(route);
  }
}

function previewPage(page, router) {
  const { layout, pageSize, layers = {} } = page;
  const routeComponents = {};
  const storeKeys = [];
  for (const key in layers) {
    if (Object.hasOwnProperty.call(layers, key)) {
      const comps = layers[key];
      if (comps && comps.length) {
        routeComponents[key] = DynamicPage;
        createComponents(comps);
        createStoreKeys(storeKeys, comps);
      }
    }
  }
  const route = {
    path: "/pagePreview",
    name: "PagePreview",
    components: {
      editLayer: DynamicPageEdit,
      ...routeComponents,
    },
    meta: {
      layout,
      pageSize,
      layers,
      storeKeys,
      stores: [useDynamicStore],
    },
  };
  router.removeRoute("PagePreview");
  router.addRoute(route);
  router.push({
    name: "PagePreview",
  });
}

function createComponents(comps) {
  for (const comp of comps) {
    if (Modules[comp.key] && !Components[comp.key]) {
      Components[comp.key] = defineAsyncComponent(() => Modules[comp.key]());
    }
  }
}

function createStoreKeys(storeKeys, comps) {
  for (const comp of comps) {
    const { storeKey, storePrefix } = CompsConfig[comp.key] || {};
    const keys = [];
    if (typeof storeKey === "string") {
      keys.push(storeKey);
    } else if (Array.isArray(storeKey)) {
      keys.push(...storeKey);
    }
    for (const key of keys) {
      const composeKey = `${storePrefix}_${key}`;
      if (!storeKeys.includes(composeKey)) {
        storeKeys.push(composeKey);
      }
    }
  }
}

export { Components, PageTemplate, pagesConfig, dynamicPageInit, previewPage, lsid };
