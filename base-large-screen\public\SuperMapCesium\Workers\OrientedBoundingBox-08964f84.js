define(["exports","./buildModuleUrl-8958744c","./Cartesian2-47311507","./Cartographic-3309dd0d","./Cartesian4-3ca25aab","./Check-7b2a090c","./when-b60132fc","./EllipsoidTangentPlane-ce9a1fbb","./Math-119be1a3","./FeatureDetection-806b12f0","./Plane-a3d8b3d2","./PolygonPipeline-d328cdf1"],(function(a,t,e,n,r,i,s,o,C,d,u,c){"use strict";var l=[];function h(a,t){this.center=n.Cartesian3.clone(s.defaultValue(a,n.Cartesian3.ZERO)),this.halfAxes=d.Matrix3.clone(s.defaultValue(t,d.Matrix3.ZERO)),this.areaDirty=1}l[0]={num:0,des:"inside"},l[1]={num:4,data:[0,4,7,3],des:"left"},l[2]={num:4,data:[1,2,6,5],des:"right"},l[3]={num:0},l[4]={num:4,data:[0,1,5,4],des:"bottom"},l[5]={num:6,data:[0,1,5,4,7,3],des:"bottom, left"},l[6]={num:6,data:[0,1,2,6,5,4],des:"bottom, right"},l[7]={num:0},l[8]={num:4,data:[2,3,7,6],des:"top"},l[9]={num:6,data:[4,7,6,2,3,0],des:"top, left"},l[10]={num:6,data:[2,3,7,6,5,1],des:"top, right"},l[11]={num:0},l[12]={num:0},l[13]={num:0},l[14]={num:0},l[15]={num:0},l[16]={num:4,data:[0,3,2,1],des:"front"},l[17]={num:6,data:[0,4,7,3,2,1],des:"front, left"},l[18]={num:6,data:[0,3,2,6,5,1],des:"front, right"},l[19]={num:0},l[20]={num:6,data:[0,3,2,1,5,4],des:"front, bottom"},l[21]={num:6,data:[2,1,5,4,7,3],des:"front, bottom, left"},l[22]={num:6,data:[0,3,2,6,5,4],des:"front, bottom, right"},l[23]={num:0},l[24]={num:6,data:[0,3,7,6,2,1],des:"front, top"},l[25]={num:6,data:[0,4,7,6,2,1],des:"front, top, left"},l[26]={num:6,data:[0,3,7,6,5,1],des:"front, top, right"},l[27]={num:0},l[28]={num:0},l[29]={num:0},l[30]={num:0},l[31]={num:0},l[32]={num:4,data:[4,5,6,7],des:"back"},l[33]={num:6,data:[4,5,6,7,3,0],des:"back, left"},l[34]={num:6,data:[1,2,6,7,4,5],des:"back, right"},l[35]={num:0},l[36]={num:6,data:[0,1,5,6,7,4],des:"back, bottom"},l[37]={num:6,data:[0,1,5,6,7,3],des:"back, bottom, left"},l[38]={num:6,data:[0,1,2,6,7,4],des:"back, bottom, right"},l[39]={num:0},l[40]={num:6,data:[2,3,7,4,5,6],des:"back, top"},l[41]={num:6,data:[0,4,5,6,2,3],des:"back, top, left"},l[42]={num:6,data:[1,2,3,7,4,5],des:"back, top, right"},h.packedLength=n.Cartesian3.packedLength+d.Matrix3.packedLength,h.pack=function(a,t,e){return e=s.defaultValue(e,0),n.Cartesian3.pack(a.center,t,e),d.Matrix3.pack(a.halfAxes,t,e+n.Cartesian3.packedLength),t},h.unpack=function(a,t,e){return t=s.defaultValue(t,0),s.defined(e)||(e=new h),n.Cartesian3.unpack(a,t,e.center),d.Matrix3.unpack(a,t+n.Cartesian3.packedLength,e.halfAxes),e};var m=new n.Cartesian3,x=new n.Cartesian3,p=new n.Cartesian3,M=new n.Cartesian3,f=new n.Cartesian3,g=new n.Cartesian3,b=new d.Matrix3,w={unitary:new d.Matrix3,diagonal:new d.Matrix3},y=new n.Cartesian3,v=new n.Cartesian3,P=new n.Cartesian3;h.fromPoints=function(a,t){if(s.defined(t)||(t=new h),!s.defined(a)||0===a.length)return t.halfAxes=d.Matrix3.ZERO,t.center=n.Cartesian3.ZERO,t;var e,r=a.length,i=n.Cartesian3.clone(a[0],m);for(e=1;e<r;e++)n.Cartesian3.add(i,a[e],i);var o=1/r;n.Cartesian3.multiplyByScalar(i,o,i);var C,u=0,c=0,l=0,y=0,v=0,P=0;for(e=0;e<r;e++)u+=(C=n.Cartesian3.subtract(a[e],i,x)).x*C.x,c+=C.x*C.y,l+=C.x*C.z,y+=C.y*C.y,v+=C.y*C.z,P+=C.z*C.z;u*=o,c*=o,l*=o,y*=o,v*=o,P*=o;var O=b;O[0]=u,O[1]=c,O[2]=l,O[3]=c,O[4]=y,O[5]=v,O[6]=l,O[7]=v,O[8]=P;var z=d.Matrix3.computeEigenDecomposition(O,w),A=d.Matrix3.clone(z.unitary,t.halfAxes),N=d.Matrix3.getColumn(A,0,M),R=d.Matrix3.getColumn(A,1,f),T=d.Matrix3.getColumn(A,2,g),I=-Number.MAX_VALUE,E=-Number.MAX_VALUE,B=-Number.MAX_VALUE,L=Number.MAX_VALUE,k=Number.MAX_VALUE,U=Number.MAX_VALUE;for(e=0;e<r;e++)C=a[e],I=Math.max(n.Cartesian3.dot(N,C),I),E=Math.max(n.Cartesian3.dot(R,C),E),B=Math.max(n.Cartesian3.dot(T,C),B),L=Math.min(n.Cartesian3.dot(N,C),L),k=Math.min(n.Cartesian3.dot(R,C),k),U=Math.min(n.Cartesian3.dot(T,C),U);N=n.Cartesian3.multiplyByScalar(N,.5*(L+I),N),R=n.Cartesian3.multiplyByScalar(R,.5*(k+E),R),T=n.Cartesian3.multiplyByScalar(T,.5*(U+B),T);var V=n.Cartesian3.add(N,R,t.center);n.Cartesian3.add(V,T,V);var S=p;return S.x=I-L,S.y=E-k,S.z=B-U,n.Cartesian3.multiplyByScalar(S,.5,S),d.Matrix3.multiplyByScale(t.halfAxes,S,t.halfAxes),t};var O=new n.Cartesian3,z=new n.Cartesian3;function A(a,t,e,r,i,o,C,u,c,l,m){s.defined(m)||(m=new h);var x=m.halfAxes;d.Matrix3.setColumn(x,0,t,x),d.Matrix3.setColumn(x,1,e,x),d.Matrix3.setColumn(x,2,r,x);var p=O;p.x=(i+o)/2,p.y=(C+u)/2,p.z=(c+l)/2;var M=z;M.x=(o-i)/2,M.y=(u-C)/2,M.z=(l-c)/2;var f=m.center;return p=d.Matrix3.multiplyByVector(x,p,p),n.Cartesian3.add(a,p,f),d.Matrix3.multiplyByScale(x,M,x),m}var N=new n.Cartographic,R=new n.Cartesian3,T=new n.Cartographic,I=new n.Cartographic,E=new n.Cartographic,B=new n.Cartographic,L=new n.Cartographic,k=new n.Cartesian3,U=new n.Cartesian3,V=new n.Cartesian3,S=new n.Cartesian3,D=new n.Cartesian3,_=new e.Cartesian2,X=new e.Cartesian2,W=new e.Cartesian2,q=new e.Cartesian2,j=new e.Cartesian2,Z=new n.Cartesian3,F=new n.Cartesian3,G=new n.Cartesian3,Y=new n.Cartesian3,H=new e.Cartesian2,J=new n.Cartesian3,K=new n.Cartesian3,Q=new n.Cartesian3,$=new u.Plane(n.Cartesian3.UNIT_X,0);h.fromRectangle=function(a,t,r,i,d){var c,l,h,m,x,p,M;if(t=s.defaultValue(t,0),r=s.defaultValue(r,0),i=s.defaultValue(i,e.Ellipsoid.WGS84),a.width<=C.CesiumMath.PI){var f=e.Rectangle.center(a,N),g=i.cartographicToCartesian(f,R),b=new o.EllipsoidTangentPlane(g,i);M=b.plane;var w=f.longitude,y=a.south<0&&a.north>0?0:f.latitude,v=n.Cartographic.fromRadians(w,a.north,r,T),P=n.Cartographic.fromRadians(a.west,a.north,r,I),O=n.Cartographic.fromRadians(a.west,y,r,E),z=n.Cartographic.fromRadians(a.west,a.south,r,B),aa=n.Cartographic.fromRadians(w,a.south,r,L),ta=i.cartographicToCartesian(v,k),ea=i.cartographicToCartesian(P,U),na=i.cartographicToCartesian(O,V),ra=i.cartographicToCartesian(z,S),ia=i.cartographicToCartesian(aa,D),sa=b.projectPointToNearestOnPlane(ta,_),oa=b.projectPointToNearestOnPlane(ea,X),Ca=b.projectPointToNearestOnPlane(na,W),da=b.projectPointToNearestOnPlane(ra,q),ua=b.projectPointToNearestOnPlane(ia,j);return l=-(c=Math.min(oa.x,Ca.x,da.x)),m=Math.max(oa.y,sa.y),h=Math.min(da.y,ua.y),P.height=z.height=t,ea=i.cartographicToCartesian(P,U),ra=i.cartographicToCartesian(z,S),x=Math.min(u.Plane.getPointDistance(M,ea),u.Plane.getPointDistance(M,ra)),p=r,A(b.origin,b.xAxis,b.yAxis,b.zAxis,c,l,h,m,x,p,d)}var ca=a.south>0,la=a.north<0,ha=ca?a.south:la?a.north:0,ma=e.Rectangle.center(a,N).longitude,xa=n.Cartesian3.fromRadians(ma,ha,r,i,Z);xa.z=0;var pa=Math.abs(xa.x)<C.CesiumMath.EPSILON10&&Math.abs(xa.y)<C.CesiumMath.EPSILON10?n.Cartesian3.UNIT_X:n.Cartesian3.normalize(xa,F),Ma=n.Cartesian3.UNIT_Z,fa=n.Cartesian3.cross(pa,Ma,G);M=u.Plane.fromPointNormal(xa,pa,$);var ga=n.Cartesian3.fromRadians(ma+C.CesiumMath.PI_OVER_TWO,ha,r,i,Y);c=-(l=n.Cartesian3.dot(u.Plane.projectPointOntoPlane(M,ga,H),fa)),m=n.Cartesian3.fromRadians(0,a.north,la?t:r,i,J).z,h=n.Cartesian3.fromRadians(0,a.south,ca?t:r,i,K).z;var ba=n.Cartesian3.fromRadians(a.east,ha,r,i,Q);return A(xa,fa,Ma,pa,c,l,h,m,x=u.Plane.getPointDistance(M,ba),p=0,d)},h.clone=function(a,t){if(s.defined(a))return s.defined(t)?(n.Cartesian3.clone(a.center,t.center),d.Matrix3.clone(a.halfAxes,t.halfAxes),t.areaDirty=1,t):new h(a.center,a.halfAxes)},h.intersectPlane=function(a,e){var r=a.center,i=e.normal,s=a.halfAxes,o=i.x,C=i.y,u=i.z,c=Math.abs(o*s[d.Matrix3.COLUMN0ROW0]+C*s[d.Matrix3.COLUMN0ROW1]+u*s[d.Matrix3.COLUMN0ROW2])+Math.abs(o*s[d.Matrix3.COLUMN1ROW0]+C*s[d.Matrix3.COLUMN1ROW1]+u*s[d.Matrix3.COLUMN1ROW2])+Math.abs(o*s[d.Matrix3.COLUMN2ROW0]+C*s[d.Matrix3.COLUMN2ROW1]+u*s[d.Matrix3.COLUMN2ROW2]),l=n.Cartesian3.dot(i,r)+e.distance;return l<=-c?t.Intersect.OUTSIDE:l>=c?t.Intersect.INSIDE:t.Intersect.INTERSECTING};var aa=new n.Cartesian3,ta=new n.Cartesian3,ea=new n.Cartesian3,na=new n.Cartesian3,ra=new n.Cartesian3;h.distanceSquaredTo=function(a,t){var e=n.Cartesian3.subtract(t,a.center,O),r=a.halfAxes,i=d.Matrix3.getColumn(r,0,aa),s=d.Matrix3.getColumn(r,1,ta),o=d.Matrix3.getColumn(r,2,ea),C=n.Cartesian3.magnitude(i),u=n.Cartesian3.magnitude(s),c=n.Cartesian3.magnitude(o);n.Cartesian3.normalize(i,i),n.Cartesian3.normalize(s,s),n.Cartesian3.normalize(o,o);var l=na;l.x=n.Cartesian3.dot(e,i),l.y=n.Cartesian3.dot(e,s),l.z=n.Cartesian3.dot(e,o);var h,m=0;return l.x<-C?m+=(h=l.x+C)*h:l.x>C&&(m+=(h=l.x-C)*h),l.y<-u?m+=(h=l.y+u)*h:l.y>u&&(m+=(h=l.y-u)*h),l.z<-c?m+=(h=l.z+c)*h:l.z>c&&(m+=(h=l.z-c)*h),m};var ia=new n.Cartesian3,sa=new n.Cartesian3;h.computePlaneDistances=function(a,e,r,i){s.defined(i)||(i=new t.Interval);var o=Number.POSITIVE_INFINITY,C=Number.NEGATIVE_INFINITY,u=a.center,c=a.halfAxes,l=d.Matrix3.getColumn(c,0,aa),h=d.Matrix3.getColumn(c,1,ta),m=d.Matrix3.getColumn(c,2,ea),x=n.Cartesian3.add(l,h,ia);n.Cartesian3.add(x,m,x),n.Cartesian3.add(x,u,x);var p=n.Cartesian3.subtract(x,e,sa),M=n.Cartesian3.dot(r,p);return o=Math.min(M,o),C=Math.max(M,C),n.Cartesian3.add(u,l,x),n.Cartesian3.add(x,h,x),n.Cartesian3.subtract(x,m,x),n.Cartesian3.subtract(x,e,p),M=n.Cartesian3.dot(r,p),o=Math.min(M,o),C=Math.max(M,C),n.Cartesian3.add(u,l,x),n.Cartesian3.subtract(x,h,x),n.Cartesian3.add(x,m,x),n.Cartesian3.subtract(x,e,p),M=n.Cartesian3.dot(r,p),o=Math.min(M,o),C=Math.max(M,C),n.Cartesian3.add(u,l,x),n.Cartesian3.subtract(x,h,x),n.Cartesian3.subtract(x,m,x),n.Cartesian3.subtract(x,e,p),M=n.Cartesian3.dot(r,p),o=Math.min(M,o),C=Math.max(M,C),n.Cartesian3.subtract(u,l,x),n.Cartesian3.add(x,h,x),n.Cartesian3.add(x,m,x),n.Cartesian3.subtract(x,e,p),M=n.Cartesian3.dot(r,p),o=Math.min(M,o),C=Math.max(M,C),n.Cartesian3.subtract(u,l,x),n.Cartesian3.add(x,h,x),n.Cartesian3.subtract(x,m,x),n.Cartesian3.subtract(x,e,p),M=n.Cartesian3.dot(r,p),o=Math.min(M,o),C=Math.max(M,C),n.Cartesian3.subtract(u,l,x),n.Cartesian3.subtract(x,h,x),n.Cartesian3.add(x,m,x),n.Cartesian3.subtract(x,e,p),M=n.Cartesian3.dot(r,p),o=Math.min(M,o),C=Math.max(M,C),n.Cartesian3.subtract(u,l,x),n.Cartesian3.subtract(x,h,x),n.Cartesian3.subtract(x,m,x),n.Cartesian3.subtract(x,e,p),M=n.Cartesian3.dot(r,p),o=Math.min(M,o),C=Math.max(M,C),i.start=o,i.stop=C,i};var oa=new t.BoundingSphere;h.isOccluded=function(a,e){var n=t.BoundingSphere.fromOrientedBoundingBox(a,oa);return!e.isBoundingSphereVisible(n)},h.prototype.intersectPlane=function(a){return h.intersectPlane(this,a)},h.prototype.distanceSquaredTo=function(a){return h.distanceSquaredTo(this,a)},h.prototype.computePlaneDistances=function(a,t,e){return h.computePlaneDistances(this,a,t,e)},h.prototype.isOccluded=function(a){return h.isOccluded(this,a)},h.equals=function(a,t){return a===t||s.defined(a)&&s.defined(t)&&n.Cartesian3.equals(a.center,t.center)&&d.Matrix3.equals(a.halfAxes,t.halfAxes)},h.prototype.clone=function(a){return h.clone(this,a)},h.prototype.equals=function(a){return h.equals(this,a)};var Ca=new r.Cartesian4;h.prototype._updateBBox=function(){if(1==this.areaDirty){var a=d.Matrix3.getColumn(this.halfAxes,0,aa),t=n.Cartesian3.clone(n.Cartesian3.negate(a,y)),e=d.Matrix3.getColumn(this.halfAxes,1,ta),r=n.Cartesian3.clone(n.Cartesian3.negate(e,y)),i=d.Matrix3.getColumn(this.halfAxes,2,ea),s=n.Cartesian3.clone(n.Cartesian3.negate(i,y));this.bbox=[],n.Cartesian3.add(this.center,e,y),n.Cartesian3.add(y,s,v),n.Cartesian3.add(v,t,P),this.bbox[0]=new n.Cartesian3(P.x,P.y,P.z),n.Cartesian3.add(v,a,P),this.bbox[1]=new n.Cartesian3(P.x,P.y,P.z),n.Cartesian3.add(y,i,v),n.Cartesian3.add(v,a,P),this.bbox[2]=new n.Cartesian3(P.x,P.y,P.z),n.Cartesian3.add(v,t,P),this.bbox[3]=new n.Cartesian3(P.x,P.y,P.z),n.Cartesian3.add(this.center,r,y),n.Cartesian3.add(y,s,v),n.Cartesian3.add(v,t,P),this.bbox[4]=new n.Cartesian3(P.x,P.y,P.z),n.Cartesian3.add(v,a,P),this.bbox[5]=new n.Cartesian3(P.x,P.y,P.z),n.Cartesian3.add(y,i,v),n.Cartesian3.add(v,a,P),this.bbox[6]=new n.Cartesian3(P.x,P.y,P.z),n.Cartesian3.add(v,t,P),this.bbox[7]=new n.Cartesian3(P.x,P.y,P.z);var o=n.Cartesian3.magnitude(a),C=n.Cartesian3.magnitude(e),u=n.Cartesian3.magnitude(i),c=new n.Cartesian3(-o,-C,-u),l=new n.Cartesian3(o,C,u);if(o*C*u==0)return void(this.areaDirty=-1);n.Cartesian3.normalize(a,a),n.Cartesian3.normalize(e,e),n.Cartesian3.normalize(i,i),this.u=n.Cartesian3.clone(a),this.v=n.Cartesian3.clone(e),this.w=n.Cartesian3.clone(i),this.posMin=c,this.posMaX=l,this.areaDirty=0}};var da=[];da.push(new e.Cartesian2),da.push(new e.Cartesian2),da.push(new e.Cartesian2),da.push(new e.Cartesian2),da.push(new e.Cartesian2),da.push(new e.Cartesian2);var ua=new n.Cartographic,ca=new n.Cartesian3;h.prototype.calculateBoxArea=function(a,t,e,i,o,u,h,m){this._updateBBox();var x=a,p=n.Cartesian3.subtract(x,this.center,ra);if(-1==this.areaDirty){var M=o/i*(N=-1!=u?u:.5*n.Cartesian3.distance(this.posMaX,this.posMin))/e;return C.CesiumMath.PI*M*M}var f=n.Cartesian3.fromElements(n.Cartesian3.dot(p,this.u),n.Cartesian3.dot(p,this.v),n.Cartesian3.dot(p,this.w),ia),g=(f.x<this.posMin.x?1:0)+((f.x>this.posMaX.x?1:0)<<1)+((f.z<this.posMin.z?1:0)<<2)+((f.z>this.posMaX.z?1:0)<<3)+((f.y>this.posMaX.y?1:0)<<4)+((f.y<this.posMin.y?1:0)<<5);if(g>42)return console.log("area calculation is wrong"),-100;var b=l[g];if(0==b.num){M=o/i*(N=-1!=u?u:.5*n.Cartesian3.distance(this.posMaX,this.posMin))/e;return C.CesiumMath.PI*M*M}if(0==b.num)return console.log("area calculation is wrong"),-100;for(var w,y=[],v=h,P=0;P<b.num;P++){var O,z=da[P],A=this.bbox[b.data[P]];w=!1;var N,R=C.CesiumMath.PI;if(3===t)(O=d.Matrix4.multiplyByVector(v,r.Cartesian4.fromElements(A.x,A.y,A.z,1),Ca)).z<0&&(w=!0,-1==N&&(R=C.CesiumMath.PI_OVER_FOUR,e=n.Cartesian3.magnitude(p)));else{var T=m,I=T.ellipsoid.cartesianToCartographic(A,ua);s.defined(I)?(T.project(I,ca),(O=d.Matrix4.multiplyByVector(v,r.Cartesian4.fromElements(ca.z,ca.x,ca.y,1),Ca)).z<0&&(w=!0)):w=!0}if(1==w)return R*(M=o/i*(N=-1!=u?u:.5*n.Cartesian3.distance(this.posMaX,this.posMin))/e)*M;z.x=O.x/O.w,z.y=o-O.y/O.w,y.push(z)}return Math.abs(c.PolygonPipeline.computeArea2D(y))},a.OrientedBoundingBox=h}));
