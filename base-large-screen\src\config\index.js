import ANIMATIONS from "./gsap";
//应用配置
const APP_CONFIG = {
  layout: "adapt", //显示适配：default 等比缩放、adapt 高度缩放，宽度占满全屏
  pageSize: {
    width: 1920,
    height: 1080,
  },
  focus: false, //聚焦模式：true 开启、false 关闭
  focusScale: 2, //聚焦模式放大比例
};
//地图配置
const MAP_CONFIG = {
  defaultType: "raster", //默认地图类型：raster 影像地图、 vector 电子地图
  provider: "TDT", //地图提供商：TDT SDTDT
  projection: "EPSG:4326", //openlayers投影方式(TDT有效)：EPSG:4326 经纬度、EPSG:3857 伪墨卡托投影
  checkKey: true, //是否需要在系统初始化时校验并选择可用的地图key(在配置多个key时有效)
};
//天地图
const TDT_CONFIG = {
  key: ["e19158b9cbc5bec0671b3fd3146196d1", "65884a3c3eb6fdee894709e06bad8d44"], //可使用数组配置多个
};
//山东天地图
const SDTDT_CONFIG = {
  key: "548c3b49fcc664df52f47a8b38aa1054", //可使用数组配置多个
};
//高德地图
const AMAP_CONFIG = {
  key: "91e02b9d7fd16e8fa5f85be6116fa2f7",
};

export { APP_CONFIG, ANIMATIONS, AMAP_CONFIG, TDT_CONFIG, SDTDT_CONFIG, MAP_CONFIG };
