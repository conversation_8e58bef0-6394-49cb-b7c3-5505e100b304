import { createApp } from "vue";
import pinia from "./stores";

import LayoutBox from "./components/core/LayoutBox.vue";
import DialogView from "./components/core/DialogView.vue";
import { register } from "swiper/element/bundle";

import App from "./App.vue";
import router from "./router";

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

import "./assets/main.css";

register();

const app = createApp(App);

app.use(pinia);
app.use(router);
//ElementPlus
app.use(ElementPlus)

app.component("LayoutBox", LayoutBox);
app.component("UDialog", DialogView);

app.mount("#app");
