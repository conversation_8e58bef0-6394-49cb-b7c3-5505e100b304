define(["./when-b60132fc","./Cartesian2-47311507","./ArcType-29cf2197","./arrayFill-4513d7ad","./BoundingRectangle-1f901ba8","./buildModuleUrl-8958744c","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./EllipsoidGeodesic-0f19ac62","./EllipsoidTangentPlane-ce9a1fbb","./GeometryAttribute-06a41648","./GeometryInstance-6bd4503d","./GeometryOffsetAttribute-fbeb6f1a","./GeometryPipeline-44c6c124","./IndexDatatype-8a5eead4","./Math-119be1a3","./FeatureDetection-806b12f0","./PolygonGeometryLibrary-208ca427","./PolygonPipeline-d328cdf1","./VertexFormat-6446fca0","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian4-3ca25aab","./IntersectionTests-a793ed08","./Plane-a3d8b3d2","./AttributeCompression-90851096","./EncodedCartesian3-f1396b05","./arrayRemoveDuplicates-d2f048c5","./EllipsoidRhumbLine-ed1a6bf4","./GeometryAttributes-252e9929","./earcut-2.2.1-20c8012f"],(function(e,t,o,r,a,i,n,s,l,u,d,p,c,g,m,y,h,f,b,_,v,C,x,P,T,w,A,E,I,G,V,F,H){"use strict";var N=new n.Cartographic,O=new n.Cartographic;function R(e,t,o,r){var a=r.cartesianToCartographic(e,N).height,i=r.cartesianToCartographic(t,O);i.height=a,r.cartographicToCartesian(i,t);var n=r.cartesianToCartographic(o,O);n.height=a-100,r.cartographicToCartesian(n,o)}var D=new a.BoundingRectangle,L=new n.Cartesian3,M=new n.Cartesian3,B=new n.Cartesian3,S=new n.Cartesian3,k=new n.Cartesian3,z=new n.Cartesian3,W=new n.Cartesian3,Y=new n.Cartesian3,U=new n.Cartesian3,j=new t.Cartesian2,Q=new t.Cartesian2,q=new n.Cartesian3,K=new p.Quaternion,Z=new f.Matrix3,J=new f.Matrix3;function X(o){var a=o.vertexFormat,i=o.geometry,s=o.shadowVolume,u=i.attributes.position.values,d=u.length,c=o.wall,m=o.top,y=o.bottom;if(a.st||a.normal||a.tangent||a.bitangent||s){var b=o.boundingRectangle,_=o.tangentPlane,v=o.ellipsoid,C=o.stRotation,x=o.perPositionHeight,P=j;P.x=b.x,P.y=b.y;var T,w=a.st?new Float32Array(d):void 0;a.normal&&(T=x&&m&&!c?i.attributes.normal.values:new Float32Array(d));var A=a.tangent?new Float32Array(d):void 0,E=a.bitangent?new Float32Array(d):void 0,I=s?new Float32Array(d):void 0,G=0,V=0,F=M,H=B,N=S,O=!0,D=Z,X=J;if(0!==C){var $=p.Quaternion.fromAxisAngle(_._plane.normal,C,K);D=f.Matrix3.fromQuaternion($,D),$=p.Quaternion.fromAxisAngle(_._plane.normal,-C,K),X=f.Matrix3.fromQuaternion($,X)}else D=f.Matrix3.clone(f.Matrix3.IDENTITY,D),X=f.Matrix3.clone(f.Matrix3.IDENTITY,X);var ee=0;(m&&y||c)&&(ee=d/2,d/=2);var te=1,oe=[];if(a.st&&c&&o.isComputeTexCoord){let e=[...o.outerPositions,o.outerPositions[0]];for(let t=1;t<e.length;t++){te+=n.Cartesian3.distance(e[t-1],e[t]),oe.push(te)}}for(var re=0;re<d;re+=3){var ae=n.Cartesian3.fromArray(u,re,q);if(a.st){var ie=f.Matrix3.multiplyByVector(D,ae,L);ie=v.scaleToGeodeticSurface(ie,ie);var ne=_.projectPointOntoPlane(ie,Q);t.Cartesian2.subtract(ne,P,ne);var se=h.CesiumMath.clamp(ne.x/b.width,0,1),le=h.CesiumMath.clamp(ne.y/b.height,0,1);if(c&&te>1){let e=Math.ceil(re/6)-1;se=oe[e]?oe[e]/te:0,w[G]=1-se,w[G+1]=1,w[G+2]=0,w[G+ee]=1-se,w[G+1+ee]=0,w[G+2+ee]=0}y&&(w[G+ee]=se,w[G+1+ee]=le,w[G+2+ee]=-1),m&&(w[G]=se,w[G+1]=le,w[G+2]=1),G+=3}if(a.normal||a.tangent||a.bitangent||s){var ue=V+1,de=V+2;if(c){if(re+3<d){var pe=n.Cartesian3.fromArray(u,re+3,k);if(O){var ce=n.Cartesian3.fromArray(u,re+d,z);x&&R(ae,pe,ce,v),n.Cartesian3.subtract(pe,ae,pe),n.Cartesian3.subtract(ce,ae,ce),F=n.Cartesian3.normalize(n.Cartesian3.cross(ce,pe,F),F),O=!1}n.Cartesian3.equalsEpsilon(pe,ae,h.CesiumMath.EPSILON10)&&(O=!0)}(a.tangent||a.bitangent)&&(N=v.geodeticSurfaceNormal(ae,N),a.tangent&&(H=n.Cartesian3.normalize(n.Cartesian3.cross(N,F,H),H)))}else F=v.geodeticSurfaceNormal(ae,F),(a.tangent||a.bitangent)&&(x&&(W=n.Cartesian3.fromArray(T,V,W),Y=n.Cartesian3.cross(n.Cartesian3.UNIT_Z,W,Y),Y=n.Cartesian3.normalize(f.Matrix3.multiplyByVector(X,Y,Y),Y),a.bitangent&&(U=n.Cartesian3.normalize(n.Cartesian3.cross(W,Y,U),U))),H=n.Cartesian3.cross(n.Cartesian3.UNIT_Z,F,H),H=n.Cartesian3.normalize(f.Matrix3.multiplyByVector(X,H,H),H),a.bitangent&&(N=n.Cartesian3.normalize(n.Cartesian3.cross(F,H,N),N)));a.normal&&(o.wall?(T[V+ee]=F.x,T[ue+ee]=F.y,T[de+ee]=F.z):y&&(T[V+ee]=-F.x,T[ue+ee]=-F.y,T[de+ee]=-F.z),(m&&!x||c)&&(T[V]=F.x,T[ue]=F.y,T[de]=F.z)),s&&(c&&(F=v.geodeticSurfaceNormal(ae,F)),I[V+ee]=-F.x,I[ue+ee]=-F.y,I[de+ee]=-F.z),a.tangent&&(o.wall?(A[V+ee]=H.x,A[ue+ee]=H.y,A[de+ee]=H.z):y&&(A[V+ee]=-H.x,A[ue+ee]=-H.y,A[de+ee]=-H.z),m&&(x?(A[V]=Y.x,A[ue]=Y.y,A[de]=Y.z):(A[V]=H.x,A[ue]=H.y,A[de]=H.z))),a.bitangent&&(y&&(E[V+ee]=N.x,E[ue+ee]=N.y,E[de+ee]=N.z),m&&(x?(E[V]=U.x,E[ue]=U.y,E[de]=U.z):(E[V]=N.x,E[ue]=N.y,E[de]=N.z))),V+=3}}a.st&&(i.attributes.st=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:w})),a.normal&&(i.attributes.normal=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:T})),a.tangent&&(i.attributes.tangent=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:A})),a.bitangent&&(i.attributes.bitangent=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:E})),s&&(i.attributes.extrudeDirection=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:I}))}if(o.extrude&&e.defined(o.offsetAttribute)){var ge=u.length/3,me=new Uint8Array(ge);if(o.offsetAttribute===g.GeometryOffsetAttribute.TOP)m&&y||c?me=r.arrayFill(me,1,0,ge/2):m&&(me=r.arrayFill(me,1));else{var ye=o.offsetAttribute===g.GeometryOffsetAttribute.NONE?0:1;me=r.arrayFill(me,ye)}i.attributes.applyOffset=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:me})}return i}var $=new n.Cartographic,ee=new n.Cartographic,te={west:0,east:0},oe=new u.EllipsoidGeodesic;function re(r,a,i,n,s){if(s=e.defaultValue(s,new t.Rectangle),!e.defined(r)||r.length<3)return s.west=0,s.north=0,s.south=0,s.east=0,s;if(i===o.ArcType.RHUMB)return t.Rectangle.fromCartesianArray(r,a,s);oe.ellipsoid.equals(a)||(oe=new u.EllipsoidGeodesic(void 0,void 0,a)),s.west=Number.POSITIVE_INFINITY,s.east=Number.NEGATIVE_INFINITY,s.south=Number.POSITIVE_INFINITY,s.north=Number.NEGATIVE_INFINITY,te.west=Number.POSITIVE_INFINITY,te.east=Number.NEGATIVE_INFINITY;for(var l,d=1/h.CesiumMath.chordLength(n,a.maximumRadius),p=r.length,c=a.cartesianToCartographic(r[0],ee),g=$,m=1;m<p;m++)l=g,g=c,c=a.cartesianToCartographic(r[m],l),oe.setEndPoints(g,c),ie(oe,d,s,te);return l=g,g=c,c=a.cartesianToCartographic(r[0],l),oe.setEndPoints(g,c),ie(oe,d,s,te),s.east-s.west>te.west-te.east&&(s.east=te.east,s.west=te.west),s}var ae=new n.Cartographic;function ie(e,t,o,r){for(var a=e.surfaceDistance,i=Math.ceil(a*t),n=i>0?a/(i-1):Number.POSITIVE_INFINITY,s=0,l=0;l<i;l++){var u=e.interpolateUsingSurfaceDistance(s,ae);s+=n;var d=u.longitude,p=u.latitude;o.west=Math.min(o.west,d),o.east=Math.max(o.east,d),o.south=Math.min(o.south,p),o.north=Math.max(o.north,p),r.west=d>0?Math.min(d,r.west):r.west,r.east=d<0?Math.max(d,r.east):r.east}}var ne=[];function se(e,t,o,r,a,i,n,s,l,u,p){var g,m={walls:[]};if(i||n){var h,f,v=b.PolygonGeometryLibrary.createGeometryFromPositions(e,t,o,a,s,l),C=v.attributes.position.values,x=v.indices;if(i&&n){var P=C.concat(C);h=P.length/3,(f=y.IndexDatatype.createTypedArray(h,2*x.length)).set(x);var T=x.length,w=h/2;for(g=0;g<T;g+=3){var A=f[g]+w,E=f[g+1]+w,I=f[g+2]+w;f[g+T]=I,f[g+1+T]=E,f[g+2+T]=A}if(v.attributes.position.values=P,a&&s.normal){var G=v.attributes.normal.values;v.attributes.normal.values=new Float32Array(P.length),v.attributes.normal.values.set(G)}v.indices=f}else if(n){for(h=C.length/3,f=y.IndexDatatype.createTypedArray(h,x.length),g=0;g<x.length;g+=3)f[g]=x[g+2],f[g+1]=x[g+1],f[g+2]=x[g];v.indices=f}m.topAndBottom=new c.GeometryInstance({geometry:v})}var V,F=r.outerRing,H=d.EllipsoidTangentPlane.fromPoints(F,e),N=H.projectPointsOntoPlane(F,ne),O=_.PolygonPipeline.computeWindingOrder2D(N);O===_.WindingOrder.CLOCKWISE&&(F=F.slice().reverse()),u&&(V=b.PolygonGeometryLibrary.computeWallGeometry(F,e,o,a,l,p),m.walls.push(new c.GeometryInstance({geometry:V})));var R=r.holes;for(g=0;g<R.length;g++){var D=R[g];N=(H=d.EllipsoidTangentPlane.fromPoints(D,e)).projectPointsOntoPlane(D,ne),(O=_.PolygonPipeline.computeWindingOrder2D(N))===_.WindingOrder.COUNTER_CLOCKWISE&&(D=D.slice().reverse()),V=b.PolygonGeometryLibrary.computeWallGeometry(D,e,o,a,l),m.walls.push(new c.GeometryInstance({geometry:V}))}return m}function le(r){var a=r.polygonHierarchy,i=e.defaultValue(r.vertexFormat,v.VertexFormat.DEFAULT),n=e.defaultValue(r.ellipsoid,t.Ellipsoid.WGS84),s=e.defaultValue(r.granularity,h.CesiumMath.RADIANS_PER_DEGREE),l=e.defaultValue(r.stRotation,0),u=e.defaultValue(r.perPositionHeight,!1),d=u&&e.defined(r.extrudedHeight),p=e.defaultValue(r.height,0),c=e.defaultValue(r.extrudedHeight,p);if(!d){var g=Math.max(p,c);c=Math.min(p,c),p=g}this._vertexFormat=v.VertexFormat.clone(i),this._ellipsoid=t.Ellipsoid.clone(n),this._granularity=s,this._stRotation=l,this._height=p,this._extrudedHeight=c,this._closeTop=e.defaultValue(r.closeTop,!0),this._closeBottom=e.defaultValue(r.closeBottom,!0),this._extrudeOutering=e.defaultValue(r.extrudeOutering,!0),this._polygonHierarchy=a,this._perPositionHeight=u,this._perPositionHeightExtrude=d,this._shadowVolume=e.defaultValue(r.shadowVolume,!1),this._workerName="createPolygonGeometry",this._offsetAttribute=r.offsetAttribute,this._arcType=e.defaultValue(r.arcType,o.ArcType.GEODESIC),this._groundBottomAltitude=e.defaultValue(r.groundBottomAltitude,void 0),this._groundExtrudedHeight=e.defaultValue(r.groundExtrudedHeight,0),this._rectangle=void 0,this._textureCoordinateRotationPoints=void 0,this._isComputeTexCoord=r.isComputeTexCoord,this._isWall=e.defaultValue(r.isWall,!1),this.packedLength=b.PolygonGeometryLibrary.computeHierarchyPackedLength(a)+t.Ellipsoid.packedLength+v.VertexFormat.packedLength+12}le.fromPositions=function(t){return new le({polygonHierarchy:{positions:(t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT)).positions},height:t.height,extrudedHeight:t.extrudedHeight,vertexFormat:t.vertexFormat,stRotation:t.stRotation,ellipsoid:t.ellipsoid,granularity:t.granularity,perPositionHeight:t.perPositionHeight,closeTop:t.closeTop,closeBottom:t.closeBottom,offsetAttribute:t.offsetAttribute,arcType:t.arcType,isComputeTexCoord:t.isComputeTexCoord,isWall:t.isWall})},le.pack=function(o,r,a){return a=e.defaultValue(a,0),a=b.PolygonGeometryLibrary.packPolygonHierarchy(o._polygonHierarchy,r,a),t.Ellipsoid.pack(o._ellipsoid,r,a),a+=t.Ellipsoid.packedLength,v.VertexFormat.pack(o._vertexFormat,r,a),a+=v.VertexFormat.packedLength,r[a++]=o._height,r[a++]=o._extrudedHeight,r[a++]=o._granularity,r[a++]=o._stRotation,r[a++]=o._perPositionHeightExtrude?1:0,r[a++]=o._perPositionHeight?1:0,r[a++]=o._closeTop?1:0,r[a++]=o._closeBottom?1:0,r[a++]=o._shadowVolume?1:0,r[a++]=e.defaultValue(o._offsetAttribute,-1),r[a++]=o._arcType,r[a]=o.packedLength,r};var ue=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),de=new v.VertexFormat,pe={polygonHierarchy:{}};return le.unpack=function(o,r,a){r=e.defaultValue(r,0);var i=b.PolygonGeometryLibrary.unpackPolygonHierarchy(o,r);r=i.startingIndex,delete i.startingIndex;var n=t.Ellipsoid.unpack(o,r,ue);r+=t.Ellipsoid.packedLength;var s=v.VertexFormat.unpack(o,r,de);r+=v.VertexFormat.packedLength;var l=o[r++],u=o[r++],d=o[r++],p=o[r++],c=1===o[r++],g=1===o[r++],m=1===o[r++],y=1===o[r++],h=1===o[r++],f=o[r++],_=o[r++],C=o[r];return e.defined(a)||(a=new le(pe)),a._polygonHierarchy=i,a._ellipsoid=t.Ellipsoid.clone(n,a._ellipsoid),a._vertexFormat=v.VertexFormat.clone(s,a._vertexFormat),a._height=l,a._extrudedHeight=u,a._granularity=d,a._stRotation=p,a._perPositionHeightExtrude=c,a._perPositionHeight=g,a._closeTop=m,a._closeBottom=y,a._shadowVolume=h,a._offsetAttribute=-1===f?void 0:f,a._arcType=_,a.packedLength=C,a},le.computeRectangle=function(r,a){var i=e.defaultValue(r.granularity,h.CesiumMath.RADIANS_PER_DEGREE),n=e.defaultValue(r.arcType,o.ArcType.GEODESIC),s=r.polygonHierarchy,l=e.defaultValue(r.ellipsoid,t.Ellipsoid.WGS84);return re(s.positions,l,n,i,a)},le.createGeometry=function(t){var o=t._vertexFormat,a=t._ellipsoid,n=t._granularity,s=t._stRotation,u=t._polygonHierarchy,f=t._perPositionHeight,v=t._closeTop,C=t._closeBottom,x=t._arcType,P=u.positions;if(!(P.length<3)){var T=d.EllipsoidTangentPlane.fromPoints(P,a),w=b.PolygonGeometryLibrary.polygonsFromHierarchy(u,T.projectPointsOntoPlane.bind(T),!f,a),A=w.hierarchy,E=w.polygons;if(0!==A.length){P=A[0].outerRing;var I,G=b.PolygonGeometryLibrary.computeBoundingRectangle(T.plane.normal,T.projectPointOntoPlane.bind(T),P,s,D),V=[],F=t._height,H=t._extrudedHeight,N=t._perPositionHeightExtrude||!h.CesiumMath.equalsEpsilon(F,H,0,h.CesiumMath.EPSILON2),O={perPositionHeight:f,vertexFormat:o,geometry:void 0,tangentPlane:T,boundingRectangle:G,ellipsoid:a,stRotation:s,bottom:!1,top:!0,wall:!1,extrude:!1,arcType:x,outerPositions:P,isComputeTexCoord:t._isComputeTexCoord};if(N)for(O.extrude=!0,O.top=v,O.bottom=C,O.shadowVolume=t._shadowVolume,O.offsetAttribute=t._offsetAttribute,I=0;I<E.length;I++){var R,L=se(a,E[I],n,A[I],f,v,C,o,x,t._extrudeOutering,t._isWall);v&&C?(R=L.topAndBottom,O.geometry=b.PolygonGeometryLibrary.scaleToGeodeticHeightExtruded(R.geometry,F,H,a,f)):v?((R=L.topAndBottom).geometry.attributes.position.values=_.PolygonPipeline.scaleToGeodeticHeight(R.geometry.attributes.position.values,F,a,!f),O.geometry=R.geometry):C&&((R=L.topAndBottom).geometry.attributes.position.values=_.PolygonPipeline.scaleToGeodeticHeight(R.geometry.attributes.position.values,H,a,!0),O.geometry=R.geometry),(v||C)&&(O.wall=!1,R.geometry=X(O),V.push(R));var M=L.walls;O.wall=!0;for(var B=0;B<M.length;B++){var S=M[B];O.top=!1,O.bottom=!1,O.geometry=b.PolygonGeometryLibrary.scaleToGeodeticHeightExtruded(S.geometry,F,H,a,f),S.geometry=X(O),V.push(S)}}else for(I=0;I<E.length;I++){var k=new c.GeometryInstance({geometry:b.PolygonGeometryLibrary.createGeometryFromPositions(a,E[I],n,f,o,x)});if(k.geometry.attributes.position.values=_.PolygonPipeline.scaleToGeodeticHeight(k.geometry.attributes.position.values,F,a,!f),O.geometry=k.geometry,k.geometry=X(O),e.defined(t._offsetAttribute)){var z=k.geometry.attributes.position.values.length,W=new Uint8Array(z/3),Y=t._offsetAttribute===g.GeometryOffsetAttribute.NONE?0:1;r.arrayFill(W,Y),k.geometry.attributes.applyOffset=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:W})}V.push(k)}var U=m.GeometryPipeline.combineInstances(V)[0];U.attributes.position.values=new Float64Array(U.attributes.position.values),U.indices=y.IndexDatatype.createTypedArray(U.attributes.position.values.length/3,U.indices);var j=U.attributes,Q=i.BoundingSphere.fromVertices(j.position.values);return o.position||delete j.position,new p.Geometry({attributes:j,indices:U.indices,primitiveType:U.primitiveType,boundingSphere:Q,offsetAttribute:t._offsetAttribute})}}},le.createShadowVolume=function(e,t,o){var r=e._granularity,a=e._ellipsoid,i=e._groundBottomAltitude+e._groundExtrudedHeight,n=e._groundBottomAltitude?e._groundBottomAltitude:t(r,a),s=i||o(r,a);return new le({polygonHierarchy:e._polygonHierarchy,ellipsoid:a,stRotation:e._stRotation,granularity:r,perPositionHeight:!1,extrudedHeight:n,height:s,vertexFormat:v.VertexFormat.POSITION_ONLY,shadowVolume:!0,arcType:e._arcType})},Object.defineProperties(le.prototype,{rectangle:{get:function(){if(!e.defined(this._rectangle)){var t=this._polygonHierarchy.positions;this._rectangle=re(t,this._ellipsoid,this._arcType,this._granularity)}return this._rectangle}},textureCoordinateRotationPoints:{get:function(){return e.defined(this._textureCoordinateRotationPoints)||(this._textureCoordinateRotationPoints=function(e){var t=-e._stRotation;if(0===t)return[0,0,0,1,1,0];var o=e._ellipsoid,r=e._polygonHierarchy.positions,a=e.rectangle;return p.Geometry._textureCoordinateRotationPoints(r,t,o,a)}(this)),this._textureCoordinateRotationPoints}}}),function(o,r){return e.defined(r)&&(o=le.unpack(o,r)),o._ellipsoid=t.Ellipsoid.clone(o._ellipsoid),le.createGeometry(o)}}));
