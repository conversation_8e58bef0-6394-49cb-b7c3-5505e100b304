//This file is automatically rebuilt by the Cesium build process.
export default "/**\n\
 * A built-in GLSL floating-point constant for <code>2pi</code>.\n\
 *\n\
 * @alias czm_twoPi\n\
 * @glslConstant\n\
 *\n\
 * @see CesiumMath.TWO_PI\n\
 *\n\
 * @example\n\
 * // GLSL declaration\n\
 * const float czm_twoPi = ...;\n\
 *\n\
 * // Example\n\
 * float pi = czm_twoPi / 2.0;\n\
 */\n\
const float czm_twoPi = 6.283185307179586;\n\
";
