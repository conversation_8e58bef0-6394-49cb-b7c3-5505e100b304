export default {
  url: "/api/village",
  method: "get",
  response: () => {
    return {
      environmentCase: {
        total: 298,
        complete: "@integer(100, 298)",
      },
      environmentActivity: [
        {
          name: "春秋冬战役活动",
          poster: "./mock/spot1.png",
        },
        {
          name: "三类村庄活动",
          poster: "./mock/spot2.png",
        },
        {
          name: "户外捡垃圾活动",
          poster: "./mock/spot1.png",
        },
        {
          name: "走进大自然活动",
          poster: "./mock/spot2.png",
        },
        {
          name: "走进乡村活动",
          poster: "./mock/spot1.png",
        },
        {
          name: "自由采摘活动",
          poster: "./mock/spot2.png",
        },
        {
          name: "自驾游活动",
          poster: "./mock/spot1.png",
        },
      ],
      tourism: {
        poster: "./mock/poster1.jpeg",
        name: "风车山",
        introduce:
          "3月21日，姜疃零碳智慧物流园启动仪式暨，姜疃零碳智慧物流园启动仪式暨，姜疃零碳智慧物流园启动仪式暨，姜疃零碳智慧物流园启动仪式暨，姜疃零碳智慧物流园启动仪式暨，姜疃零碳智慧物流园启动仪式暨，姜疃零碳智慧物流园启动仪式暨，姜疃零碳智慧物流园启动仪式暨，姜疃零碳智慧物流园启动仪式暨，姜疃零碳智慧物流园启动仪式暨企业可持续发展高端对话在本市莱阳市的城际物流中心举行。记者获悉物流姜疃零碳智慧物流园启动仪式暨企业可持续发展高端对话在本市莱阳市的城际物流中心举行。姜疃零碳智慧物流园启动仪式暨企业可持续发展高端对话在本市莱阳市的城际物流中心举行。...",
      },
    };
  },
};
