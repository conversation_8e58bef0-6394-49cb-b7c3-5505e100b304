//This file is automatically rebuilt by the Cesium build process.
export default "/**\n\
 * Parameters for {@link czm_pbrLighting}\n\
 *\n\
 * @name czm_material\n\
 * @glslStruct\n\
 *\n\
 * @property {vec3} diffuseColor the diffuse color of the material for the lambert term of the rendering equation\n\
 * @property {float} roughness a value from 0.0 to 1.0 that indicates how rough the surface of the material is.\n\
 * @property {vec3} f0 The reflectance of the material at normal incidence\n\
 */\n\
struct czm_pbrParameters\n\
{\n\
    vec3 diffuseColor;\n\
    float roughness;\n\
    vec3 f0;\n\
};\n\
";
