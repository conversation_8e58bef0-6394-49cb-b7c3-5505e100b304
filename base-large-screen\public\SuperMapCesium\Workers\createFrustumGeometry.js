define(["./when-b60132fc","./FrustumGeometry-d53e50fb","./buildModuleUrl-8958744c","./Cartographic-3309dd0d","./Check-7b2a090c","./Math-119be1a3","./Cartesian2-47311507","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./Plane-a3d8b3d2","./VertexFormat-6446fca0"],(function(e,t,r,a,n,u,o,c,d,b,i,m,f,s,y,G,C){"use strict";return function(r,a){return e.defined(a)&&(r=t.FrustumGeometry.unpack(r,a)),t.FrustumGeometry.createGeometry(r)}}));
