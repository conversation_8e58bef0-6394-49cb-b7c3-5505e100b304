define(["./arrayFill-4513d7ad","./buildModuleUrl-8958744c","./Cartesian2-47311507","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./CylinderGeometryLibrary-aa453214","./when-b60132fc","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4","./FeatureDetection-806b12f0","./Math-119be1a3","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian4-3ca25aab"],(function(t,e,a,i,r,n,o,u,s,f,d,l,b,c,m,p,y,_){"use strict";var h=new a.Cartesian2;function v(t){var e=(t=u.defaultValue(t,u.defaultValue.EMPTY_OBJECT)).length,a=t.topRadius,i=t.bottomRadius,r=u.defaultValue(t.slices,128),n=Math.max(u.defaultValue(t.numberOfVerticalLines,16),0);this._length=e,this._topRadius=a,this._bottomRadius=i,this._slices=r,this._numberOfVerticalLines=n,this._offsetAttribute=t.offsetAttribute,this._workerName="createCylinderOutlineGeometry"}v.packedLength=6,v.pack=function(t,e,a){return a=u.defaultValue(a,0),e[a++]=t._length,e[a++]=t._topRadius,e[a++]=t._bottomRadius,e[a++]=t._slices,e[a++]=t._numberOfVerticalLines,e[a]=u.defaultValue(t._offsetAttribute,-1),e};var A={length:void 0,topRadius:void 0,bottomRadius:void 0,slices:void 0,numberOfVerticalLines:void 0,offsetAttribute:void 0};return v.unpack=function(t,e,a){e=u.defaultValue(e,0);var i=t[e++],r=t[e++],n=t[e++],o=t[e++],s=t[e++],f=t[e];return u.defined(a)?(a._length=i,a._topRadius=r,a._bottomRadius=n,a._slices=o,a._numberOfVerticalLines=s,a._offsetAttribute=-1===f?void 0:f,a):(A.length=i,A.topRadius=r,A.bottomRadius=n,A.slices=o,A.numberOfVerticalLines=s,A.offsetAttribute=-1===f?void 0:f,new v(A))},v.createGeometry=function(r){var c=r._length,m=r._topRadius,p=r._bottomRadius,y=r._slices,_=r._numberOfVerticalLines;if(!(c<=0||m<0||p<0||0===m&&0===p)){var v,A=2*y,R=o.CylinderGeometryLibrary.computePositions(c,m,p,y,!1),C=2*y;if(_>0){var G=Math.min(_,y);v=Math.round(y/G),C+=G}var O,V=l.IndexDatatype.createTypedArray(A,2*C),g=0;for(O=0;O<y-1;O++)V[g++]=O,V[g++]=O+1,V[g++]=O+y,V[g++]=O+1+y;if(V[g++]=y-1,V[g++]=0,V[g++]=y+y-1,V[g++]=y,_>0)for(O=0;O<y;O+=v)V[g++]=O,V[g++]=O+y;var L=new f.GeometryAttributes;L.position=new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:R}),h.x=.5*c,h.y=Math.max(p,m);var w=new e.BoundingSphere(i.Cartesian3.ZERO,a.Cartesian2.magnitude(h));if(u.defined(r._offsetAttribute)){c=R.length;var D=new Uint8Array(c/3),E=r._offsetAttribute===d.GeometryOffsetAttribute.NONE?0:1;t.arrayFill(D,E),L.applyOffset=new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:D})}return new s.Geometry({attributes:L,indices:V,primitiveType:b.PrimitiveType.LINES,boundingSphere:w,offsetAttribute:r._offsetAttribute})}},function(t,e){return u.defined(e)&&(t=v.unpack(t,e)),v.createGeometry(t)}}));
