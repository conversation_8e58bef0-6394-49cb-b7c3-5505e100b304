{"version": 3, "sources": ["../../ol/structs/RBush.js", "../../ol/source/VectorEventType.js", "../../ol/loadingstrategy.js", "../../ol/featureloader.js", "../../ol/source/Vector.js"], "sourcesContent": ["/**\n * @module ol/structs/RBush\n */\nimport RBush_ from 'rbush';\nimport {createOrUpdate, equals} from '../extent.js';\nimport {getUid} from '../util.js';\nimport {isEmpty} from '../obj.js';\n\n/**\n * @typedef {Object} Entry\n * @property {number} minX MinX.\n * @property {number} minY MinY.\n * @property {number} maxX MaxX.\n * @property {number} maxY MaxY.\n * @property {Object} [value] Value.\n */\n\n/**\n * @classdesc\n * Wrapper around the RBush by Vladimir Agafonkin.\n * See https://github.com/mourner/rbush.\n *\n * @template T\n */\nclass RBush {\n  /**\n   * @param {number} [maxEntries] Max entries.\n   */\n  constructor(maxEntries) {\n    /**\n     * @private\n     */\n    this.rbush_ = new RBush_(maxEntries);\n\n    /**\n     * A mapping between the objects added to this rbush wrapper\n     * and the objects that are actually added to the internal rbush.\n     * @private\n     * @type {Object<string, Entry>}\n     */\n    this.items_ = {};\n  }\n\n  /**\n   * Insert a value into the RBush.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {T} value Value.\n   */\n  insert(extent, value) {\n    /** @type {Entry} */\n    const item = {\n      minX: extent[0],\n      minY: extent[1],\n      maxX: extent[2],\n      maxY: extent[3],\n      value: value,\n    };\n\n    this.rbush_.insert(item);\n    this.items_[getUid(value)] = item;\n  }\n\n  /**\n   * Bulk-insert values into the RBush.\n   * @param {Array<import(\"../extent.js\").Extent>} extents Extents.\n   * @param {Array<T>} values Values.\n   */\n  load(extents, values) {\n    const items = new Array(values.length);\n    for (let i = 0, l = values.length; i < l; i++) {\n      const extent = extents[i];\n      const value = values[i];\n\n      /** @type {Entry} */\n      const item = {\n        minX: extent[0],\n        minY: extent[1],\n        maxX: extent[2],\n        maxY: extent[3],\n        value: value,\n      };\n      items[i] = item;\n      this.items_[getUid(value)] = item;\n    }\n    this.rbush_.load(items);\n  }\n\n  /**\n   * Remove a value from the RBush.\n   * @param {T} value Value.\n   * @return {boolean} Removed.\n   */\n  remove(value) {\n    const uid = getUid(value);\n\n    // get the object in which the value was wrapped when adding to the\n    // internal rbush. then use that object to do the removal.\n    const item = this.items_[uid];\n    delete this.items_[uid];\n    return this.rbush_.remove(item) !== null;\n  }\n\n  /**\n   * Update the extent of a value in the RBush.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {T} value Value.\n   */\n  update(extent, value) {\n    const item = this.items_[getUid(value)];\n    const bbox = [item.minX, item.minY, item.maxX, item.maxY];\n    if (!equals(bbox, extent)) {\n      this.remove(value);\n      this.insert(extent, value);\n    }\n  }\n\n  /**\n   * Return all values in the RBush.\n   * @return {Array<T>} All.\n   */\n  getAll() {\n    const items = this.rbush_.all();\n    return items.map(function (item) {\n      return item.value;\n    });\n  }\n\n  /**\n   * Return all values in the given extent.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {Array<T>} All in extent.\n   */\n  getInExtent(extent) {\n    /** @type {Entry} */\n    const bbox = {\n      minX: extent[0],\n      minY: extent[1],\n      maxX: extent[2],\n      maxY: extent[3],\n    };\n    const items = this.rbush_.search(bbox);\n    return items.map(function (item) {\n      return item.value;\n    });\n  }\n\n  /**\n   * Calls a callback function with each value in the tree.\n   * If the callback returns a truthy value, this value is returned without\n   * checking the rest of the tree.\n   * @param {function(T): *} callback Callback.\n   * @return {*} Callback return value.\n   */\n  forEach(callback) {\n    return this.forEach_(this.getAll(), callback);\n  }\n\n  /**\n   * Calls a callback function with each value in the provided extent.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {function(T): *} callback Callback.\n   * @return {*} Callback return value.\n   */\n  forEachInExtent(extent, callback) {\n    return this.forEach_(this.getInExtent(extent), callback);\n  }\n\n  /**\n   * @param {Array<T>} values Values.\n   * @param {function(T): *} callback Callback.\n   * @private\n   * @return {*} Callback return value.\n   */\n  forEach_(values, callback) {\n    let result;\n    for (let i = 0, l = values.length; i < l; i++) {\n      result = callback(values[i]);\n      if (result) {\n        return result;\n      }\n    }\n    return result;\n  }\n\n  /**\n   * @return {boolean} Is empty.\n   */\n  isEmpty() {\n    return isEmpty(this.items_);\n  }\n\n  /**\n   * Remove all values from the RBush.\n   */\n  clear() {\n    this.rbush_.clear();\n    this.items_ = {};\n  }\n\n  /**\n   * @param {import(\"../extent.js\").Extent} [extent] Extent.\n   * @return {import(\"../extent.js\").Extent} Extent.\n   */\n  getExtent(extent) {\n    const data = this.rbush_.toJSON();\n    return createOrUpdate(data.minX, data.minY, data.maxX, data.maxY, extent);\n  }\n\n  /**\n   * @param {RBush} rbush R-Tree.\n   */\n  concat(rbush) {\n    this.rbush_.load(rbush.rbush_.all());\n    for (const i in rbush.items_) {\n      this.items_[i] = rbush.items_[i];\n    }\n  }\n}\n\nexport default RBush;\n", "/**\n * @module ol/source/VectorEventType\n */\n\n/**\n * @enum {string}\n */\nexport default {\n  /**\n   * Triggered when a feature is added to the source.\n   * @event module:ol/source/Vector.VectorSourceEvent#addfeature\n   * @api\n   */\n  ADDFEATURE: 'addfeature',\n\n  /**\n   * Triggered when a feature is updated.\n   * @event module:ol/source/Vector.VectorSourceEvent#changefeature\n   * @api\n   */\n  CHANGEFEATURE: 'changefeature',\n\n  /**\n   * Triggered when the clear method is called on the source.\n   * @event module:ol/source/Vector.VectorSourceEvent#clear\n   * @api\n   */\n  CLEAR: 'clear',\n\n  /**\n   * Triggered when a feature is removed from the source.\n   * See {@link module:ol/source/Vector~VectorSource#clear source.clear()} for exceptions.\n   * @event module:ol/source/Vector.VectorSourceEvent#removefeature\n   * @api\n   */\n  REMOVEFEATURE: 'removefeature',\n\n  /**\n   * Triggered when features starts loading.\n   * @event module:ol/source/Vector.VectorSourceEvent#featuresloadstart\n   * @api\n   */\n  FEATURESLOADSTART: 'featuresloadstart',\n\n  /**\n   * Triggered when features finishes loading.\n   * @event module:ol/source/Vector.VectorSourceEvent#featuresloadend\n   * @api\n   */\n  FEATURESLOADEND: 'featuresloadend',\n\n  /**\n   * Triggered if feature loading results in an error.\n   * @event module:ol/source/Vector.VectorSourceEvent#featuresloaderror\n   * @api\n   */\n  FEATURESLOADERROR: 'featuresloaderror',\n};\n\n/**\n * @typedef {'addfeature'|'changefeature'|'clear'|'removefeature'|'featuresloadstart'|'featuresloadend'|'featuresloaderror'} VectorSourceEventTypes\n */\n", "/**\n * @module ol/loadingstrategy\n */\n\nimport {fromUserExtent, fromUserResolution, toUserExtent} from './proj.js';\n\n/**\n * Strategy function for loading all features with a single request.\n * @param {import(\"./extent.js\").Extent} extent Extent.\n * @param {number} resolution Resolution.\n * @return {Array<import(\"./extent.js\").Extent>} Extents.\n * @api\n */\nexport function all(extent, resolution) {\n  return [[-Infinity, -Infinity, Infinity, Infinity]];\n}\n\n/**\n * Strategy function for loading features based on the view's extent and\n * resolution.\n * @param {import(\"./extent.js\").Extent} extent Extent.\n * @param {number} resolution Resolution.\n * @return {Array<import(\"./extent.js\").Extent>} Extents.\n * @api\n */\nexport function bbox(extent, resolution) {\n  return [extent];\n}\n\n/**\n * Creates a strategy function for loading features based on a tile grid.\n * @param {import(\"./tilegrid/TileGrid.js\").default} tileGrid Tile grid.\n * @return {function(import(\"./extent.js\").Extent, number, import(\"./proj.js\").Projection): Array<import(\"./extent.js\").Extent>} Loading strategy.\n * @api\n */\nexport function tile(tileGrid) {\n  return (\n    /**\n     * @param {import(\"./extent.js\").Extent} extent Extent.\n     * @param {number} resolution Resolution.\n     * @param {import(\"./proj.js\").Projection} projection Projection.\n     * @return {Array<import(\"./extent.js\").Extent>} Extents.\n     */\n    function (extent, resolution, projection) {\n      const z = tileGrid.getZForResolution(\n        fromUserResolution(resolution, projection)\n      );\n      const tileRange = tileGrid.getTileRangeForExtentAndZ(\n        fromUserExtent(extent, projection),\n        z\n      );\n      /** @type {Array<import(\"./extent.js\").Extent>} */\n      const extents = [];\n      /** @type {import(\"./tilecoord.js\").TileCoord} */\n      const tileCoord = [z, 0, 0];\n      for (\n        tileCoord[1] = tileRange.minX;\n        tileCoord[1] <= tileRange.maxX;\n        ++tileCoord[1]\n      ) {\n        for (\n          tileCoord[2] = tileRange.minY;\n          tileCoord[2] <= tileRange.maxY;\n          ++tileCoord[2]\n        ) {\n          extents.push(\n            toUserExtent(tileGrid.getTileCoordExtent(tileCoord), projection)\n          );\n        }\n      }\n      return extents;\n    }\n  );\n}\n", "/**\n * @module ol/featureloader\n */\nimport {VOID} from './functions.js';\n\n/**\n *\n * @type {boolean}\n * @private\n */\nlet withCredentials = false;\n\n/**\n * {@link module:ol/source/Vector~VectorSource} sources use a function of this type to\n * load features.\n *\n * This function takes up to 5 arguments. These are an {@link module:ol/extent~Extent} representing\n * the area to be loaded, a `{number}` representing the resolution (map units per pixel), an\n * {@link module:ol/proj/Projection~Projection} for the projection, an optional success callback that should get\n * the loaded features passed as an argument and an optional failure callback with no arguments. If\n * the callbacks are not used, the corresponding vector source will not fire `'featuresloadend'` and\n * `'featuresloaderror'` events. `this` within the function is bound to the\n * {@link module:ol/source/Vector~VectorSource} it's called from.\n *\n * The function is responsible for loading the features and adding them to the\n * source.\n * @typedef {function(this:(import(\"./source/Vector\").default|import(\"./VectorTile.js\").default),\n *           import(\"./extent.js\").Extent,\n *           number,\n *           import(\"./proj/Projection.js\").default,\n *           function(Array<import(\"./Feature.js\").default>): void=,\n *           function(): void=): void} FeatureLoader\n * @api\n */\n\n/**\n * {@link module:ol/source/Vector~VectorSource} sources use a function of this type to\n * get the url to load features from.\n *\n * This function takes an {@link module:ol/extent~Extent} representing the area\n * to be loaded, a `{number}` representing the resolution (map units per pixel)\n * and an {@link module:ol/proj/Projection~Projection} for the projection  as\n * arguments and returns a `{string}` representing the URL.\n * @typedef {function(import(\"./extent.js\").Extent, number, import(\"./proj/Projection.js\").default): string} FeatureUrlFunction\n * @api\n */\n\n/**\n * @param {string|FeatureUrlFunction} url Feature URL service.\n * @param {import(\"./format/Feature.js\").default} format Feature format.\n * @param {import(\"./extent.js\").Extent} extent Extent.\n * @param {number} resolution Resolution.\n * @param {import(\"./proj/Projection.js\").default} projection Projection.\n * @param {function(Array<import(\"./Feature.js\").default>, import(\"./proj/Projection.js\").default): void} success Success\n *      Function called with the loaded features and optionally with the data projection.\n * @param {function(): void} failure Failure\n *      Function called when loading failed.\n */\nexport function loadFeaturesXhr(\n  url,\n  format,\n  extent,\n  resolution,\n  projection,\n  success,\n  failure\n) {\n  const xhr = new XMLHttpRequest();\n  xhr.open(\n    'GET',\n    typeof url === 'function' ? url(extent, resolution, projection) : url,\n    true\n  );\n  if (format.getType() == 'arraybuffer') {\n    xhr.responseType = 'arraybuffer';\n  }\n  xhr.withCredentials = withCredentials;\n  /**\n   * @param {Event} event Event.\n   * @private\n   */\n  xhr.onload = function (event) {\n    // status will be 0 for file:// urls\n    if (!xhr.status || (xhr.status >= 200 && xhr.status < 300)) {\n      const type = format.getType();\n      /** @type {Document|Node|Object|string|undefined} */\n      let source;\n      if (type == 'json' || type == 'text') {\n        source = xhr.responseText;\n      } else if (type == 'xml') {\n        source = xhr.responseXML;\n        if (!source) {\n          source = new DOMParser().parseFromString(\n            xhr.responseText,\n            'application/xml'\n          );\n        }\n      } else if (type == 'arraybuffer') {\n        source = /** @type {ArrayBuffer} */ (xhr.response);\n      }\n      if (source) {\n        success(\n          /** @type {Array<import(\"./Feature.js\").default>} */\n          (\n            format.readFeatures(source, {\n              extent: extent,\n              featureProjection: projection,\n            })\n          ),\n          format.readProjection(source)\n        );\n      } else {\n        failure();\n      }\n    } else {\n      failure();\n    }\n  };\n  /**\n   * @private\n   */\n  xhr.onerror = failure;\n  xhr.send();\n}\n\n/**\n * Create an XHR feature loader for a `url` and `format`. The feature loader\n * loads features (with XHR), parses the features, and adds them to the\n * vector source.\n * @param {string|FeatureUrlFunction} url Feature URL service.\n * @param {import(\"./format/Feature.js\").default} format Feature format.\n * @return {FeatureLoader} The feature loader.\n * @api\n */\nexport function xhr(url, format) {\n  /**\n   * @param {import(\"./extent.js\").Extent} extent Extent.\n   * @param {number} resolution Resolution.\n   * @param {import(\"./proj/Projection.js\").default} projection Projection.\n   * @param {function(Array<import(\"./Feature.js\").default>): void} [success] Success\n   *      Function called when loading succeeded.\n   * @param {function(): void} [failure] Failure\n   *      Function called when loading failed.\n   */\n  return function (extent, resolution, projection, success, failure) {\n    const source = /** @type {import(\"./source/Vector\").default} */ (this);\n    loadFeaturesXhr(\n      url,\n      format,\n      extent,\n      resolution,\n      projection,\n      /**\n       * @param {Array<import(\"./Feature.js\").default>} features The loaded features.\n       * @param {import(\"./proj/Projection.js\").default} dataProjection Data\n       * projection.\n       */\n      function (features, dataProjection) {\n        source.addFeatures(features);\n        if (success !== undefined) {\n          success(features);\n        }\n      },\n      /* FIXME handle error */ failure ? failure : VOID\n    );\n  };\n}\n\n/**\n * Setter for the withCredentials configuration for the XHR.\n *\n * @param {boolean} xhrWithCredentials The value of withCredentials to set.\n * Compare https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/\n * @api\n */\nexport function setWithCredentials(xhrWithCredentials) {\n  withCredentials = xhrWithCredentials;\n}\n", "/**\n * @module ol/source/Vector\n */\n\nimport Collection from '../Collection.js';\nimport CollectionEventType from '../CollectionEventType.js';\nimport Event from '../events/Event.js';\nimport EventType from '../events/EventType.js';\nimport ObjectEventType from '../ObjectEventType.js';\nimport RBush from '../structs/RBush.js';\nimport Source from './Source.js';\nimport VectorEventType from './VectorEventType.js';\nimport {TRUE, VOID} from '../functions.js';\nimport {all as allStrategy} from '../loadingstrategy.js';\nimport {assert} from '../asserts.js';\nimport {containsExtent, equals, wrapAndSliceX} from '../extent.js';\nimport {extend} from '../array.js';\nimport {getUid} from '../util.js';\nimport {isEmpty} from '../obj.js';\nimport {listen, unlistenBy<PERSON>ey} from '../events.js';\nimport {xhr} from '../featureloader.js';\n\n/**\n * A function that takes an {@link module:ol/extent~Extent} and a resolution as arguments, and\n * returns an array of {@link module:ol/extent~Extent} with the extents to load. Usually this\n * is one of the standard {@link module:ol/loadingstrategy} strategies.\n *\n * @typedef {function(import(\"../extent.js\").Extent, number, import(\"../proj/Projection.js\").default): Array<import(\"../extent.js\").Extent>} LoadingStrategy\n * @api\n */\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/source/Vector~VectorSource} instances are instances of this\n * type.\n * @template {import(\"../geom/Geometry.js\").default} [Geometry=import(\"../geom/Geometry.js\").default]\n */\nexport class VectorSourceEvent extends Event {\n  /**\n   * @param {string} type Type.\n   * @param {import(\"../Feature.js\").default<Geometry>} [feature] Feature.\n   * @param {Array<import(\"../Feature.js\").default<Geometry>>} [features] Features.\n   */\n  constructor(type, feature, features) {\n    super(type);\n\n    /**\n     * The added or removed feature for the `ADDFEATURE` and `REMOVEFEATURE` events, `undefined` otherwise.\n     * @type {import(\"../Feature.js\").default<Geometry>|undefined}\n     * @api\n     */\n    this.feature = feature;\n\n    /**\n     * The loaded features for the `FEATURESLOADED` event, `undefined` otherwise.\n     * @type {Array<import(\"../Feature.js\").default<Geometry>>|undefined}\n     * @api\n     */\n    this.features = features;\n  }\n}\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types, import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").OnSignature<import(\"./VectorEventType\").VectorSourceEventTypes, VectorSourceEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"../ObjectEventType\").Types|\n *     import(\"./VectorEventType\").VectorSourceEventTypes, Return>} VectorSourceOnSignature\n */\n\n/**\n * @typedef {Object} Options\n * @property {import(\"./Source.js\").AttributionLike} [attributions] Attributions.\n * @property {Array<import(\"../Feature.js\").default<Geometry>>|Collection<import(\"../Feature.js\").default<Geometry>>} [features]\n * Features. If provided as {@link module:ol/Collection~Collection}, the features in the source\n * and the collection will stay in sync.\n * @property {import(\"../format/Feature.js\").default} [format] The feature format used by the XHR\n * feature loader when `url` is set. Required if `url` is set, otherwise ignored.\n * @property {import(\"../featureloader.js\").FeatureLoader} [loader]\n * The loader function used to load features, from a remote source for example.\n * If this is not set and `url` is set, the source will create and use an XHR\n * feature loader. The `'featuresloadend'` and `'featuresloaderror'` events\n * will only fire if the `success` and `failure` callbacks are used.\n *\n * Example:\n *\n * ```js\n * import Vector from 'ol/source/Vector.js';\n * import GeoJSON from 'ol/format/GeoJSON.js';\n * import {bbox} from 'ol/loadingstrategy.js';\n *\n * const vectorSource = new Vector({\n *   format: new GeoJSON(),\n *   loader: function(extent, resolution, projection, success, failure) {\n *      const proj = projection.getCode();\n *      const url = 'https://ahocevar.com/geoserver/wfs?service=WFS&' +\n *          'version=1.1.0&request=GetFeature&typename=osm:water_areas&' +\n *          'outputFormat=application/json&srsname=' + proj + '&' +\n *          'bbox=' + extent.join(',') + ',' + proj;\n *      const xhr = new XMLHttpRequest();\n *      xhr.open('GET', url);\n *      const onError = function() {\n *        vectorSource.removeLoadedExtent(extent);\n *        failure();\n *      }\n *      xhr.onerror = onError;\n *      xhr.onload = function() {\n *        if (xhr.status == 200) {\n *          const features = vectorSource.getFormat().readFeatures(xhr.responseText);\n *          vectorSource.addFeatures(features);\n *          success(features);\n *        } else {\n *          onError();\n *        }\n *      }\n *      xhr.send();\n *    },\n *    strategy: bbox,\n *  });\n * ```\n * @property {boolean} [overlaps=true] This source may have overlapping geometries.\n * Setting this to `false` (e.g. for sources with polygons that represent administrative\n * boundaries or TopoJSON sources) allows the renderer to optimise fill and\n * stroke operations.\n * @property {LoadingStrategy} [strategy] The loading strategy to use.\n * By default an {@link module:ol/loadingstrategy.all}\n * strategy is used, a one-off strategy which loads all features at once.\n * @property {string|import(\"../featureloader.js\").FeatureUrlFunction} [url]\n * Setting this option instructs the source to load features using an XHR loader\n * (see {@link module:ol/featureloader.xhr}). Use a `string` and an\n * {@link module:ol/loadingstrategy.all} for a one-off download of all features from\n * the given URL. Use a {@link module:ol/featureloader~FeatureUrlFunction} to generate the url with\n * other loading strategies.\n * Requires `format` to be set as well.\n * When default XHR feature loader is provided, the features will\n * be transformed from the data projection to the view projection\n * during parsing. If your remote data source does not advertise its projection\n * properly, this transformation will be incorrect. For some formats, the\n * default projection (usually EPSG:4326) can be overridden by setting the\n * dataProjection constructor option on the format.\n * Note that if a source contains non-feature data, such as a GeoJSON geometry\n * or a KML NetworkLink, these will be ignored. Use a custom loader to load these.\n * @property {boolean} [useSpatialIndex=true]\n * By default, an RTree is used as spatial index. When features are removed and\n * added frequently, and the total number of features is low, setting this to\n * `false` may improve performance.\n *\n * Note that\n * {@link module:ol/source/Vector~VectorSource#getFeaturesInExtent},\n * {@link module:ol/source/Vector~VectorSource#getClosestFeatureToCoordinate} and\n * {@link module:ol/source/Vector~VectorSource#getExtent} cannot be used when `useSpatialIndex` is\n * set to `false`, and {@link module:ol/source/Vector~VectorSource#forEachFeatureInExtent} will loop\n * through all features.\n *\n * When set to `false`, the features will be maintained in an\n * {@link module:ol/Collection~Collection}, which can be retrieved through\n * {@link module:ol/source/Vector~VectorSource#getFeaturesCollection}.\n * @property {boolean} [wrapX=true] Wrap the world horizontally. For vector editing across the\n * -180° and 180° meridians to work properly, this should be set to `false`. The\n * resulting geometry coordinates will then exceed the world bounds.\n * @template {import(\"../geom/Geometry.js\").default} [Geometry=import(\"../geom/Geometry.js\").default]\n */\n\n/**\n * @classdesc\n * Provides a source of features for vector layers. Vector features provided\n * by this source are suitable for editing. See {@link module:ol/source/VectorTile~VectorTile} for\n * vector data that is optimized for rendering.\n *\n * @fires VectorSourceEvent\n * @api\n * @template {import(\"../geom/Geometry.js\").default} [Geometry=import(\"../geom/Geometry.js\").default]\n */\nclass VectorSource extends Source {\n  /**\n   * @param {Options<Geometry>} [options] Vector source options.\n   */\n  constructor(options) {\n    options = options || {};\n\n    super({\n      attributions: options.attributions,\n      interpolate: true,\n      projection: undefined,\n      state: 'ready',\n      wrapX: options.wrapX !== undefined ? options.wrapX : true,\n    });\n\n    /***\n     * @type {VectorSourceOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {VectorSourceOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {VectorSourceOnSignature<void>}\n     */\n    this.un;\n\n    /**\n     * @private\n     * @type {import(\"../featureloader.js\").FeatureLoader}\n     */\n    this.loader_ = VOID;\n\n    /**\n     * @private\n     * @type {import(\"../format/Feature.js\").default|undefined}\n     */\n    this.format_ = options.format;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.overlaps_ = options.overlaps === undefined ? true : options.overlaps;\n\n    /**\n     * @private\n     * @type {string|import(\"../featureloader.js\").FeatureUrlFunction|undefined}\n     */\n    this.url_ = options.url;\n\n    if (options.loader !== undefined) {\n      this.loader_ = options.loader;\n    } else if (this.url_ !== undefined) {\n      assert(this.format_, 7); // `format` must be set when `url` is set\n      // create a XHR feature loader for \"url\" and \"format\"\n      this.loader_ = xhr(\n        this.url_,\n        /** @type {import(\"../format/Feature.js\").default} */ (this.format_)\n      );\n    }\n\n    /**\n     * @private\n     * @type {LoadingStrategy}\n     */\n    this.strategy_ =\n      options.strategy !== undefined ? options.strategy : allStrategy;\n\n    const useSpatialIndex =\n      options.useSpatialIndex !== undefined ? options.useSpatialIndex : true;\n\n    /**\n     * @private\n     * @type {RBush<import(\"../Feature.js\").default<Geometry>>}\n     */\n    this.featuresRtree_ = useSpatialIndex ? new RBush() : null;\n\n    /**\n     * @private\n     * @type {RBush<{extent: import(\"../extent.js\").Extent}>}\n     */\n    this.loadedExtentsRtree_ = new RBush();\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.loadingExtentsCount_ = 0;\n\n    /**\n     * @private\n     * @type {!Object<string, import(\"../Feature.js\").default<Geometry>>}\n     */\n    this.nullGeometryFeatures_ = {};\n\n    /**\n     * A lookup of features by id (the return from feature.getId()).\n     * @private\n     * @type {!Object<string, import(\"../Feature.js\").default<Geometry>>}\n     */\n    this.idIndex_ = {};\n\n    /**\n     * A lookup of features by uid (using getUid(feature)).\n     * @private\n     * @type {!Object<string, import(\"../Feature.js\").default<Geometry>>}\n     */\n    this.uidIndex_ = {};\n\n    /**\n     * @private\n     * @type {Object<string, Array<import(\"../events.js\").EventsKey>>}\n     */\n    this.featureChangeKeys_ = {};\n\n    /**\n     * @private\n     * @type {Collection<import(\"../Feature.js\").default<Geometry>>|null}\n     */\n    this.featuresCollection_ = null;\n\n    /** @type {Collection<import(\"../Feature.js\").default<Geometry>>} */\n    let collection;\n    /** @type {Array<import(\"../Feature.js\").default<Geometry>>} */\n    let features;\n    if (Array.isArray(options.features)) {\n      features = options.features;\n    } else if (options.features) {\n      collection = options.features;\n      features = collection.getArray();\n    }\n    if (!useSpatialIndex && collection === undefined) {\n      collection = new Collection(features);\n    }\n    if (features !== undefined) {\n      this.addFeaturesInternal(features);\n    }\n    if (collection !== undefined) {\n      this.bindFeaturesCollection_(collection);\n    }\n  }\n\n  /**\n   * Add a single feature to the source.  If you want to add a batch of features\n   * at once, call {@link module:ol/source/Vector~VectorSource#addFeatures #addFeatures()}\n   * instead. A feature will not be added to the source if feature with\n   * the same id is already there. The reason for this behavior is to avoid\n   * feature duplication when using bbox or tile loading strategies.\n   * Note: this also applies if an {@link module:ol/Collection~Collection} is used for features,\n   * meaning that if a feature with a duplicate id is added in the collection, it will\n   * be removed from it right away.\n   * @param {import(\"../Feature.js\").default<Geometry>} feature Feature to add.\n   * @api\n   */\n  addFeature(feature) {\n    this.addFeatureInternal(feature);\n    this.changed();\n  }\n\n  /**\n   * Add a feature without firing a `change` event.\n   * @param {import(\"../Feature.js\").default<Geometry>} feature Feature.\n   * @protected\n   */\n  addFeatureInternal(feature) {\n    const featureKey = getUid(feature);\n\n    if (!this.addToIndex_(featureKey, feature)) {\n      if (this.featuresCollection_) {\n        this.featuresCollection_.remove(feature);\n      }\n      return;\n    }\n\n    this.setupChangeEvents_(featureKey, feature);\n\n    const geometry = feature.getGeometry();\n    if (geometry) {\n      const extent = geometry.getExtent();\n      if (this.featuresRtree_) {\n        this.featuresRtree_.insert(extent, feature);\n      }\n    } else {\n      this.nullGeometryFeatures_[featureKey] = feature;\n    }\n\n    this.dispatchEvent(\n      new VectorSourceEvent(VectorEventType.ADDFEATURE, feature)\n    );\n  }\n\n  /**\n   * @param {string} featureKey Unique identifier for the feature.\n   * @param {import(\"../Feature.js\").default<Geometry>} feature The feature.\n   * @private\n   */\n  setupChangeEvents_(featureKey, feature) {\n    this.featureChangeKeys_[featureKey] = [\n      listen(feature, EventType.CHANGE, this.handleFeatureChange_, this),\n      listen(\n        feature,\n        ObjectEventType.PROPERTYCHANGE,\n        this.handleFeatureChange_,\n        this\n      ),\n    ];\n  }\n\n  /**\n   * @param {string} featureKey Unique identifier for the feature.\n   * @param {import(\"../Feature.js\").default<Geometry>} feature The feature.\n   * @return {boolean} The feature is \"valid\", in the sense that it is also a\n   *     candidate for insertion into the Rtree.\n   * @private\n   */\n  addToIndex_(featureKey, feature) {\n    let valid = true;\n    const id = feature.getId();\n    if (id !== undefined) {\n      if (!(id.toString() in this.idIndex_)) {\n        this.idIndex_[id.toString()] = feature;\n      } else {\n        valid = false;\n      }\n    }\n    if (valid) {\n      assert(!(featureKey in this.uidIndex_), 30); // The passed `feature` was already added to the source\n      this.uidIndex_[featureKey] = feature;\n    }\n    return valid;\n  }\n\n  /**\n   * Add a batch of features to the source.\n   * @param {Array<import(\"../Feature.js\").default<Geometry>>} features Features to add.\n   * @api\n   */\n  addFeatures(features) {\n    this.addFeaturesInternal(features);\n    this.changed();\n  }\n\n  /**\n   * Add features without firing a `change` event.\n   * @param {Array<import(\"../Feature.js\").default<Geometry>>} features Features.\n   * @protected\n   */\n  addFeaturesInternal(features) {\n    const extents = [];\n    const newFeatures = [];\n    const geometryFeatures = [];\n\n    for (let i = 0, length = features.length; i < length; i++) {\n      const feature = features[i];\n      const featureKey = getUid(feature);\n      if (this.addToIndex_(featureKey, feature)) {\n        newFeatures.push(feature);\n      }\n    }\n\n    for (let i = 0, length = newFeatures.length; i < length; i++) {\n      const feature = newFeatures[i];\n      const featureKey = getUid(feature);\n      this.setupChangeEvents_(featureKey, feature);\n\n      const geometry = feature.getGeometry();\n      if (geometry) {\n        const extent = geometry.getExtent();\n        extents.push(extent);\n        geometryFeatures.push(feature);\n      } else {\n        this.nullGeometryFeatures_[featureKey] = feature;\n      }\n    }\n    if (this.featuresRtree_) {\n      this.featuresRtree_.load(extents, geometryFeatures);\n    }\n\n    if (this.hasListener(VectorEventType.ADDFEATURE)) {\n      for (let i = 0, length = newFeatures.length; i < length; i++) {\n        this.dispatchEvent(\n          new VectorSourceEvent(VectorEventType.ADDFEATURE, newFeatures[i])\n        );\n      }\n    }\n  }\n\n  /**\n   * @param {!Collection<import(\"../Feature.js\").default<Geometry>>} collection Collection.\n   * @private\n   */\n  bindFeaturesCollection_(collection) {\n    let modifyingCollection = false;\n    this.addEventListener(\n      VectorEventType.ADDFEATURE,\n      /**\n       * @param {VectorSourceEvent<Geometry>} evt The vector source event\n       */\n      function (evt) {\n        if (!modifyingCollection) {\n          modifyingCollection = true;\n          collection.push(evt.feature);\n          modifyingCollection = false;\n        }\n      }\n    );\n    this.addEventListener(\n      VectorEventType.REMOVEFEATURE,\n      /**\n       * @param {VectorSourceEvent<Geometry>} evt The vector source event\n       */\n      function (evt) {\n        if (!modifyingCollection) {\n          modifyingCollection = true;\n          collection.remove(evt.feature);\n          modifyingCollection = false;\n        }\n      }\n    );\n    collection.addEventListener(\n      CollectionEventType.ADD,\n      /**\n       * @param {import(\"../Collection.js\").CollectionEvent<import(\"../Feature.js\").default<Geometry>>} evt The collection event\n       */\n      (evt) => {\n        if (!modifyingCollection) {\n          modifyingCollection = true;\n          this.addFeature(evt.element);\n          modifyingCollection = false;\n        }\n      }\n    );\n    collection.addEventListener(\n      CollectionEventType.REMOVE,\n      /**\n       * @param {import(\"../Collection.js\").CollectionEvent<import(\"../Feature.js\").default<Geometry>>} evt The collection event\n       */\n      (evt) => {\n        if (!modifyingCollection) {\n          modifyingCollection = true;\n          this.removeFeature(evt.element);\n          modifyingCollection = false;\n        }\n      }\n    );\n    this.featuresCollection_ = collection;\n  }\n\n  /**\n   * Remove all features from the source.\n   * @param {boolean} [fast] Skip dispatching of {@link module:ol/source/Vector.VectorSourceEvent#event:removefeature} events.\n   * @api\n   */\n  clear(fast) {\n    if (fast) {\n      for (const featureId in this.featureChangeKeys_) {\n        const keys = this.featureChangeKeys_[featureId];\n        keys.forEach(unlistenByKey);\n      }\n      if (!this.featuresCollection_) {\n        this.featureChangeKeys_ = {};\n        this.idIndex_ = {};\n        this.uidIndex_ = {};\n      }\n    } else {\n      if (this.featuresRtree_) {\n        const removeAndIgnoreReturn = (feature) => {\n          this.removeFeatureInternal(feature);\n        };\n        this.featuresRtree_.forEach(removeAndIgnoreReturn);\n        for (const id in this.nullGeometryFeatures_) {\n          this.removeFeatureInternal(this.nullGeometryFeatures_[id]);\n        }\n      }\n    }\n    if (this.featuresCollection_) {\n      this.featuresCollection_.clear();\n    }\n\n    if (this.featuresRtree_) {\n      this.featuresRtree_.clear();\n    }\n    this.nullGeometryFeatures_ = {};\n\n    const clearEvent = new VectorSourceEvent(VectorEventType.CLEAR);\n    this.dispatchEvent(clearEvent);\n    this.changed();\n  }\n\n  /**\n   * Iterate through all features on the source, calling the provided callback\n   * with each one.  If the callback returns any \"truthy\" value, iteration will\n   * stop and the function will return the same value.\n   * Note: this function only iterate through the feature that have a defined geometry.\n   *\n   * @param {function(import(\"../Feature.js\").default<Geometry>): T} callback Called with each feature\n   *     on the source.  Return a truthy value to stop iteration.\n   * @return {T|undefined} The return value from the last call to the callback.\n   * @template T\n   * @api\n   */\n  forEachFeature(callback) {\n    if (this.featuresRtree_) {\n      return this.featuresRtree_.forEach(callback);\n    }\n    if (this.featuresCollection_) {\n      this.featuresCollection_.forEach(callback);\n    }\n  }\n\n  /**\n   * Iterate through all features whose geometries contain the provided\n   * coordinate, calling the callback with each feature.  If the callback returns\n   * a \"truthy\" value, iteration will stop and the function will return the same\n   * value.\n   *\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @param {function(import(\"../Feature.js\").default<Geometry>): T} callback Called with each feature\n   *     whose goemetry contains the provided coordinate.\n   * @return {T|undefined} The return value from the last call to the callback.\n   * @template T\n   */\n  forEachFeatureAtCoordinateDirect(coordinate, callback) {\n    const extent = [coordinate[0], coordinate[1], coordinate[0], coordinate[1]];\n    return this.forEachFeatureInExtent(extent, function (feature) {\n      const geometry = feature.getGeometry();\n      if (geometry.intersectsCoordinate(coordinate)) {\n        return callback(feature);\n      }\n      return undefined;\n    });\n  }\n\n  /**\n   * Iterate through all features whose bounding box intersects the provided\n   * extent (note that the feature's geometry may not intersect the extent),\n   * calling the callback with each feature.  If the callback returns a \"truthy\"\n   * value, iteration will stop and the function will return the same value.\n   *\n   * If you are interested in features whose geometry intersects an extent, call\n   * the {@link module:ol/source/Vector~VectorSource#forEachFeatureIntersectingExtent #forEachFeatureIntersectingExtent()} method instead.\n   *\n   * When `useSpatialIndex` is set to false, this method will loop through all\n   * features, equivalent to {@link module:ol/source/Vector~VectorSource#forEachFeature #forEachFeature()}.\n   *\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {function(import(\"../Feature.js\").default<Geometry>): T} callback Called with each feature\n   *     whose bounding box intersects the provided extent.\n   * @return {T|undefined} The return value from the last call to the callback.\n   * @template T\n   * @api\n   */\n  forEachFeatureInExtent(extent, callback) {\n    if (this.featuresRtree_) {\n      return this.featuresRtree_.forEachInExtent(extent, callback);\n    }\n    if (this.featuresCollection_) {\n      this.featuresCollection_.forEach(callback);\n    }\n  }\n\n  /**\n   * Iterate through all features whose geometry intersects the provided extent,\n   * calling the callback with each feature.  If the callback returns a \"truthy\"\n   * value, iteration will stop and the function will return the same value.\n   *\n   * If you only want to test for bounding box intersection, call the\n   * {@link module:ol/source/Vector~VectorSource#forEachFeatureInExtent #forEachFeatureInExtent()} method instead.\n   *\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {function(import(\"../Feature.js\").default<Geometry>): T} callback Called with each feature\n   *     whose geometry intersects the provided extent.\n   * @return {T|undefined} The return value from the last call to the callback.\n   * @template T\n   * @api\n   */\n  forEachFeatureIntersectingExtent(extent, callback) {\n    return this.forEachFeatureInExtent(\n      extent,\n      /**\n       * @param {import(\"../Feature.js\").default<Geometry>} feature Feature.\n       * @return {T|undefined} The return value from the last call to the callback.\n       */\n      function (feature) {\n        const geometry = feature.getGeometry();\n        if (geometry.intersectsExtent(extent)) {\n          const result = callback(feature);\n          if (result) {\n            return result;\n          }\n        }\n      }\n    );\n  }\n\n  /**\n   * Get the features collection associated with this source. Will be `null`\n   * unless the source was configured with `useSpatialIndex` set to `false`, or\n   * with an {@link module:ol/Collection~Collection} as `features`.\n   * @return {Collection<import(\"../Feature.js\").default<Geometry>>|null} The collection of features.\n   * @api\n   */\n  getFeaturesCollection() {\n    return this.featuresCollection_;\n  }\n\n  /**\n   * Get a snapshot of the features currently on the source in random order. The returned array\n   * is a copy, the features are references to the features in the source.\n   * @return {Array<import(\"../Feature.js\").default<Geometry>>} Features.\n   * @api\n   */\n  getFeatures() {\n    let features;\n    if (this.featuresCollection_) {\n      features = this.featuresCollection_.getArray().slice(0);\n    } else if (this.featuresRtree_) {\n      features = this.featuresRtree_.getAll();\n      if (!isEmpty(this.nullGeometryFeatures_)) {\n        extend(features, Object.values(this.nullGeometryFeatures_));\n      }\n    }\n    return /** @type {Array<import(\"../Feature.js\").default<Geometry>>} */ (\n      features\n    );\n  }\n\n  /**\n   * Get all features whose geometry intersects the provided coordinate.\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @return {Array<import(\"../Feature.js\").default<Geometry>>} Features.\n   * @api\n   */\n  getFeaturesAtCoordinate(coordinate) {\n    const features = [];\n    this.forEachFeatureAtCoordinateDirect(coordinate, function (feature) {\n      features.push(feature);\n    });\n    return features;\n  }\n\n  /**\n   * Get all features whose bounding box intersects the provided extent.  Note that this returns an array of\n   * all features intersecting the given extent in random order (so it may include\n   * features whose geometries do not intersect the extent).\n   *\n   * When `useSpatialIndex` is set to false, this method will return all\n   * features.\n   *\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {import(\"../proj/Projection.js\").default} [projection] Include features\n   * where `extent` exceeds the x-axis bounds of `projection` and wraps around the world.\n   * @return {Array<import(\"../Feature.js\").default<Geometry>>} Features.\n   * @api\n   */\n  getFeaturesInExtent(extent, projection) {\n    if (this.featuresRtree_) {\n      const multiWorld = projection && projection.canWrapX() && this.getWrapX();\n\n      if (!multiWorld) {\n        return this.featuresRtree_.getInExtent(extent);\n      }\n\n      const extents = wrapAndSliceX(extent, projection);\n\n      return [].concat(\n        ...extents.map((anExtent) => this.featuresRtree_.getInExtent(anExtent))\n      );\n    }\n    if (this.featuresCollection_) {\n      return this.featuresCollection_.getArray().slice(0);\n    }\n    return [];\n  }\n\n  /**\n   * Get the closest feature to the provided coordinate.\n   *\n   * This method is not available when the source is configured with\n   * `useSpatialIndex` set to `false`.\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @param {function(import(\"../Feature.js\").default<Geometry>):boolean} [filter] Feature filter function.\n   *     The filter function will receive one argument, the {@link module:ol/Feature~Feature feature}\n   *     and it should return a boolean value. By default, no filtering is made.\n   * @return {import(\"../Feature.js\").default<Geometry>} Closest feature.\n   * @api\n   */\n  getClosestFeatureToCoordinate(coordinate, filter) {\n    // Find the closest feature using branch and bound.  We start searching an\n    // infinite extent, and find the distance from the first feature found.  This\n    // becomes the closest feature.  We then compute a smaller extent which any\n    // closer feature must intersect.  We continue searching with this smaller\n    // extent, trying to find a closer feature.  Every time we find a closer\n    // feature, we update the extent being searched so that any even closer\n    // feature must intersect it.  We continue until we run out of features.\n    const x = coordinate[0];\n    const y = coordinate[1];\n    let closestFeature = null;\n    const closestPoint = [NaN, NaN];\n    let minSquaredDistance = Infinity;\n    const extent = [-Infinity, -Infinity, Infinity, Infinity];\n    filter = filter ? filter : TRUE;\n    this.featuresRtree_.forEachInExtent(\n      extent,\n      /**\n       * @param {import(\"../Feature.js\").default<Geometry>} feature Feature.\n       */\n      function (feature) {\n        if (filter(feature)) {\n          const geometry = feature.getGeometry();\n          const previousMinSquaredDistance = minSquaredDistance;\n          minSquaredDistance = geometry.closestPointXY(\n            x,\n            y,\n            closestPoint,\n            minSquaredDistance\n          );\n          if (minSquaredDistance < previousMinSquaredDistance) {\n            closestFeature = feature;\n            // This is sneaky.  Reduce the extent that it is currently being\n            // searched while the R-Tree traversal using this same extent object\n            // is still in progress.  This is safe because the new extent is\n            // strictly contained by the old extent.\n            const minDistance = Math.sqrt(minSquaredDistance);\n            extent[0] = x - minDistance;\n            extent[1] = y - minDistance;\n            extent[2] = x + minDistance;\n            extent[3] = y + minDistance;\n          }\n        }\n      }\n    );\n    return closestFeature;\n  }\n\n  /**\n   * Get the extent of the features currently in the source.\n   *\n   * This method is not available when the source is configured with\n   * `useSpatialIndex` set to `false`.\n   * @param {import(\"../extent.js\").Extent} [extent] Destination extent. If provided, no new extent\n   *     will be created. Instead, that extent's coordinates will be overwritten.\n   * @return {import(\"../extent.js\").Extent} Extent.\n   * @api\n   */\n  getExtent(extent) {\n    return this.featuresRtree_.getExtent(extent);\n  }\n\n  /**\n   * Get a feature by its identifier (the value returned by feature.getId()).\n   * Note that the index treats string and numeric identifiers as the same.  So\n   * `source.getFeatureById(2)` will return a feature with id `'2'` or `2`.\n   *\n   * @param {string|number} id Feature identifier.\n   * @return {import(\"../Feature.js\").default<Geometry>|null} The feature (or `null` if not found).\n   * @api\n   */\n  getFeatureById(id) {\n    const feature = this.idIndex_[id.toString()];\n    return feature !== undefined ? feature : null;\n  }\n\n  /**\n   * Get a feature by its internal unique identifier (using `getUid`).\n   *\n   * @param {string} uid Feature identifier.\n   * @return {import(\"../Feature.js\").default<Geometry>|null} The feature (or `null` if not found).\n   */\n  getFeatureByUid(uid) {\n    const feature = this.uidIndex_[uid];\n    return feature !== undefined ? feature : null;\n  }\n\n  /**\n   * Get the format associated with this source.\n   *\n   * @return {import(\"../format/Feature.js\").default|undefined} The feature format.\n   * @api\n   */\n  getFormat() {\n    return this.format_;\n  }\n\n  /**\n   * @return {boolean} The source can have overlapping geometries.\n   */\n  getOverlaps() {\n    return this.overlaps_;\n  }\n\n  /**\n   * Get the url associated with this source.\n   *\n   * @return {string|import(\"../featureloader.js\").FeatureUrlFunction|undefined} The url.\n   * @api\n   */\n  getUrl() {\n    return this.url_;\n  }\n\n  /**\n   * @param {Event} event Event.\n   * @private\n   */\n  handleFeatureChange_(event) {\n    const feature = /** @type {import(\"../Feature.js\").default<Geometry>} */ (\n      event.target\n    );\n    const featureKey = getUid(feature);\n    const geometry = feature.getGeometry();\n    if (!geometry) {\n      if (!(featureKey in this.nullGeometryFeatures_)) {\n        if (this.featuresRtree_) {\n          this.featuresRtree_.remove(feature);\n        }\n        this.nullGeometryFeatures_[featureKey] = feature;\n      }\n    } else {\n      const extent = geometry.getExtent();\n      if (featureKey in this.nullGeometryFeatures_) {\n        delete this.nullGeometryFeatures_[featureKey];\n        if (this.featuresRtree_) {\n          this.featuresRtree_.insert(extent, feature);\n        }\n      } else {\n        if (this.featuresRtree_) {\n          this.featuresRtree_.update(extent, feature);\n        }\n      }\n    }\n    const id = feature.getId();\n    if (id !== undefined) {\n      const sid = id.toString();\n      if (this.idIndex_[sid] !== feature) {\n        this.removeFromIdIndex_(feature);\n        this.idIndex_[sid] = feature;\n      }\n    } else {\n      this.removeFromIdIndex_(feature);\n      this.uidIndex_[featureKey] = feature;\n    }\n    this.changed();\n    this.dispatchEvent(\n      new VectorSourceEvent(VectorEventType.CHANGEFEATURE, feature)\n    );\n  }\n\n  /**\n   * Returns true if the feature is contained within the source.\n   * @param {import(\"../Feature.js\").default<Geometry>} feature Feature.\n   * @return {boolean} Has feature.\n   * @api\n   */\n  hasFeature(feature) {\n    const id = feature.getId();\n    if (id !== undefined) {\n      return id in this.idIndex_;\n    }\n    return getUid(feature) in this.uidIndex_;\n  }\n\n  /**\n   * @return {boolean} Is empty.\n   */\n  isEmpty() {\n    if (this.featuresRtree_) {\n      return (\n        this.featuresRtree_.isEmpty() && isEmpty(this.nullGeometryFeatures_)\n      );\n    }\n    if (this.featuresCollection_) {\n      return this.featuresCollection_.getLength() === 0;\n    }\n    return true;\n  }\n\n  /**\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {number} resolution Resolution.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   */\n  loadFeatures(extent, resolution, projection) {\n    const loadedExtentsRtree = this.loadedExtentsRtree_;\n    const extentsToLoad = this.strategy_(extent, resolution, projection);\n    for (let i = 0, ii = extentsToLoad.length; i < ii; ++i) {\n      const extentToLoad = extentsToLoad[i];\n      const alreadyLoaded = loadedExtentsRtree.forEachInExtent(\n        extentToLoad,\n        /**\n         * @param {{extent: import(\"../extent.js\").Extent}} object Object.\n         * @return {boolean} Contains.\n         */\n        function (object) {\n          return containsExtent(object.extent, extentToLoad);\n        }\n      );\n      if (!alreadyLoaded) {\n        ++this.loadingExtentsCount_;\n        this.dispatchEvent(\n          new VectorSourceEvent(VectorEventType.FEATURESLOADSTART)\n        );\n        this.loader_.call(\n          this,\n          extentToLoad,\n          resolution,\n          projection,\n          (features) => {\n            --this.loadingExtentsCount_;\n            this.dispatchEvent(\n              new VectorSourceEvent(\n                VectorEventType.FEATURESLOADEND,\n                undefined,\n                features\n              )\n            );\n          },\n          () => {\n            --this.loadingExtentsCount_;\n            this.dispatchEvent(\n              new VectorSourceEvent(VectorEventType.FEATURESLOADERROR)\n            );\n          }\n        );\n        loadedExtentsRtree.insert(extentToLoad, {extent: extentToLoad.slice()});\n      }\n    }\n    this.loading =\n      this.loader_.length < 4 ? false : this.loadingExtentsCount_ > 0;\n  }\n\n  refresh() {\n    this.clear(true);\n    this.loadedExtentsRtree_.clear();\n    super.refresh();\n  }\n\n  /**\n   * Remove an extent from the list of loaded extents.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @api\n   */\n  removeLoadedExtent(extent) {\n    const loadedExtentsRtree = this.loadedExtentsRtree_;\n    let obj;\n    loadedExtentsRtree.forEachInExtent(extent, function (object) {\n      if (equals(object.extent, extent)) {\n        obj = object;\n        return true;\n      }\n    });\n    if (obj) {\n      loadedExtentsRtree.remove(obj);\n    }\n  }\n\n  /**\n   * Remove a single feature from the source.  If you want to remove all features\n   * at once, use the {@link module:ol/source/Vector~VectorSource#clear #clear()} method\n   * instead.\n   * @param {import(\"../Feature.js\").default<Geometry>} feature Feature to remove.\n   * @api\n   */\n  removeFeature(feature) {\n    if (!feature) {\n      return;\n    }\n    const featureKey = getUid(feature);\n    if (featureKey in this.nullGeometryFeatures_) {\n      delete this.nullGeometryFeatures_[featureKey];\n    } else {\n      if (this.featuresRtree_) {\n        this.featuresRtree_.remove(feature);\n      }\n    }\n    const result = this.removeFeatureInternal(feature);\n    if (result) {\n      this.changed();\n    }\n  }\n\n  /**\n   * Remove feature without firing a `change` event.\n   * @param {import(\"../Feature.js\").default<Geometry>} feature Feature.\n   * @return {import(\"../Feature.js\").default<Geometry>|undefined} The removed feature\n   *     (or undefined if the feature was not found).\n   * @protected\n   */\n  removeFeatureInternal(feature) {\n    const featureKey = getUid(feature);\n    const featureChangeKeys = this.featureChangeKeys_[featureKey];\n    if (!featureChangeKeys) {\n      return;\n    }\n    featureChangeKeys.forEach(unlistenByKey);\n    delete this.featureChangeKeys_[featureKey];\n    const id = feature.getId();\n    if (id !== undefined) {\n      delete this.idIndex_[id.toString()];\n    }\n    delete this.uidIndex_[featureKey];\n    this.dispatchEvent(\n      new VectorSourceEvent(VectorEventType.REMOVEFEATURE, feature)\n    );\n    return feature;\n  }\n\n  /**\n   * Remove a feature from the id index.  Called internally when the feature id\n   * may have changed.\n   * @param {import(\"../Feature.js\").default<Geometry>} feature The feature.\n   * @return {boolean} Removed the feature from the index.\n   * @private\n   */\n  removeFromIdIndex_(feature) {\n    let removed = false;\n    for (const id in this.idIndex_) {\n      if (this.idIndex_[id] === feature) {\n        delete this.idIndex_[id];\n        removed = true;\n        break;\n      }\n    }\n    return removed;\n  }\n\n  /**\n   * Set the new loader of the source. The next render cycle will use the\n   * new loader.\n   * @param {import(\"../featureloader.js\").FeatureLoader} loader The loader to set.\n   * @api\n   */\n  setLoader(loader) {\n    this.loader_ = loader;\n  }\n\n  /**\n   * Points the source to a new url. The next render cycle will use the new url.\n   * @param {string|import(\"../featureloader.js\").FeatureUrlFunction} url Url.\n   * @api\n   */\n  setUrl(url) {\n    assert(this.format_, 7); // `format` must be set when `url` is set\n    this.url_ = url;\n    this.setLoader(xhr(url, this.format_));\n  }\n}\n\nexport default VectorSource;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,IAAMA,SAAN,MAAY;AAAA;AAAA;AAAA;AAAA,EAIV,YAAY,YAAY;AAItB,SAAK,SAAS,IAAI,MAAO,UAAU;AAQnC,SAAK,SAAS,CAAC;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,QAAQ,OAAO;AAEpB,UAAM,OAAO;AAAA,MACX,MAAM,OAAO,CAAC;AAAA,MACd,MAAM,OAAO,CAAC;AAAA,MACd,MAAM,OAAO,CAAC;AAAA,MACd,MAAM,OAAO,CAAC;AAAA,MACd;AAAA,IACF;AAEA,SAAK,OAAO,OAAO,IAAI;AACvB,SAAK,OAAO,OAAO,KAAK,CAAC,IAAI;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,SAAS,QAAQ;AACpB,UAAM,QAAQ,IAAI,MAAM,OAAO,MAAM;AACrC,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,YAAM,SAAS,QAAQ,CAAC;AACxB,YAAM,QAAQ,OAAO,CAAC;AAGtB,YAAM,OAAO;AAAA,QACX,MAAM,OAAO,CAAC;AAAA,QACd,MAAM,OAAO,CAAC;AAAA,QACd,MAAM,OAAO,CAAC;AAAA,QACd,MAAM,OAAO,CAAC;AAAA,QACd;AAAA,MACF;AACA,YAAM,CAAC,IAAI;AACX,WAAK,OAAO,OAAO,KAAK,CAAC,IAAI;AAAA,IAC/B;AACA,SAAK,OAAO,KAAK,KAAK;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,OAAO;AACZ,UAAM,MAAM,OAAO,KAAK;AAIxB,UAAM,OAAO,KAAK,OAAO,GAAG;AAC5B,WAAO,KAAK,OAAO,GAAG;AACtB,WAAO,KAAK,OAAO,OAAO,IAAI,MAAM;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,QAAQ,OAAO;AACpB,UAAM,OAAO,KAAK,OAAO,OAAO,KAAK,CAAC;AACtC,UAAM,OAAO,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AACxD,QAAI,CAAC,OAAO,MAAM,MAAM,GAAG;AACzB,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO,QAAQ,KAAK;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,UAAM,QAAQ,KAAK,OAAO,IAAI;AAC9B,WAAO,MAAM,IAAI,SAAU,MAAM;AAC/B,aAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,QAAQ;AAElB,UAAM,OAAO;AAAA,MACX,MAAM,OAAO,CAAC;AAAA,MACd,MAAM,OAAO,CAAC;AAAA,MACd,MAAM,OAAO,CAAC;AAAA,MACd,MAAM,OAAO,CAAC;AAAA,IAChB;AACA,UAAM,QAAQ,KAAK,OAAO,OAAO,IAAI;AACrC,WAAO,MAAM,IAAI,SAAU,MAAM;AAC/B,aAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,UAAU;AAChB,WAAO,KAAK,SAAS,KAAK,OAAO,GAAG,QAAQ;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,QAAQ,UAAU;AAChC,WAAO,KAAK,SAAS,KAAK,YAAY,MAAM,GAAG,QAAQ;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,QAAQ,UAAU;AACzB,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,eAAS,SAAS,OAAO,CAAC,CAAC;AAC3B,UAAI,QAAQ;AACV,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAO,QAAQ,KAAK,MAAM;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,SAAK,OAAO,MAAM;AAClB,SAAK,SAAS,CAAC;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,QAAQ;AAChB,UAAM,OAAO,KAAK,OAAO,OAAO;AAChC,WAAO,eAAe,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,MAAM;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO;AACZ,SAAK,OAAO,KAAK,MAAM,OAAO,IAAI,CAAC;AACnC,eAAW,KAAK,MAAM,QAAQ;AAC5B,WAAK,OAAO,CAAC,IAAI,MAAM,OAAO,CAAC;AAAA,IACjC;AAAA,EACF;AACF;AAEA,IAAO,gBAAQA;;;ACpNf,IAAO,0BAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOZ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOf,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQP,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOf,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOnB,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjB,mBAAmB;AACrB;;;AC5CO,SAAS,IAAI,QAAQ,YAAY;AACtC,SAAO,CAAC,CAAC,WAAW,WAAW,UAAU,QAAQ,CAAC;AACpD;;;ACLA,IAAI,kBAAkB;AAgDf,SAAS,gBACd,KACA,QACA,QACA,YACA,YACA,SACA,SACA;AACA,QAAMC,OAAM,IAAI,eAAe;AAC/B,EAAAA,KAAI;AAAA,IACF;AAAA,IACA,OAAO,QAAQ,aAAa,IAAI,QAAQ,YAAY,UAAU,IAAI;AAAA,IAClE;AAAA,EACF;AACA,MAAI,OAAO,QAAQ,KAAK,eAAe;AACrC,IAAAA,KAAI,eAAe;AAAA,EACrB;AACA,EAAAA,KAAI,kBAAkB;AAKtB,EAAAA,KAAI,SAAS,SAAU,OAAO;AAE5B,QAAI,CAACA,KAAI,UAAWA,KAAI,UAAU,OAAOA,KAAI,SAAS,KAAM;AAC1D,YAAM,OAAO,OAAO,QAAQ;AAE5B,UAAI;AACJ,UAAI,QAAQ,UAAU,QAAQ,QAAQ;AACpC,iBAASA,KAAI;AAAA,MACf,WAAW,QAAQ,OAAO;AACxB,iBAASA,KAAI;AACb,YAAI,CAAC,QAAQ;AACX,mBAAS,IAAI,UAAU,EAAE;AAAA,YACvBA,KAAI;AAAA,YACJ;AAAA,UACF;AAAA,QACF;AAAA,MACF,WAAW,QAAQ,eAAe;AAChC;AAAA,QAAqCA,KAAI;AAAA,MAC3C;AACA,UAAI,QAAQ;AACV;AAAA;AAAA,UAGI,OAAO,aAAa,QAAQ;AAAA,YAC1B;AAAA,YACA,mBAAmB;AAAA,UACrB,CAAC;AAAA,UAEH,OAAO,eAAe,MAAM;AAAA,QAC9B;AAAA,MACF,OAAO;AACL,gBAAQ;AAAA,MACV;AAAA,IACF,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF;AAIA,EAAAA,KAAI,UAAU;AACd,EAAAA,KAAI,KAAK;AACX;AAWO,SAAS,IAAI,KAAK,QAAQ;AAU/B,SAAO,SAAU,QAAQ,YAAY,YAAY,SAAS,SAAS;AACjE,UAAM;AAAA;AAAA,MAA2D;AAAA;AACjE;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,SAAU,UAAU,gBAAgB;AAClC,eAAO,YAAY,QAAQ;AAC3B,YAAI,YAAY,QAAW;AACzB,kBAAQ,QAAQ;AAAA,QAClB;AAAA,MACF;AAAA;AAAA,MACyB,UAAU,UAAU;AAAA,IAC/C;AAAA,EACF;AACF;;;ACjIO,IAAM,oBAAN,cAAgC,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3C,YAAY,MAAM,SAAS,UAAU;AACnC,UAAM,IAAI;AAOV,SAAK,UAAU;AAOf,SAAK,WAAW;AAAA,EAClB;AACF;AAkHA,IAAM,eAAN,cAA2B,eAAO;AAAA;AAAA;AAAA;AAAA,EAIhC,YAAY,SAAS;AACnB,cAAU,WAAW,CAAC;AAEtB,UAAM;AAAA,MACJ,cAAc,QAAQ;AAAA,MACtB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAAA,IACvD,CAAC;AAKD,SAAK;AAKL,SAAK;AAKL,SAAK;AAML,SAAK,UAAU;AAMf,SAAK,UAAU,QAAQ;AAMvB,SAAK,YAAY,QAAQ,aAAa,SAAY,OAAO,QAAQ;AAMjE,SAAK,OAAO,QAAQ;AAEpB,QAAI,QAAQ,WAAW,QAAW;AAChC,WAAK,UAAU,QAAQ;AAAA,IACzB,WAAW,KAAK,SAAS,QAAW;AAClC,aAAO,KAAK,SAAS,CAAC;AAEtB,WAAK,UAAU;AAAA,QACb,KAAK;AAAA;AAAA,QACkD,KAAK;AAAA,MAC9D;AAAA,IACF;AAMA,SAAK,YACH,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAEtD,UAAM,kBACJ,QAAQ,oBAAoB,SAAY,QAAQ,kBAAkB;AAMpE,SAAK,iBAAiB,kBAAkB,IAAI,cAAM,IAAI;AAMtD,SAAK,sBAAsB,IAAI,cAAM;AAMrC,SAAK,uBAAuB;AAM5B,SAAK,wBAAwB,CAAC;AAO9B,SAAK,WAAW,CAAC;AAOjB,SAAK,YAAY,CAAC;AAMlB,SAAK,qBAAqB,CAAC;AAM3B,SAAK,sBAAsB;AAG3B,QAAI;AAEJ,QAAI;AACJ,QAAI,MAAM,QAAQ,QAAQ,QAAQ,GAAG;AACnC,iBAAW,QAAQ;AAAA,IACrB,WAAW,QAAQ,UAAU;AAC3B,mBAAa,QAAQ;AACrB,iBAAW,WAAW,SAAS;AAAA,IACjC;AACA,QAAI,CAAC,mBAAmB,eAAe,QAAW;AAChD,mBAAa,IAAI,mBAAW,QAAQ;AAAA,IACtC;AACA,QAAI,aAAa,QAAW;AAC1B,WAAK,oBAAoB,QAAQ;AAAA,IACnC;AACA,QAAI,eAAe,QAAW;AAC5B,WAAK,wBAAwB,UAAU;AAAA,IACzC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,WAAW,SAAS;AAClB,SAAK,mBAAmB,OAAO;AAC/B,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,SAAS;AAC1B,UAAM,aAAa,OAAO,OAAO;AAEjC,QAAI,CAAC,KAAK,YAAY,YAAY,OAAO,GAAG;AAC1C,UAAI,KAAK,qBAAqB;AAC5B,aAAK,oBAAoB,OAAO,OAAO;AAAA,MACzC;AACA;AAAA,IACF;AAEA,SAAK,mBAAmB,YAAY,OAAO;AAE3C,UAAM,WAAW,QAAQ,YAAY;AACrC,QAAI,UAAU;AACZ,YAAM,SAAS,SAAS,UAAU;AAClC,UAAI,KAAK,gBAAgB;AACvB,aAAK,eAAe,OAAO,QAAQ,OAAO;AAAA,MAC5C;AAAA,IACF,OAAO;AACL,WAAK,sBAAsB,UAAU,IAAI;AAAA,IAC3C;AAEA,SAAK;AAAA,MACH,IAAI,kBAAkB,wBAAgB,YAAY,OAAO;AAAA,IAC3D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,YAAY,SAAS;AACtC,SAAK,mBAAmB,UAAU,IAAI;AAAA,MACpC,OAAO,SAAS,kBAAU,QAAQ,KAAK,sBAAsB,IAAI;AAAA,MACjE;AAAA,QACE;AAAA,QACA,wBAAgB;AAAA,QAChB,KAAK;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,YAAY,SAAS;AAC/B,QAAI,QAAQ;AACZ,UAAM,KAAK,QAAQ,MAAM;AACzB,QAAI,OAAO,QAAW;AACpB,UAAI,EAAE,GAAG,SAAS,KAAK,KAAK,WAAW;AACrC,aAAK,SAAS,GAAG,SAAS,CAAC,IAAI;AAAA,MACjC,OAAO;AACL,gBAAQ;AAAA,MACV;AAAA,IACF;AACA,QAAI,OAAO;AACT,aAAO,EAAE,cAAc,KAAK,YAAY,EAAE;AAC1C,WAAK,UAAU,UAAU,IAAI;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,UAAU;AACpB,SAAK,oBAAoB,QAAQ;AACjC,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,UAAU;AAC5B,UAAM,UAAU,CAAC;AACjB,UAAM,cAAc,CAAC;AACrB,UAAM,mBAAmB,CAAC;AAE1B,aAAS,IAAI,GAAG,SAAS,SAAS,QAAQ,IAAI,QAAQ,KAAK;AACzD,YAAM,UAAU,SAAS,CAAC;AAC1B,YAAM,aAAa,OAAO,OAAO;AACjC,UAAI,KAAK,YAAY,YAAY,OAAO,GAAG;AACzC,oBAAY,KAAK,OAAO;AAAA,MAC1B;AAAA,IACF;AAEA,aAAS,IAAI,GAAG,SAAS,YAAY,QAAQ,IAAI,QAAQ,KAAK;AAC5D,YAAM,UAAU,YAAY,CAAC;AAC7B,YAAM,aAAa,OAAO,OAAO;AACjC,WAAK,mBAAmB,YAAY,OAAO;AAE3C,YAAM,WAAW,QAAQ,YAAY;AACrC,UAAI,UAAU;AACZ,cAAM,SAAS,SAAS,UAAU;AAClC,gBAAQ,KAAK,MAAM;AACnB,yBAAiB,KAAK,OAAO;AAAA,MAC/B,OAAO;AACL,aAAK,sBAAsB,UAAU,IAAI;AAAA,MAC3C;AAAA,IACF;AACA,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe,KAAK,SAAS,gBAAgB;AAAA,IACpD;AAEA,QAAI,KAAK,YAAY,wBAAgB,UAAU,GAAG;AAChD,eAAS,IAAI,GAAG,SAAS,YAAY,QAAQ,IAAI,QAAQ,KAAK;AAC5D,aAAK;AAAA,UACH,IAAI,kBAAkB,wBAAgB,YAAY,YAAY,CAAC,CAAC;AAAA,QAClE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB,YAAY;AAClC,QAAI,sBAAsB;AAC1B,SAAK;AAAA,MACH,wBAAgB;AAAA;AAAA;AAAA;AAAA,MAIhB,SAAU,KAAK;AACb,YAAI,CAAC,qBAAqB;AACxB,gCAAsB;AACtB,qBAAW,KAAK,IAAI,OAAO;AAC3B,gCAAsB;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,SAAK;AAAA,MACH,wBAAgB;AAAA;AAAA;AAAA;AAAA,MAIhB,SAAU,KAAK;AACb,YAAI,CAAC,qBAAqB;AACxB,gCAAsB;AACtB,qBAAW,OAAO,IAAI,OAAO;AAC7B,gCAAsB;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,eAAW;AAAA,MACT,4BAAoB;AAAA;AAAA;AAAA;AAAA,MAIpB,CAAC,QAAQ;AACP,YAAI,CAAC,qBAAqB;AACxB,gCAAsB;AACtB,eAAK,WAAW,IAAI,OAAO;AAC3B,gCAAsB;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,eAAW;AAAA,MACT,4BAAoB;AAAA;AAAA;AAAA;AAAA,MAIpB,CAAC,QAAQ;AACP,YAAI,CAAC,qBAAqB;AACxB,gCAAsB;AACtB,eAAK,cAAc,IAAI,OAAO;AAC9B,gCAAsB;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,MAAM;AACV,QAAI,MAAM;AACR,iBAAW,aAAa,KAAK,oBAAoB;AAC/C,cAAM,OAAO,KAAK,mBAAmB,SAAS;AAC9C,aAAK,QAAQ,aAAa;AAAA,MAC5B;AACA,UAAI,CAAC,KAAK,qBAAqB;AAC7B,aAAK,qBAAqB,CAAC;AAC3B,aAAK,WAAW,CAAC;AACjB,aAAK,YAAY,CAAC;AAAA,MACpB;AAAA,IACF,OAAO;AACL,UAAI,KAAK,gBAAgB;AACvB,cAAM,wBAAwB,CAAC,YAAY;AACzC,eAAK,sBAAsB,OAAO;AAAA,QACpC;AACA,aAAK,eAAe,QAAQ,qBAAqB;AACjD,mBAAW,MAAM,KAAK,uBAAuB;AAC3C,eAAK,sBAAsB,KAAK,sBAAsB,EAAE,CAAC;AAAA,QAC3D;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,MAAM;AAAA,IACjC;AAEA,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe,MAAM;AAAA,IAC5B;AACA,SAAK,wBAAwB,CAAC;AAE9B,UAAM,aAAa,IAAI,kBAAkB,wBAAgB,KAAK;AAC9D,SAAK,cAAc,UAAU;AAC7B,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,eAAe,UAAU;AACvB,QAAI,KAAK,gBAAgB;AACvB,aAAO,KAAK,eAAe,QAAQ,QAAQ;AAAA,IAC7C;AACA,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,QAAQ,QAAQ;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,iCAAiC,YAAY,UAAU;AACrD,UAAM,SAAS,CAAC,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AAC1E,WAAO,KAAK,uBAAuB,QAAQ,SAAU,SAAS;AAC5D,YAAM,WAAW,QAAQ,YAAY;AACrC,UAAI,SAAS,qBAAqB,UAAU,GAAG;AAC7C,eAAO,SAAS,OAAO;AAAA,MACzB;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,uBAAuB,QAAQ,UAAU;AACvC,QAAI,KAAK,gBAAgB;AACvB,aAAO,KAAK,eAAe,gBAAgB,QAAQ,QAAQ;AAAA,IAC7D;AACA,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,QAAQ,QAAQ;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,iCAAiC,QAAQ,UAAU;AACjD,WAAO,KAAK;AAAA,MACV;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,SAAU,SAAS;AACjB,cAAM,WAAW,QAAQ,YAAY;AACrC,YAAI,SAAS,iBAAiB,MAAM,GAAG;AACrC,gBAAM,SAAS,SAAS,OAAO;AAC/B,cAAI,QAAQ;AACV,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,wBAAwB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc;AACZ,QAAI;AACJ,QAAI,KAAK,qBAAqB;AAC5B,iBAAW,KAAK,oBAAoB,SAAS,EAAE,MAAM,CAAC;AAAA,IACxD,WAAW,KAAK,gBAAgB;AAC9B,iBAAW,KAAK,eAAe,OAAO;AACtC,UAAI,CAAC,QAAQ,KAAK,qBAAqB,GAAG;AACxC,eAAO,UAAU,OAAO,OAAO,KAAK,qBAAqB,CAAC;AAAA,MAC5D;AAAA,IACF;AACA;AAAA;AAAA,MACE;AAAA;AAAA,EAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,wBAAwB,YAAY;AAClC,UAAM,WAAW,CAAC;AAClB,SAAK,iCAAiC,YAAY,SAAU,SAAS;AACnE,eAAS,KAAK,OAAO;AAAA,IACvB,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,oBAAoB,QAAQ,YAAY;AACtC,QAAI,KAAK,gBAAgB;AACvB,YAAM,aAAa,cAAc,WAAW,SAAS,KAAK,KAAK,SAAS;AAExE,UAAI,CAAC,YAAY;AACf,eAAO,KAAK,eAAe,YAAY,MAAM;AAAA,MAC/C;AAEA,YAAM,UAAU,cAAc,QAAQ,UAAU;AAEhD,aAAO,CAAC,EAAE;AAAA,QACR,GAAG,QAAQ,IAAI,CAAC,aAAa,KAAK,eAAe,YAAY,QAAQ,CAAC;AAAA,MACxE;AAAA,IACF;AACA,QAAI,KAAK,qBAAqB;AAC5B,aAAO,KAAK,oBAAoB,SAAS,EAAE,MAAM,CAAC;AAAA,IACpD;AACA,WAAO,CAAC;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,8BAA8B,YAAY,QAAQ;AAQhD,UAAM,IAAI,WAAW,CAAC;AACtB,UAAM,IAAI,WAAW,CAAC;AACtB,QAAI,iBAAiB;AACrB,UAAM,eAAe,CAAC,KAAK,GAAG;AAC9B,QAAI,qBAAqB;AACzB,UAAM,SAAS,CAAC,WAAW,WAAW,UAAU,QAAQ;AACxD,aAAS,SAAS,SAAS;AAC3B,SAAK,eAAe;AAAA,MAClB;AAAA;AAAA;AAAA;AAAA,MAIA,SAAU,SAAS;AACjB,YAAI,OAAO,OAAO,GAAG;AACnB,gBAAM,WAAW,QAAQ,YAAY;AACrC,gBAAM,6BAA6B;AACnC,+BAAqB,SAAS;AAAA,YAC5B;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,cAAI,qBAAqB,4BAA4B;AACnD,6BAAiB;AAKjB,kBAAM,cAAc,KAAK,KAAK,kBAAkB;AAChD,mBAAO,CAAC,IAAI,IAAI;AAChB,mBAAO,CAAC,IAAI,IAAI;AAChB,mBAAO,CAAC,IAAI,IAAI;AAChB,mBAAO,CAAC,IAAI,IAAI;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,UAAU,QAAQ;AAChB,WAAO,KAAK,eAAe,UAAU,MAAM;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,eAAe,IAAI;AACjB,UAAM,UAAU,KAAK,SAAS,GAAG,SAAS,CAAC;AAC3C,WAAO,YAAY,SAAY,UAAU;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,KAAK;AACnB,UAAM,UAAU,KAAK,UAAU,GAAG;AAClC,WAAO,YAAY,SAAY,UAAU;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,OAAO;AAC1B,UAAM;AAAA;AAAA,MACJ,MAAM;AAAA;AAER,UAAM,aAAa,OAAO,OAAO;AACjC,UAAM,WAAW,QAAQ,YAAY;AACrC,QAAI,CAAC,UAAU;AACb,UAAI,EAAE,cAAc,KAAK,wBAAwB;AAC/C,YAAI,KAAK,gBAAgB;AACvB,eAAK,eAAe,OAAO,OAAO;AAAA,QACpC;AACA,aAAK,sBAAsB,UAAU,IAAI;AAAA,MAC3C;AAAA,IACF,OAAO;AACL,YAAM,SAAS,SAAS,UAAU;AAClC,UAAI,cAAc,KAAK,uBAAuB;AAC5C,eAAO,KAAK,sBAAsB,UAAU;AAC5C,YAAI,KAAK,gBAAgB;AACvB,eAAK,eAAe,OAAO,QAAQ,OAAO;AAAA,QAC5C;AAAA,MACF,OAAO;AACL,YAAI,KAAK,gBAAgB;AACvB,eAAK,eAAe,OAAO,QAAQ,OAAO;AAAA,QAC5C;AAAA,MACF;AAAA,IACF;AACA,UAAM,KAAK,QAAQ,MAAM;AACzB,QAAI,OAAO,QAAW;AACpB,YAAM,MAAM,GAAG,SAAS;AACxB,UAAI,KAAK,SAAS,GAAG,MAAM,SAAS;AAClC,aAAK,mBAAmB,OAAO;AAC/B,aAAK,SAAS,GAAG,IAAI;AAAA,MACvB;AAAA,IACF,OAAO;AACL,WAAK,mBAAmB,OAAO;AAC/B,WAAK,UAAU,UAAU,IAAI;AAAA,IAC/B;AACA,SAAK,QAAQ;AACb,SAAK;AAAA,MACH,IAAI,kBAAkB,wBAAgB,eAAe,OAAO;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAClB,UAAM,KAAK,QAAQ,MAAM;AACzB,QAAI,OAAO,QAAW;AACpB,aAAO,MAAM,KAAK;AAAA,IACpB;AACA,WAAO,OAAO,OAAO,KAAK,KAAK;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,QAAI,KAAK,gBAAgB;AACvB,aACE,KAAK,eAAe,QAAQ,KAAK,QAAQ,KAAK,qBAAqB;AAAA,IAEvE;AACA,QAAI,KAAK,qBAAqB;AAC5B,aAAO,KAAK,oBAAoB,UAAU,MAAM;AAAA,IAClD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,QAAQ,YAAY,YAAY;AAC3C,UAAM,qBAAqB,KAAK;AAChC,UAAM,gBAAgB,KAAK,UAAU,QAAQ,YAAY,UAAU;AACnE,aAAS,IAAI,GAAG,KAAK,cAAc,QAAQ,IAAI,IAAI,EAAE,GAAG;AACtD,YAAM,eAAe,cAAc,CAAC;AACpC,YAAM,gBAAgB,mBAAmB;AAAA,QACvC;AAAA;AAAA;AAAA;AAAA;AAAA,QAKA,SAAU,QAAQ;AAChB,iBAAO,eAAe,OAAO,QAAQ,YAAY;AAAA,QACnD;AAAA,MACF;AACA,UAAI,CAAC,eAAe;AAClB,UAAE,KAAK;AACP,aAAK;AAAA,UACH,IAAI,kBAAkB,wBAAgB,iBAAiB;AAAA,QACzD;AACA,aAAK,QAAQ;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,CAAC,aAAa;AACZ,cAAE,KAAK;AACP,iBAAK;AAAA,cACH,IAAI;AAAA,gBACF,wBAAgB;AAAA,gBAChB;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,MAAM;AACJ,cAAE,KAAK;AACP,iBAAK;AAAA,cACH,IAAI,kBAAkB,wBAAgB,iBAAiB;AAAA,YACzD;AAAA,UACF;AAAA,QACF;AACA,2BAAmB,OAAO,cAAc,EAAC,QAAQ,aAAa,MAAM,EAAC,CAAC;AAAA,MACxE;AAAA,IACF;AACA,SAAK,UACH,KAAK,QAAQ,SAAS,IAAI,QAAQ,KAAK,uBAAuB;AAAA,EAClE;AAAA,EAEA,UAAU;AACR,SAAK,MAAM,IAAI;AACf,SAAK,oBAAoB,MAAM;AAC/B,UAAM,QAAQ;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,QAAQ;AACzB,UAAM,qBAAqB,KAAK;AAChC,QAAI;AACJ,uBAAmB,gBAAgB,QAAQ,SAAU,QAAQ;AAC3D,UAAI,OAAO,OAAO,QAAQ,MAAM,GAAG;AACjC,cAAM;AACN,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,QAAI,KAAK;AACP,yBAAmB,OAAO,GAAG;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,SAAS;AACrB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,UAAM,aAAa,OAAO,OAAO;AACjC,QAAI,cAAc,KAAK,uBAAuB;AAC5C,aAAO,KAAK,sBAAsB,UAAU;AAAA,IAC9C,OAAO;AACL,UAAI,KAAK,gBAAgB;AACvB,aAAK,eAAe,OAAO,OAAO;AAAA,MACpC;AAAA,IACF;AACA,UAAM,SAAS,KAAK,sBAAsB,OAAO;AACjD,QAAI,QAAQ;AACV,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,sBAAsB,SAAS;AAC7B,UAAM,aAAa,OAAO,OAAO;AACjC,UAAM,oBAAoB,KAAK,mBAAmB,UAAU;AAC5D,QAAI,CAAC,mBAAmB;AACtB;AAAA,IACF;AACA,sBAAkB,QAAQ,aAAa;AACvC,WAAO,KAAK,mBAAmB,UAAU;AACzC,UAAM,KAAK,QAAQ,MAAM;AACzB,QAAI,OAAO,QAAW;AACpB,aAAO,KAAK,SAAS,GAAG,SAAS,CAAC;AAAA,IACpC;AACA,WAAO,KAAK,UAAU,UAAU;AAChC,SAAK;AAAA,MACH,IAAI,kBAAkB,wBAAgB,eAAe,OAAO;AAAA,IAC9D;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,mBAAmB,SAAS;AAC1B,QAAI,UAAU;AACd,eAAW,MAAM,KAAK,UAAU;AAC9B,UAAI,KAAK,SAAS,EAAE,MAAM,SAAS;AACjC,eAAO,KAAK,SAAS,EAAE;AACvB,kBAAU;AACV;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,KAAK;AACV,WAAO,KAAK,SAAS,CAAC;AACtB,SAAK,OAAO;AACZ,SAAK,UAAU,IAAI,KAAK,KAAK,OAAO,CAAC;AAAA,EACvC;AACF;AAEA,IAAO,iBAAQ;", "names": ["<PERSON>ush", "xhr"]}