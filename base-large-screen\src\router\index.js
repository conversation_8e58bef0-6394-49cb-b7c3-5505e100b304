import { createRouter, createWebHashHistory } from "vue-router";
import routes from "./routes";
import { syncToken } from "@/api/request";
import { useSystemStore, isAuthenticated } from "@/stores/system";
import { dynamicPageInit } from "@/dynamicPage/utils";
import { ElMessage } from "element-plus";

let isInit = true;

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes,
});
// 前置守卫：系统初始化、登录认证
router.beforeEach(async (to, from) => {
  console.log("beforeEach ---------");
  console.log(to);
  if (isInit) {
    isInit = false;
    syncToken(to.query);
    await systemInit();
    if (isAuthenticated) {
      await dynamicPageInit(router, to.query);
      router.addRoute({
        path: "/:pathMatch(.*)*",
        redirect: "/not-found",
      });
      return { ...to };
    }
  }

  if (!isAuthenticated && to.name !== "Login") {
    // 将用户重定向到登录页面
    return { name: "Login" };
  }

  if (from.name === "PagePreview" && to.name !== "PageEdit" && to.name !== "PagePreview") {
    ElMessage.warning("页面切换，自动退出预览模式");
    return { name: "PageEdit", replace: true };
  }

  if (from.name === "PageEdit" && to.name !== "PageEdit" && to.name !== "PagePreview") {
    ElMessage.warning("编辑状态，不可切换页面");
    return { name: "PageEdit", replace: true };
  }
});
//解析守卫：获取页面数据
router.beforeResolve(async (to) => {
  console.log("beforeResolve ---------");
  await advanceGetData(to);
});
//后置钩子：系统loading控制
router.afterEach((to, from) => {
  if (typeof from.name === "undefined") {
    endLoading();
  }
});
//提前获取页面数据
function advanceGetData(to) {
  const { query, meta } = to;
  if (!meta.stores || !meta.stores.length) {
    return;
  }
  const promises = [];
  meta.stores.forEach((useStore) => {
    try {
      const store = useStore();
      if (typeof store.getData === "function") {
        promises.push(store.getData(query, meta.storeKeys));
      }
    } catch (error) {
      console.log(error);
    }
  });
  return Promise.allSettled(promises);
}
//系统参数初始化
async function systemInit() {
  const systemStore = useSystemStore();
  await systemStore.systemStoreInit();
}
export default router;
