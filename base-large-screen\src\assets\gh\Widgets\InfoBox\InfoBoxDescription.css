@import url(../shared.css);
body {
  margin: 0;
  padding: 0;
}

.cesium-infoBox-description {
  font-family: sans-serif;
  font-size: 13px;
  padding: 4px 10px;
  margin-right: 4px;
  color: #edffff;
}

.cesium-infoBox-description a:link,
.cesium-infoBox-description a:visited,
.cesium-infoBox-description a:hover,
.cesium-infoBox-description a:active {
  color: #edffff;
}

.cesium-infoBox-description table {
  color: #edffff;
}

.cesium-infoBox-defaultTable {
  width: 100%;
  color: #edffff;
}

.cesium-infoBox-defaultTable tr:nth-child(odd) {
  background-color: rgba(84, 84, 84, 0.8);
}

.cesium-infoBox-defaultTable tr:nth-child(even) {
  background-color: rgba(84, 84, 84, 0.25);
}

.cesium-infoBox-defaultTable th {
  font-weight: normal;
  padding: 3px;
  vertical-align: middle;
  text-align: center;
}

.cesium-infoBox-defaultTable td {
  padding: 3px;
  vertical-align: middle;
  text-align: left;
}

.cesium-infoBox-description-lighter {
  color: #000000;
}

.cesium-infoBox-description-lighter a:link,
.cesium-infoBox-description-lighter a:visited,
.cesium-infoBox-description-lighter a:hover,
.cesium-infoBox-description-lighter a:active {
  color: #000000;
}

.cesium-infoBox-description-lighter table {
  color: #000000;
}

.cesium-infoBox-defaultTable-lighter {
  width: 100%;
  color: #000000;
}

.cesium-infoBox-defaultTable-lighter tr:nth-child(odd) {
  background-color: rgba(179, 179, 179, 0.8);
}

.cesium-infoBox-defaultTable-lighter tr:nth-child(even) {
  background-color: rgba(179, 179, 179, 0.25);
}

.cesium-infoBox-loadingContainer {
  margin: 5px;
  text-align: center;
}

.cesium-infoBox-loading {
  display: inline-block;
  background-image: url(../Images/info-loading.gif);
  width: 16px;
  height: 11px;
}
