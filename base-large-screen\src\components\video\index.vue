<template>
  <div ref="videoDom"></div>
</template>
<script setup>
import { onMounted, inject, ref } from "vue";
import { SimplePlayer } from 'xgplayer';
import FlvPlugin from 'xgplayer-flv';
import HlsPlugin from 'xgplayer-hls';
// import DashPlugin from 'xgplayer-dash';
import ShakaPlugin from 'xgplayer-shaka';
import 'xgplayer/dist/index.min.css';
import Start from 'xgplayer/es/plugins/start'
import PC from 'xgplayer/es/plugins/pc'
import Progress from 'xgplayer/es/plugins/progress'
import Time from 'xgplayer/es/plugins/time'
import Play from 'xgplayer/es/plugins/play'
import Error from 'xgplayer/es/plugins/error'
import Volume from 'xgplayer/es/plugins/volume'
import Fullscreen from 'xgplayer/es/plugins/fullscreen'

// 测试链接
// flv   https://sf1-cdn-tos.huoshanstatic.com/obj/media-fe/xgplayer_doc_video/flv/xgplayer-demo-360p.flv
// hls   https://sf1-cdn-tos.huoshanstatic.com/obj/media-fe/xgplayer_doc_video/hls/xgplayer-demo.m3u8
// dash  https://sf1-cdn-tos.huoshanstatic.com/obj/media-fe/xgplayer_doc_video/dash/xgplayer-demo-dash.mpd

const videoDom = ref(null)

const props = defineProps({
  src: {
    type: String,
  },
  height: {
    type: Number,
    default: '100%'
  },
  width: {
    type: Number,
    default: '100%'
  },
  flv: {
    type: Boolean,
    default: false,
  },
  hls: {
    type: Boolean,
    default: false,
  },
  dash: {
    type: Boolean,
    default: false,
  },
  poster: {
    type: String
  },
})

let player

function initPlayer() {
  const basePlugins = [Start, PC, Progress, Play, Time, Error, Volume, Fullscreen]
  const baseConfig = {
    el: videoDom.value,
    url: props.src,
    height: props.height,
    width: props.width,
    autoplayMuted: true,
    autoplay: true,
    loop: true
  }
  let config = {}

  if (props.flv) {
    config = {
      ...baseConfig,
      plugins: [...basePlugins, FlvPlugin]
    }
  } else if (props.hls) {
    config = {
      ...baseConfig,
      plugins: [...basePlugins, HlsPlugin]
    }
  } else if (props.dash) {
    config = {
      ...baseConfig,
      plugins: [...basePlugins, ShakaPlugin]
    }
  } else {
    config = {
      ...baseConfig,
      plugins: basePlugins
    }
  }

  player = new SimplePlayer(config);
}


onMounted(() => {
  initPlayer()
})
</script>
<style scoped></style>
