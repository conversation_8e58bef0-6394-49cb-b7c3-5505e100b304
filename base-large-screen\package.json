{"name": "base-large-screen", "version": "2.0.0", "private": true, "scripts": {"dev": "vite --host", "dev:localDynamic": "vite --host --mode localDynamic", "build": "vite build", "build:localDynamic": "vite build --mode localDynamic", "preview": "vite preview"}, "dependencies": {"axios": "^1.4.0", "echarts": "^5.4.2", "element-plus": "^2.4.4", "gsap": "^3.12.2", "jsoneditor": "^10.0.0", "mockjs": "^1.1.0", "ol": "^7.4.0", "pinia": "^2.1.6", "swiper": "^10.0.4", "three": "^0.154.0", "vue": "^3.3.4", "vue-router": "^4.2.4", "xgplayer": "^3.0.6", "xgplayer-flv": "^3.0.6", "xgplayer-hls": "^3.0.6", "xgplayer-shaka": "^3.0.6"}, "devDependencies": {"@vitejs/plugin-basic-ssl": "^1.0.1", "@vitejs/plugin-vue": "^4.2.3", "sass": "^1.64.1", "vite": "^4.4.7", "vite-plugin-mock": "^2.9.6"}}