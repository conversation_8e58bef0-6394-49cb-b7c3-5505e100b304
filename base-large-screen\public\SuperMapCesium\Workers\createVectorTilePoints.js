define(["./AttributeCompression-90851096","./Cartographic-3309dd0d","./Cartesian2-47311507","./Math-119be1a3","./createTaskProcessorWorker","./Check-7b2a090c","./when-b60132fc"],(function(a,r,e,t,n,i,o){"use strict";var s=32767,c=new r.Cartographic,u=new r.Cartesian3,p=new e.Rectangle,h=new e.Ellipsoid,l={min:void 0,max:void 0};return n((function(n,i){var o=new Uint16Array(n.positions);!function(a){a=new Float64Array(a);var r=0;l.min=a[r++],l.max=a[r++],e.Rectangle.unpack(a,r,p),r+=e.Rectangle.packedLength,e.Ellipsoid.unpack(a,r,h)}(n.packedBuffer);var f=p,C=h,d=l.min,g=l.max,m=o.length/3,b=o.subarray(0,m),w=o.subarray(m,2*m),k=o.subarray(2*m,3*m);a.AttributeCompression.zigZagDeltaDecode(b,w,k);for(var v=new Float64Array(o.length),y=0;y<m;++y){var A=b[y],M=w[y],R=k[y],x=t.CesiumMath.lerp(f.west,f.east,A/s),D=t.CesiumMath.lerp(f.south,f.north,M/s),E=t.CesiumMath.lerp(d,g,R/s),F=r.Cartographic.fromRadians(x,D,E,c),T=C.cartographicToCartesian(F,u);r.Cartesian3.pack(T,v,3*y)}return i.push(v.buffer),{positions:v.buffer}}))}));
