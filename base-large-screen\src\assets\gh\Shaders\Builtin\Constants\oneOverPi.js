//This file is automatically rebuilt by the Cesium build process.
export default "/**\n\
 * A built-in GLSL floating-point constant for <code>1/pi</code>.\n\
 *\n\
 * @alias czm_oneOverPi\n\
 * @glslConstant\n\
 *\n\
 * @see CesiumMath.ONE_OVER_PI\n\
 *\n\
 * @example\n\
 * // GLSL declaration\n\
 * const float czm_oneOverPi = ...;\n\
 *\n\
 * // Example\n\
 * float pi = 1.0 / czm_oneOverPi;\n\
 */\n\
const float czm_oneOverPi = 0.3183098861837907;\n\
";
