<template>
  <div class="box">
    <GrainPartTitle title="劳务信息" :position="1" />
    <EchartsContainer :option="chartOption" style="width:420px;height:250px" />
  </div>
</template>
<script setup>
import { shallowRef, inject, onMounted } from "vue";
import GrainPartTitle from "../../components/common/GrainPartTitle.vue"
import EchartsContainer from "../../components/common/EchartsContainer.vue"
import { useGrainStore } from "../../stores/modules/grain";
import arrow from "../../assets/images/arrow_icon.png";

const { pageData } = useGrainStore()
const chartOption = shallowRef({})

const pageTl = inject('pageTl')
const isEnter = inject('isEnter')

function initTl() {
  if (pageTl) {
    pageTl.add(setOption, 1)
  } else {
    setOption()
  }
}

function setOption() {
  if (isEnter && !isEnter.value) {
    return
  }
  const xAxisData = []
  const seriesData1 = []
  let seriesData2 = []
  let total = 0
  if (pageData.staff && pageData.staff.length) {
    pageData.staff.forEach(item => {
      xAxisData.push(item.key)
      seriesData1.push(item.value)
      total += item.value
    })
  }
  seriesData2 = seriesData1.map(n => Math.ceil(n / total * 100))

  chartOption.value = {
    title: {
      text: "劳务信息人数",
      textStyle: {
        color: "#3266B2",
        fontSize: 15,
      },
      left: "center",
      top: "5"
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b0}<br />{a0} : {c0}<br />{a1} : {c1}%',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    grid: {
      left: '20',
      right: '20',
      top: '40',
      bottom: '10',
      containLabel: true
    },
    xAxis: [
      {
        show: true,
        type: 'category',
        boundaryGap: true,
        axisLabel: {
          color: "#ADDDFF",
          fontSize: 12,
        },
        data: xAxisData
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: "人数",
        nameTextStyle: {
          fontSize: 14,
          color: "#CEDCE6"
        },
        max: total,
        axisLabel: {
          color: "#fffff",
          fontSize: 12,
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: '#245085'
          }
        }
      },
      {
        type: 'value',
        name: "百分比",
        nameTextStyle: {
          fontSize: 14,
          color: "#CEDCE6"
        },
        min: 0,
        max: 100,
        interval: 25,
        axisLabel: {
          formatter: '{value}%',
          color: "#ffffff",
          fontSize: 12,
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '人数',
        type: 'pictorialBar',
        barCategoryGap: '-20%',
        symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
        itemStyle: {
          color: "#005EA1",
          borderColor: "#FFFFFF",
          borderWidth: "1",
          opacity: 0.8
        },
        emphasis: {
          itemStyle: {
            opacity: 1
          }
        },
        label: {
          show: true,
          position: 'top',
          distance: 17,
          fontSize: 12,
          color: "#3266B2",
        },
        data: seriesData1
      },
      {
        name: '百分比',
        type: 'scatter',
        yAxisIndex: 1,
        symbol: `image://${arrow}`,
        symbolOffset: [0, -9],
        symbolSize: 14,
        data: seriesData2
      }
    ]
  }
}

onMounted(() => {
  initTl()
})
</script>
<style scoped lang="scss">
.box {
  height: 290px;
  width: 450px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>