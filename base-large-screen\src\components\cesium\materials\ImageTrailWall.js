import arrowPng from "../../../assets/images/arrow.png";
const defaultColor = Cesium.Color.DEEPSKYBLUE;
const defaultSpeed = 20;

function ImageTrailWallMaterialProperty(options) {
  options = Cesium.defaultValue(options, Cesium.defaultValue.EMPTY_OBJECT);

  this._definitionChanged = new Cesium.Event();
  this._color = undefined;
  this._colorSubscription = undefined;
  this._speed = undefined;
  this._speedSubscription = undefined;

  this.color = options.color;
  this.speed = options.speed;
}

Object.defineProperties(ImageTrailWallMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return Cesium.Property.isConstant(this._color) && Cesium.Property.isConstant(this._speed);
    },
  },
  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    },
  },
  color: Cesium.createPropertyDescriptor("color"),
  speed: Cesium.createPropertyDescriptor("speed"),
});

ImageTrailWallMaterialProperty.prototype.getType = function (time) {
  return "ImageTrailWall";
};

ImageTrailWallMaterialProperty.prototype.getValue = function (time, result) {
  if (!Cesium.defined(result)) {
    result = {};
  }
  result.color = Cesium.Property.getValueOrClonedDefault(this._color, time, defaultColor, result.color);
  result.speed = Cesium.Property.getValueOrClonedDefault(this._speed, time, defaultSpeed, result.speed);
  return result;
};

ImageTrailWallMaterialProperty.prototype.equals = function (other) {
  return (
    this === other || //
    (other instanceof ImageTrailWallMaterialProperty && //
      Cesium.Property.equals(this._color, other._color) && //
      Cesium.Property.equals(this._speed, other._speed))
  );
};

Cesium.ImageTrailWallMaterialProperty = ImageTrailWallMaterialProperty;

const type = "ImageTrailWall";

const source = `
uniform sampler2D image;
uniform vec4 color;
uniform float speed;
uniform vec2 repeat;
czm_material czm_getMaterial(czm_materialInput materialInput){
  czm_material material = czm_getDefaultMaterial(materialInput);
  vec2 st = materialInput.st * repeat;
  float time = fract(czm_frameNumber * speed / 1000.0);
  vec4 colorImage = texture2D(image, vec2(fract(st.s - time), st.t));
  material.alpha =  colorImage.a * color.a ;
  material.diffuse = colorImage.rgb * color.rgb * 3.0 ;
  return material;
}
`;

Cesium.Material._materialCache.addMaterial(type, {
  fabric: {
    type,
    uniforms: {
      color: defaultColor,
      image: arrowPng,
      speed: defaultSpeed,
      repeat: new Cesium.Cartesian2(100, 1),
    },
    source,
  },
  translucent: function (material) {
    return true;
  },
});
