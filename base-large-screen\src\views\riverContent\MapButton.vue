<template>
  <div class="box">
    <div v-for="(item, index) in mapStore.markData" ref="buttonRefs" :tlPosition="index * 0.3"
      :class="['button', item.visible ? 'active' : 'passive']" @click="markChange(index)">{{ item.name }}</div>
  </div>
</template>
<script setup>
import { onMounted, onUnmounted, ref, inject } from "vue";
import gsap from "gsap";
import { useMapStore } from "../../stores/modules/map";
import { ANIMATIONS } from '../../config'

const mapStore = useMapStore();
function markChange(index) {
  const newData = [...mapStore.markData]
  newData[index] = { ...mapStore.markData[index], visible: !mapStore.markData[index].visible }
  mapStore.setMarkData(newData)
}

const buttonRefs = ref([])

const pageTl = inject('pageTl')

function enterTl() {
  const startTime = 0.5
  const { from = {}, to = {} } = ANIMATIONS.ShrinkShow
  buttonRefs.value.forEach(b => {
    const tlPosition = b.getAttribute('tlPosition')
    if (typeof tlPosition !== 'undefined') {
      const tween = gsap.fromTo(b, from, to)
      pageTl.add(tween, startTime + tlPosition)
    }
  })
}

onMounted(() => {
  enterTl()
})
</script>
<style scoped>
.box {
  display: flex;
  flex-direction: column;
  width: 167px;
}

.button {
  width: 167px;
  height: 64px;
  cursor: pointer;
  font-size: 16px;
  font-family: Source Han Sans SC;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 14px;
  padding-left: 21px;
  margin-top: 8px;
}

.passive {
  background-image: url('../../assets/images/button_bg.png');
  background-size: 100% 100%;
  color: #ffffffbf;
}

.active {
  background-image: url('../../assets/images/button_bg_active.png');
  background-size: 100% 100%;
  color: #FFFFFF;
}
</style>
