<template>
  <div class="box">
    <GrainPartTitle title="物流信息" :position="0.5" />
    <div class="item">
      <img class="icon" src="../../assets/images/car_icon.png" alt="icon">
      <div class="right">
        <div class="bg">
          <div class="title">车辆运输</div>
        </div>
        <div class="line" v-if="pageData.logistics && pageData.logistics.car">
          <div class="text">在线:<span style="font-size: 18px;color: #32DCFB;">{{ pageData.logistics.car.online }}</span>
          </div>
          <div class="text">离线:<span style="font-size: 18px;color: #798F9A;">{{ pageData.logistics.car.offline }}</span>
          </div>
          <div class="text">在线率:<span style="font-size: 18px;color: #32DCFB;">{{ getRate(pageData.logistics.car) }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="item">
      <img class="icon" src="../../assets/images/container_icon.png" alt="icon">
      <div class="right">
        <div class="bg">
          <div class="title">集装箱数量</div>
        </div>
        <div class="line" v-if="pageData.logistics && pageData.logistics.container">
          <div class="text">在线:<span style="font-size: 18px;color: #32DCFB;">{{ pageData.logistics.container.online
          }}</span></div>
          <div class="text">离线:<span style="font-size: 18px;color: #798F9A;">{{ pageData.logistics.container.offline
          }}</span></div>
          <div class="text">在线率:<span style="font-size: 18px;color: #32DCFB;">{{ getRate(pageData.logistics.container)
          }}</span></div>
        </div>
      </div>
    </div>
    <div class="item">
      <img class="icon" src="../../assets/images/car_warn_icon.png" alt="icon">
      <div class="right">
        <div class="bg">
          <div class="title">车辆预警</div>
        </div>
        <div class="line" v-if="pageData.logistics && pageData.logistics.carWarn">
          <div class="text">正常:<span style="font-size: 18px;color: #32DCFB;">{{ pageData.logistics.carWarn.normal
          }}</span></div>
          <div class="text">异常:<span style="font-size: 18px;color: #C20000;">{{ pageData.logistics.carWarn.abnormal
          }}</span></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import GrainPartTitle from "../../components/common/GrainPartTitle.vue"
import { useGrainStore } from "../../stores/modules/grain";

const { pageData } = useGrainStore()

function getRate(item) {
  try {
    return `${(item.online / (item.online + item.offline) * 100).toFixed(1)}%`
  } catch {
    return '100%'
  }
}

</script>
<style scoped lang="scss">
.box {
  height: 389px;
  width: 450px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.item {
  width: 450px;
  height: 85px;
  display: flex;
  align-items: center;
  padding-left: 49px;
  margin-top: 23px;

  .icon {
    width: 76px;
    height: 85px;
    object-fit: contain;
    margin-right: 23px;
    flex-shrink: 0;
  }

  .right {
    width: 270px;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }

  .bg {
    width: 100%;
    height: 24px;
    background: linear-gradient(to right, #367bc900 0%, #367bc9a6 30%, #367bc900 80%);
  }

  .title {
    width: 100%;
    height: 24px;
    font-size: 18px;
    font-family: Alibaba PuHuiTi;
    font-weight: 500;
    font-style: italic;
    color: #D1D6DF;
    line-height: 24px;

    background: linear-gradient(0deg, #ACDDFF 0%, #FFFFFF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .line {
    width: 100%;
    height: 18px;
    display: flex;
    align-items: center;

    .text {
      font-size: 16px;
      line-height: 18px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #CEDCE6;
      flex: 1;
      white-space: nowrap;
    }
  }
}
</style>