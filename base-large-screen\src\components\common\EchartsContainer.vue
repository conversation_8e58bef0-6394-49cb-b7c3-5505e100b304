<template>
  <div ref="dom" class="container">
  </div>
</template>
<script setup>
import { watch, onMounted, onBeforeUnmount, inject, ref, onUpdated } from "vue";
import * as echarts from 'echarts';
const props = defineProps({
  option: {
    type: Object,
    default: {}
  }
})

const dom = ref(null)

let chart;

defineExpose({
  chart
})

function initChart() {
  chart = echarts.init(dom.value);
}
function initOption() {
  if (JSON.stringify(props.option) !== "{}") {
    updateOption(props.option)
  }
}
function updateOption(option) {
  if (!chart) {
    return
  }
  chart.clear()
  chart.setOption(option)
}
function disposeChart() {
  if (chart) {
    chart.dispose()
  }
}
function resizeChart() {
  if (chart) {
    chart.resize()
  }
}
watch(
  () => props.option,
  (newOption, oldOption) => {
    try {
      if (JSON.stringify(newOption) !== JSON.stringify(oldOption)) {
        updateOption(newOption)
      }
    } catch (error) {
      console.log(error)
    }
  })

let resizeObserver

function initObserver() {
  let running = false;
  resizeObserver = new ResizeObserver((entries) => {
    if (!entries || !entries[0]) {
      return
    }
    if (running) {
      return;
    }
    running = true;
    requestAnimationFrame(() => {
      resizeChart()
      running = false;
    });
  });
  resizeObserver.observe(dom.value);
}
function disposeObserver() {
  resizeObserver.unobserve(dom.value);
}
onMounted(() => {
  initChart()
  initObserver()
  initOption()
})
onBeforeUnmount(() => {
  disposeChart()
  disposeObserver()
})
</script>
<style scoped>
.container {
  width: 400px;
  height: 300px;
}
</style>
