<template>
  <div class="box">
    <GrainPartTitle title="车辆数量" :position="1.5" />
    <div class="item bg1">
      <div class="name">车辆使用数量</div>
      <GrowNumber class="value color1" :value="pageData.car && pageData.car.use" :position="1.5" />
    </div>
    <div class="item bg2">
      <div class="name">车辆闲置数量</div>
      <GrowNumber class="value color2" :value="pageData.car && pageData.car.unused" :position="1.5" />
    </div>
  </div>
</template>
<script setup>
import GrainPartTitle from "../../components/common/GrainPartTitle.vue"
import { useGrainStore } from "../../stores/modules/grain";
import GrowNumber from "../../components/common/GrowNumber.vue"

const { pageData } = useGrainStore()

</script>
<style scoped lang="scss">
.box {
  height: 252px;
  width: 450px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.item {
  width: 330px;
  height: 89px;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  margin-left: 40px;
  margin-top: 11px;

  .name {
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 72px;
    text-shadow: 1px 2px 0px rgba(17, 20, 22, 0.22);
    margin-right: 26px;
  }

  .value {
    width: 90px;
    font-size: 34px;
    line-height: 72px;
    font-family: YouSheBiaoTiHei;
    font-weight: 400;
  }

  .color1 {
    background: linear-gradient(80deg, rgba(29, 126, 224, 0.734) 0%, rgb(255, 255, 255) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .color2 {
    background: linear-gradient(80deg, rgba(3, 203, 180, 0.734) 0%, rgb(255, 255, 255) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.bg1 {
  background-image: url('../../assets/images/item2_bg.png');
  background-size: 100% 100%;
}

.bg2 {
  background-image: url('../../assets/images/item3_bg.png');
  background-size: 100% 100%;
}
</style>