{"version": 3, "sources": ["../../xgplayer-flv/es/_virtual/_rollupPluginBabelHelpers.js", "../../xgplayer-flv/es/flv/index.js", "../../xgplayer-flv/es/flv/services/buffer-service.js", "../../xgplayer-flv/es/flv/options.js", "../../xgplayer-flv/es/flv/utils.js", "../../xgplayer-flv/es/plugin-extension.js", "../../xgplayer-flv/es/plugin.js"], "sourcesContent": ["function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function(sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), true).forEach(function(key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _regeneratorRuntime() {\n  _regeneratorRuntime = function() {\n    return exports;\n  };\n  var exports = {}, Op = Object.prototype, hasOwn = Op.hasOwnProperty, defineProperty = Object.defineProperty || function(obj, key, desc) {\n    obj[key] = desc.value;\n  }, $Symbol = \"function\" == typeof Symbol ? Symbol : {}, iteratorSymbol = $Symbol.iterator || \"@@iterator\", asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\", toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n  function define(obj, key, value) {\n    return Object.defineProperty(obj, key, {\n      value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    }), obj[key];\n  }\n  try {\n    define({}, \"\");\n  } catch (err) {\n    define = function(obj, key, value) {\n      return obj[key] = value;\n    };\n  }\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator, generator = Object.create(protoGenerator.prototype), context = new Context(tryLocsList || []);\n    return defineProperty(generator, \"_invoke\", {\n      value: makeInvokeMethod(innerFn, self, context)\n    }), generator;\n  }\n  function tryCatch(fn, obj, arg) {\n    try {\n      return {\n        type: \"normal\",\n        arg: fn.call(obj, arg)\n      };\n    } catch (err) {\n      return {\n        type: \"throw\",\n        arg: err\n      };\n    }\n  }\n  exports.wrap = wrap;\n  var ContinueSentinel = {};\n  function Generator() {\n  }\n  function GeneratorFunction() {\n  }\n  function GeneratorFunctionPrototype() {\n  }\n  var IteratorPrototype = {};\n  define(IteratorPrototype, iteratorSymbol, function() {\n    return this;\n  });\n  var getProto = Object.getPrototypeOf, NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  NativeIteratorPrototype && NativeIteratorPrototype !== Op && hasOwn.call(NativeIteratorPrototype, iteratorSymbol) && (IteratorPrototype = NativeIteratorPrototype);\n  var Gp = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(IteratorPrototype);\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function(method) {\n      define(prototype, method, function(arg) {\n        return this._invoke(method, arg);\n      });\n    });\n  }\n  function AsyncIterator(generator, PromiseImpl) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (\"throw\" !== record.type) {\n        var result = record.arg, value = result.value;\n        return value && \"object\" == typeof value && hasOwn.call(value, \"__await\") ? PromiseImpl.resolve(value.__await).then(function(value2) {\n          invoke(\"next\", value2, resolve, reject);\n        }, function(err) {\n          invoke(\"throw\", err, resolve, reject);\n        }) : PromiseImpl.resolve(value).then(function(unwrapped) {\n          result.value = unwrapped, resolve(result);\n        }, function(error) {\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n      reject(record.arg);\n    }\n    var previousPromise;\n    defineProperty(this, \"_invoke\", {\n      value: function(method, arg) {\n        function callInvokeWithMethodAndArg() {\n          return new PromiseImpl(function(resolve, reject) {\n            invoke(method, arg, resolve, reject);\n          });\n        }\n        return previousPromise = previousPromise ? previousPromise.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg();\n      }\n    });\n  }\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = \"suspendedStart\";\n    return function(method, arg) {\n      if (\"executing\" === state)\n        throw new Error(\"Generator is already running\");\n      if (\"completed\" === state) {\n        if (\"throw\" === method)\n          throw arg;\n        return doneResult();\n      }\n      for (context.method = method, context.arg = arg; ; ) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel)\n              continue;\n            return delegateResult;\n          }\n        }\n        if (\"next\" === context.method)\n          context.sent = context._sent = context.arg;\n        else if (\"throw\" === context.method) {\n          if (\"suspendedStart\" === state)\n            throw state = \"completed\", context.arg;\n          context.dispatchException(context.arg);\n        } else\n          \"return\" === context.method && context.abrupt(\"return\", context.arg);\n        state = \"executing\";\n        var record = tryCatch(innerFn, self, context);\n        if (\"normal\" === record.type) {\n          if (state = context.done ? \"completed\" : \"suspendedYield\", record.arg === ContinueSentinel)\n            continue;\n          return {\n            value: record.arg,\n            done: context.done\n          };\n        }\n        \"throw\" === record.type && (state = \"completed\", context.method = \"throw\", context.arg = record.arg);\n      }\n    };\n  }\n  function maybeInvokeDelegate(delegate, context) {\n    var methodName = context.method, method = delegate.iterator[methodName];\n    if (void 0 === method)\n      return context.delegate = null, \"throw\" === methodName && delegate.iterator.return && (context.method = \"return\", context.arg = void 0, maybeInvokeDelegate(delegate, context), \"throw\" === context.method) || \"return\" !== methodName && (context.method = \"throw\", context.arg = new TypeError(\"The iterator does not provide a '\" + methodName + \"' method\")), ContinueSentinel;\n    var record = tryCatch(method, delegate.iterator, context.arg);\n    if (\"throw\" === record.type)\n      return context.method = \"throw\", context.arg = record.arg, context.delegate = null, ContinueSentinel;\n    var info = record.arg;\n    return info ? info.done ? (context[delegate.resultName] = info.value, context.next = delegate.nextLoc, \"return\" !== context.method && (context.method = \"next\", context.arg = void 0), context.delegate = null, ContinueSentinel) : info : (context.method = \"throw\", context.arg = new TypeError(\"iterator result is not an object\"), context.delegate = null, ContinueSentinel);\n  }\n  function pushTryEntry(locs) {\n    var entry = {\n      tryLoc: locs[0]\n    };\n    1 in locs && (entry.catchLoc = locs[1]), 2 in locs && (entry.finallyLoc = locs[2], entry.afterLoc = locs[3]), this.tryEntries.push(entry);\n  }\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\", delete record.arg, entry.completion = record;\n  }\n  function Context(tryLocsList) {\n    this.tryEntries = [{\n      tryLoc: \"root\"\n    }], tryLocsList.forEach(pushTryEntry, this), this.reset(true);\n  }\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod)\n        return iteratorMethod.call(iterable);\n      if (\"function\" == typeof iterable.next)\n        return iterable;\n      if (!isNaN(iterable.length)) {\n        var i = -1, next = function next2() {\n          for (; ++i < iterable.length; )\n            if (hasOwn.call(iterable, i))\n              return next2.value = iterable[i], next2.done = false, next2;\n          return next2.value = void 0, next2.done = true, next2;\n        };\n        return next.next = next;\n      }\n    }\n    return {\n      next: doneResult\n    };\n  }\n  function doneResult() {\n    return {\n      value: void 0,\n      done: true\n    };\n  }\n  return GeneratorFunction.prototype = GeneratorFunctionPrototype, defineProperty(Gp, \"constructor\", {\n    value: GeneratorFunctionPrototype,\n    configurable: true\n  }), defineProperty(GeneratorFunctionPrototype, \"constructor\", {\n    value: GeneratorFunction,\n    configurable: true\n  }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, toStringTagSymbol, \"GeneratorFunction\"), exports.isGeneratorFunction = function(genFun) {\n    var ctor = \"function\" == typeof genFun && genFun.constructor;\n    return !!ctor && (ctor === GeneratorFunction || \"GeneratorFunction\" === (ctor.displayName || ctor.name));\n  }, exports.mark = function(genFun) {\n    return Object.setPrototypeOf ? Object.setPrototypeOf(genFun, GeneratorFunctionPrototype) : (genFun.__proto__ = GeneratorFunctionPrototype, define(genFun, toStringTagSymbol, \"GeneratorFunction\")), genFun.prototype = Object.create(Gp), genFun;\n  }, exports.awrap = function(arg) {\n    return {\n      __await: arg\n    };\n  }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, asyncIteratorSymbol, function() {\n    return this;\n  }), exports.AsyncIterator = AsyncIterator, exports.async = function(innerFn, outerFn, self, tryLocsList, PromiseImpl) {\n    void 0 === PromiseImpl && (PromiseImpl = Promise);\n    var iter = new AsyncIterator(wrap(innerFn, outerFn, self, tryLocsList), PromiseImpl);\n    return exports.isGeneratorFunction(outerFn) ? iter : iter.next().then(function(result) {\n      return result.done ? result.value : iter.next();\n    });\n  }, defineIteratorMethods(Gp), define(Gp, toStringTagSymbol, \"Generator\"), define(Gp, iteratorSymbol, function() {\n    return this;\n  }), define(Gp, \"toString\", function() {\n    return \"[object Generator]\";\n  }), exports.keys = function(val) {\n    var object = Object(val), keys = [];\n    for (var key in object)\n      keys.push(key);\n    return keys.reverse(), function next() {\n      for (; keys.length; ) {\n        var key2 = keys.pop();\n        if (key2 in object)\n          return next.value = key2, next.done = false, next;\n      }\n      return next.done = true, next;\n    };\n  }, exports.values = values, Context.prototype = {\n    constructor: Context,\n    reset: function(skipTempReset) {\n      if (this.prev = 0, this.next = 0, this.sent = this._sent = void 0, this.done = false, this.delegate = null, this.method = \"next\", this.arg = void 0, this.tryEntries.forEach(resetTryEntry), !skipTempReset)\n        for (var name in this)\n          \"t\" === name.charAt(0) && hasOwn.call(this, name) && !isNaN(+name.slice(1)) && (this[name] = void 0);\n    },\n    stop: function() {\n      this.done = true;\n      var rootRecord = this.tryEntries[0].completion;\n      if (\"throw\" === rootRecord.type)\n        throw rootRecord.arg;\n      return this.rval;\n    },\n    dispatchException: function(exception) {\n      if (this.done)\n        throw exception;\n      var context = this;\n      function handle(loc, caught) {\n        return record.type = \"throw\", record.arg = exception, context.next = loc, caught && (context.method = \"next\", context.arg = void 0), !!caught;\n      }\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i], record = entry.completion;\n        if (\"root\" === entry.tryLoc)\n          return handle(\"end\");\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\"), hasFinally = hasOwn.call(entry, \"finallyLoc\");\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc)\n              return handle(entry.catchLoc, true);\n            if (this.prev < entry.finallyLoc)\n              return handle(entry.finallyLoc);\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc)\n              return handle(entry.catchLoc, true);\n          } else {\n            if (!hasFinally)\n              throw new Error(\"try statement without catch or finally\");\n            if (this.prev < entry.finallyLoc)\n              return handle(entry.finallyLoc);\n          }\n        }\n      }\n    },\n    abrupt: function(type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev && hasOwn.call(entry, \"finallyLoc\") && this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n      finallyEntry && (\"break\" === type || \"continue\" === type) && finallyEntry.tryLoc <= arg && arg <= finallyEntry.finallyLoc && (finallyEntry = null);\n      var record = finallyEntry ? finallyEntry.completion : {};\n      return record.type = type, record.arg = arg, finallyEntry ? (this.method = \"next\", this.next = finallyEntry.finallyLoc, ContinueSentinel) : this.complete(record);\n    },\n    complete: function(record, afterLoc) {\n      if (\"throw\" === record.type)\n        throw record.arg;\n      return \"break\" === record.type || \"continue\" === record.type ? this.next = record.arg : \"return\" === record.type ? (this.rval = this.arg = record.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === record.type && afterLoc && (this.next = afterLoc), ContinueSentinel;\n    },\n    finish: function(finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc)\n          return this.complete(entry.completion, entry.afterLoc), resetTryEntry(entry), ContinueSentinel;\n      }\n    },\n    catch: function(tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (\"throw\" === record.type) {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n      throw new Error(\"illegal catch attempt\");\n    },\n    delegateYield: function(iterable, resultName, nextLoc) {\n      return this.delegate = {\n        iterator: values(iterable),\n        resultName,\n        nextLoc\n      }, \"next\" === this.method && (this.arg = void 0), ContinueSentinel;\n    }\n  }, exports;\n}\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n  try {\n    var info = gen[key](arg);\n    var value = info.value;\n  } catch (error) {\n    reject(error);\n    return;\n  }\n  if (info.done) {\n    resolve(value);\n  } else {\n    Promise.resolve(value).then(_next, _throw);\n  }\n}\nfunction _asyncToGenerator(fn) {\n  return function() {\n    var self = this, args = arguments;\n    return new Promise(function(resolve, reject) {\n      var gen = fn.apply(self, args);\n      function _next(value) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n      }\n      function _throw(err) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n      }\n      _next(void 0);\n    });\n  };\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor)\n      descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps)\n    _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps)\n    _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass)\n    _setPrototypeOf(subClass, superClass);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf2(o2) {\n    return o2.__proto__ || Object.getPrototypeOf(o2);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf2(o2, p2) {\n    o2.__proto__ = p2;\n    return o2;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct)\n    return false;\n  if (Reflect.construct.sham)\n    return false;\n  if (typeof Proxy === \"function\")\n    return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {\n    }));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived), result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null)\n    return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== void 0) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\")\n      return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\nexport { _assertThisInitialized as assertThisInitialized, _asyncToGenerator as asyncToGenerator, _classCallCheck as classCallCheck, _createClass as createClass, _createSuper as createSuper, _defineProperty as defineProperty, _getPrototypeOf as getPrototypeOf, _inherits as inherits, _isNativeReflectConstruct as isNativeReflectConstruct, _objectSpread2 as objectSpread2, _possibleConstructorReturn as possibleConstructorReturn, _regeneratorRuntime as regeneratorRuntime, _setPrototypeOf as setPrototypeOf, _toPrimitive as toPrimitive, _toPropertyKey as toPropertyKey };\n", "import { inherits as _inherits, createSuper as _createSuper, createClass as _createClass, classCallCheck as _classCallCheck, defineProperty as _defineProperty, assertThisInitialized as _assertThisInitialized, asyncToGenerator as _asyncToGenerator, objectSpread2 as _objectSpread2, regeneratorRuntime as _regeneratorRuntime } from \"../_virtual/_rollupPluginBabelHelpers.js\";\nimport EventEmitter from \"eventemitter3\";\nimport { Logger, EVENT, StreamingError, Buffer, isMediaPlaying, NetLoader, SeiService, BandwidthService, MediaStatsService, GapService, MSE, getVideoPlaybackQuality } from \"xgplayer-streaming-shared\";\nimport { Logger as Logger$1 } from \"xgplayer-transmuxer\";\nimport { BufferService } from \"./services/buffer-service.js\";\nimport { getOption } from \"./options.js\";\nimport { searchKeyframeIndex } from \"./utils.js\";\nvar logger = new Logger(\"flv\");\nvar Flv = /* @__PURE__ */ function(_EventEmitter) {\n  _inherits(Flv2, _EventEmitter);\n  var _super = _createSuper(Flv2);\n  function Flv2(_opts) {\n    var _this;\n    _classCallCheck(this, Flv2);\n    _this = _super.call(this);\n    _defineProperty(_assertThisInitialized(_this), \"media\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_loading\", false);\n    _defineProperty(_assertThisInitialized(_this), \"_opts\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_bufferService\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_gapService\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_stats\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_mediaLoader\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_maxChunkWaitTimer\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_tickTimer\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_tickInterval\", 500);\n    _defineProperty(_assertThisInitialized(_this), \"_urlSwitching\", false);\n    _defineProperty(_assertThisInitialized(_this), \"_seamlessSwitching\", false);\n    _defineProperty(_assertThisInitialized(_this), \"_keyframes\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_acceptRanges\", true);\n    _defineProperty(_assertThisInitialized(_this), \"_onProgress\", /* @__PURE__ */ function() {\n      var _ref2 = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee(chunk, done, _ref, response) {\n        var startTime, endTime, st, firstByteTime, _this$_mediaLoader, headers, _this$_bufferService, maxReaderInterval;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1)\n            switch (_context.prev = _context.next) {\n              case 0:\n                startTime = _ref.startTime, endTime = _ref.endTime, st = _ref.st, firstByteTime = _ref.firstByteTime;\n                _this._loading = !done;\n                if (_this._firstProgressEmit) {\n                  _context.next = 11;\n                  break;\n                }\n                if (_this.media) {\n                  _context.next = 6;\n                  break;\n                }\n                (_this$_mediaLoader = _this._mediaLoader) === null || _this$_mediaLoader === void 0 ? void 0 : _this$_mediaLoader.cancel();\n                return _context.abrupt(\"return\");\n              case 6:\n                headers = response.headers;\n                _this.emit(EVENT.TTFB, {\n                  url: _this._opts.url,\n                  responseUrl: response.url,\n                  elapsed: st ? firstByteTime - st : endTime - startTime\n                });\n                _this.emit(EVENT.LOAD_RESPONSE_HEADERS, {\n                  headers\n                });\n                _this._acceptRanges = !!(headers !== null && headers !== void 0 && headers.get(\"Accept-Ranges\")) || !!(headers !== null && headers !== void 0 && headers.get(\"Content-Range\"));\n                _this._firstProgressEmit = true;\n              case 11:\n                if (_this._bufferService) {\n                  _context.next = 13;\n                  break;\n                }\n                return _context.abrupt(\"return\");\n              case 13:\n                clearTimeout(_this._maxChunkWaitTimer);\n                _this._bandwidthService.addChunkRecord(chunk === null || chunk === void 0 ? void 0 : chunk.byteLength, endTime - startTime);\n                _context.prev = 15;\n                _context.next = 18;\n                return _this._bufferService.appendBuffer(chunk);\n              case 18:\n                (_this$_bufferService = _this._bufferService) === null || _this$_bufferService === void 0 ? void 0 : _this$_bufferService.evictBuffer(_this._opts.bufferBehind);\n                _context.next = 24;\n                break;\n              case 21:\n                _context.prev = 21;\n                _context.t0 = _context[\"catch\"](15);\n                return _context.abrupt(\"return\", _this._emitError(StreamingError.create(_context.t0)));\n              case 24:\n                if (_this._urlSwitching) {\n                  _this._urlSwitching = false;\n                  _this.emit(EVENT.SWITCH_URL_SUCCESS, {\n                    url: _this._opts.url\n                  });\n                }\n                if (_this._seamlessSwitching) {\n                  _this._seamlessSwitching = false;\n                  _this._tick();\n                }\n                if (!(done && !_this.media.seeking)) {\n                  _context.next = 32;\n                  break;\n                }\n                _this.emit(EVENT.LOAD_COMPLETE);\n                logger.debug(\"load done\");\n                return _context.abrupt(\"return\", _this._end());\n              case 32:\n                maxReaderInterval = _this._opts.maxReaderInterval;\n                if (maxReaderInterval) {\n                  clearTimeout(_this._maxChunkWaitTimer);\n                  _this._maxChunkWaitTimer = setTimeout(function() {\n                    logger.debug(\"onMaxChunkWait\", maxReaderInterval);\n                    _this._end();\n                  }, maxReaderInterval);\n                }\n              case 34:\n              case \"end\":\n                return _context.stop();\n            }\n        }, _callee, null, [[15, 21]]);\n      }));\n      return function(_x, _x2, _x3, _x4) {\n        return _ref2.apply(this, arguments);\n      };\n    }());\n    _defineProperty(_assertThisInitialized(_this), \"_onRetryError\", function(error, retryTime) {\n      logger.debug(\"load retry\", error, retryTime);\n      _this.emit(EVENT.LOAD_RETRY, {\n        error: StreamingError.network(error),\n        retryTime\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"_end\", function() {\n      _this._clear();\n      if (_this._bufferService) {\n        _this._bufferService.endOfStream();\n      }\n      logger.debug(\"end stream\");\n    });\n    _defineProperty(_assertThisInitialized(_this), \"_tick\", function() {\n      clearTimeout(_this._tickTimer);\n      var _assertThisInitialize = _assertThisInitialized(_this), media = _assertThisInitialize.media;\n      if (!media)\n        return;\n      _this._tickTimer = setTimeout(_this._tick, _this._tickInterval);\n      var bufferEnd = Buffer.end(Buffer.get(media));\n      if (bufferEnd < 0.1 || !media.readyState)\n        return;\n      var opts = _this._opts;\n      if (isMediaPlaying(media)) {\n        if (_this._gapService) {\n          _this._gapService.do(media, opts.maxJumpDistance, _this.isLive, 3);\n        }\n      } else {\n        if (!media.currentTime && _this._gapService) {\n          _this._gapService.do(media, opts.maxJumpDistance, _this.isLive, 3);\n          return;\n        }\n        if (opts.isLive && bufferEnd > opts.disconnectTime) {\n          _this.disconnect();\n        }\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"_onPlay\", function() {\n      var _this$media, _this$media$buffered;\n      var canReplay = _this._opts.softDecode || ((_this$media = _this.media) === null || _this$media === void 0 ? void 0 : (_this$media$buffered = _this$media.buffered) === null || _this$media$buffered === void 0 ? void 0 : _this$media$buffered.length);\n      if (_this.isLive && !_this._loading && canReplay) {\n        _this.replay(void 0, true);\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"_onSeeking\", /* @__PURE__ */ _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee2() {\n      var times, filepositions, currentTime, i, startByte;\n      return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n        while (1)\n          switch (_context2.prev = _context2.next) {\n            case 0:\n              if (!(!_this.isLive && _this.seekable)) {\n                _context2.next = 14;\n                break;\n              }\n              times = _this._keyframes.times;\n              filepositions = _this._keyframes.filepositions;\n              if (!(!(times !== null && times !== void 0 && times.length) || !(filepositions !== null && filepositions !== void 0 && filepositions.length))) {\n                _context2.next = 5;\n                break;\n              }\n              return _context2.abrupt(\"return\");\n            case 5:\n              currentTime = _this.media.currentTime;\n              i = searchKeyframeIndex(_this._keyframes.times, currentTime);\n              startByte = filepositions[i];\n              if (!(startByte === null || startByte === void 0)) {\n                _context2.next = 10;\n                break;\n              }\n              return _context2.abrupt(\"return\");\n            case 10:\n              _context2.next = 12;\n              return _this._mediaLoader.cancel();\n            case 12:\n              _this._loadData(null, [startByte]);\n              _this._bufferService.unContiguous(times[i]);\n            case 14:\n            case \"end\":\n              return _context2.stop();\n          }\n      }, _callee2);\n    })));\n    _defineProperty(_assertThisInitialized(_this), \"_onTimeupdate\", function() {\n      if (!_this.media)\n        return;\n      var opts = _this._opts;\n      var currentTime = _this.media.currentTime;\n      if (opts.isLive && opts.maxLatency && opts.targetLatency) {\n        var bufferEnd = Buffer.end(Buffer.get(_this.media));\n        var latency = bufferEnd - currentTime;\n        if (latency >= opts.maxLatency) {\n          _this.media.currentTime = bufferEnd - opts.targetLatency;\n        }\n      }\n      _this._seiService.throw(currentTime);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"_onFlvScriptData\", function(sample) {\n      var _sample$data, _sample$data$onMetaDa, _sample$data2, _sample$data2$onMetaD;\n      var keyframes = (_sample$data = sample.data) === null || _sample$data === void 0 ? void 0 : (_sample$data$onMetaDa = _sample$data.onMetaData) === null || _sample$data$onMetaDa === void 0 ? void 0 : _sample$data$onMetaDa.keyframes;\n      var duration = (_sample$data2 = sample.data) === null || _sample$data2 === void 0 ? void 0 : (_sample$data2$onMetaD = _sample$data2.onMetaData) === null || _sample$data2$onMetaD === void 0 ? void 0 : _sample$data2$onMetaD.duration;\n      if (keyframes) {\n        _this._keyframes = keyframes;\n      }\n      if (!_this._opts.isLive && duration) {\n        _this._bufferService.updateDuration(duration);\n      }\n    });\n    _this._opts = getOption(_opts);\n    _this.media = _this._opts.media || document.createElement(\"video\");\n    _this._opts.media = null;\n    _this._firstProgressEmit = false;\n    _this._mediaLoader = new NetLoader(_objectSpread2(_objectSpread2({}, _this._opts.fetchOptions), {}, {\n      retry: _this._opts.retryCount,\n      retryDelay: _this._opts.retryDelay,\n      timeout: _this._opts.loadTimeout,\n      onRetryError: _this._onRetryError,\n      onProgress: _this._onProgress,\n      responseType: \"arraybuffer\"\n    }));\n    _this._bufferService = new BufferService(_assertThisInitialized(_this), _this._opts.softDecode ? _this.media : void 0, _this._opts);\n    _this._seiService = new SeiService(_assertThisInitialized(_this));\n    _this._bandwidthService = new BandwidthService();\n    _this._stats = new MediaStatsService(_assertThisInitialized(_this));\n    if (!_this._opts.softDecode) {\n      _this._gapService = new GapService();\n    }\n    _this.media.addEventListener(\"play\", _this._onPlay);\n    _this.media.addEventListener(\"seeking\", _this._onSeeking);\n    _this.media.addEventListener(\"timeupdate\", _this._onTimeupdate);\n    _this.on(EVENT.FLV_SCRIPT_DATA, _this._onFlvScriptData);\n    return _this;\n  }\n  _createClass(Flv2, [{\n    key: \"version\",\n    get: function get() {\n      return \"3.0.6\";\n    }\n  }, {\n    key: \"isLive\",\n    get: function get() {\n      return this._opts.isLive;\n    }\n  }, {\n    key: \"baseDts\",\n    get: function get() {\n      var _this$_bufferService2;\n      return (_this$_bufferService2 = this._bufferService) === null || _this$_bufferService2 === void 0 ? void 0 : _this$_bufferService2.baseDts;\n    }\n  }, {\n    key: \"seekable\",\n    get: function get() {\n      return !!this._keyframes && this._acceptRanges;\n    }\n  }, {\n    key: \"speedInfo\",\n    value: function speedInfo() {\n      return {\n        speed: this._bandwidthService.getLatestSpeed(),\n        avgSpeed: this._bandwidthService.getAvgSpeed()\n      };\n    }\n  }, {\n    key: \"getStats\",\n    value: function getStats() {\n      return this._stats.getStats();\n    }\n  }, {\n    key: \"bufferInfo\",\n    value: function bufferInfo() {\n      var _this$media2;\n      var maxHole = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0.1;\n      return Buffer.info(Buffer.get(this.media), (_this$media2 = this.media) === null || _this$media2 === void 0 ? void 0 : _this$media2.currentTime, maxHole);\n    }\n  }, {\n    key: \"playbackQuality\",\n    value: function playbackQuality() {\n      return getVideoPlaybackQuality(this.media);\n    }\n  }, {\n    key: \"load\",\n    value: function() {\n      var _load = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee3(url) {\n        var reuseMse, _args3 = arguments;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1)\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                reuseMse = _args3.length > 1 && _args3[1] !== void 0 ? _args3[1] : false;\n                if (this._bufferService) {\n                  _context3.next = 3;\n                  break;\n                }\n                return _context3.abrupt(\"return\");\n              case 3:\n                _context3.next = 5;\n                return this._reset(reuseMse);\n              case 5:\n                this._loadData(url);\n                clearTimeout(this._tickTimer);\n                this._tickTimer = setTimeout(this._tick, this._tickInterval);\n              case 8:\n              case \"end\":\n                return _context3.stop();\n            }\n        }, _callee3, this);\n      }));\n      function load(_x5) {\n        return _load.apply(this, arguments);\n      }\n      return load;\n    }()\n  }, {\n    key: \"replay\",\n    value: function() {\n      var _replay = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee4() {\n        var _this2 = this;\n        var seamlesslyReload, isPlayEmit, _args4 = arguments;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1)\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                seamlesslyReload = _args4.length > 0 && _args4[0] !== void 0 ? _args4[0] : this._opts.seamlesslyReload;\n                isPlayEmit = _args4.length > 1 ? _args4[1] : void 0;\n                if (this.media) {\n                  _context4.next = 4;\n                  break;\n                }\n                return _context4.abrupt(\"return\");\n              case 4:\n                if (!seamlesslyReload) {\n                  _context4.next = 10;\n                  break;\n                }\n                _context4.next = 7;\n                return this._clear();\n              case 7:\n                setTimeout(function() {\n                  _this2._loadData(_this2._opts.url);\n                  _this2._bufferService.seamlessSwitch();\n                  _this2._seamlessSwitching = true;\n                });\n                _context4.next = 12;\n                break;\n              case 10:\n                _context4.next = 12;\n                return this.load();\n              case 12:\n                return _context4.abrupt(\"return\", this.media.play(!isPlayEmit).catch(function() {\n                }));\n              case 13:\n              case \"end\":\n                return _context4.stop();\n            }\n        }, _callee4, this);\n      }));\n      function replay() {\n        return _replay.apply(this, arguments);\n      }\n      return replay;\n    }()\n  }, {\n    key: \"disconnect\",\n    value: function disconnect() {\n      logger.debug(\"disconnect!\");\n      return this._clear();\n    }\n  }, {\n    key: \"switchURL\",\n    value: function() {\n      var _switchURL = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee5(url, seamless) {\n        var _this3 = this;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1)\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                if (this._bufferService) {\n                  _context5.next = 2;\n                  break;\n                }\n                return _context5.abrupt(\"return\");\n              case 2:\n                if (!(!seamless || !this._opts.isLive)) {\n                  _context5.next = 7;\n                  break;\n                }\n                _context5.next = 5;\n                return this.load(url);\n              case 5:\n                this._urlSwitching = true;\n                return _context5.abrupt(\"return\", this.media.play(true).catch(function() {\n                }));\n              case 7:\n                _context5.next = 9;\n                return this._clear();\n              case 9:\n                setTimeout(function() {\n                  _this3._loadData(url);\n                  _this3._bufferService.seamlessSwitch();\n                  _this3._urlSwitching = true;\n                  _this3._seamlessSwitching = true;\n                });\n              case 10:\n              case \"end\":\n                return _context5.stop();\n            }\n        }, _callee5, this);\n      }));\n      function switchURL(_x6, _x7) {\n        return _switchURL.apply(this, arguments);\n      }\n      return switchURL;\n    }()\n  }, {\n    key: \"destroy\",\n    value: function() {\n      var _destroy = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee6() {\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1)\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                if (this.media) {\n                  _context6.next = 2;\n                  break;\n                }\n                return _context6.abrupt(\"return\");\n              case 2:\n                this.removeAllListeners();\n                this._seiService.reset();\n                this.media.removeEventListener(\"play\", this._onPlay);\n                this.media.removeEventListener(\"seeking\", this._onSeeking);\n                this.media.removeEventListener(\"timeupdate\", this._onTimeupdate);\n                _context6.next = 9;\n                return Promise.all([this._clear(), this._bufferService.destroy()]);\n              case 9:\n                this.media = null;\n                this._bufferService = null;\n              case 11:\n              case \"end\":\n                return _context6.stop();\n            }\n        }, _callee6, this);\n      }));\n      function destroy() {\n        return _destroy.apply(this, arguments);\n      }\n      return destroy;\n    }()\n  }, {\n    key: \"_emitError\",\n    value: function _emitError(error) {\n      var _this$media3;\n      var endOfStream = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n      logger.table(error);\n      logger.error(error);\n      logger.error((_this$media3 = this.media) === null || _this$media3 === void 0 ? void 0 : _this$media3.error);\n      if (this._urlSwitching) {\n        this._urlSwitching = false;\n        this._seamlessSwitching = false;\n        this.emit(EVENT.SWITCH_URL_FAILED, error);\n      }\n      this.emit(EVENT.ERROR, error);\n      if (endOfStream) {\n        this._seiService.reset();\n        this._end();\n      }\n    }\n  }, {\n    key: \"_reset\",\n    value: function() {\n      var _reset2 = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee7() {\n        var reuseMse, _args7 = arguments;\n        return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n          while (1)\n            switch (_context7.prev = _context7.next) {\n              case 0:\n                reuseMse = _args7.length > 0 && _args7[0] !== void 0 ? _args7[0] : false;\n                this._seiService.reset();\n                this._bandwidthService.reset();\n                this._stats.reset();\n                _context7.next = 6;\n                return this._clear();\n              case 6:\n                _context7.next = 8;\n                return this._bufferService.reset(reuseMse);\n              case 8:\n                this._firstProgressEmit = false;\n              case 9:\n              case \"end\":\n                return _context7.stop();\n            }\n        }, _callee7, this);\n      }));\n      function _reset() {\n        return _reset2.apply(this, arguments);\n      }\n      return _reset;\n    }()\n  }, {\n    key: \"_loadData\",\n    value: function() {\n      var _loadData2 = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee8(url, range) {\n        return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n          while (1)\n            switch (_context8.prev = _context8.next) {\n              case 0:\n                if (url)\n                  this._opts.url = url;\n                url = this._opts.url;\n                if (url) {\n                  _context8.next = 4;\n                  break;\n                }\n                throw new Error(\"Source url is missing\");\n              case 4:\n                this.emit(EVENT.LOAD_START, {\n                  url\n                });\n                logger.debug(\"load data, loading:\", this._loading, url);\n                if (!this._loading) {\n                  _context8.next = 9;\n                  break;\n                }\n                _context8.next = 9;\n                return this._mediaLoader.cancel();\n              case 9:\n                this._loading = true;\n                _context8.prev = 10;\n                _context8.next = 13;\n                return this._mediaLoader.load({\n                  url,\n                  range\n                });\n              case 13:\n                _context8.next = 18;\n                break;\n              case 15:\n                _context8.prev = 15;\n                _context8.t0 = _context8[\"catch\"](10);\n                return _context8.abrupt(\"return\", this._emitError(StreamingError.network(_context8.t0)));\n              case 18:\n              case \"end\":\n                return _context8.stop();\n            }\n        }, _callee8, this, [[10, 15]]);\n      }));\n      function _loadData(_x8, _x9) {\n        return _loadData2.apply(this, arguments);\n      }\n      return _loadData;\n    }()\n  }, {\n    key: \"_clear\",\n    value: function() {\n      var _clear2 = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee9() {\n        return _regeneratorRuntime().wrap(function _callee9$(_context9) {\n          while (1)\n            switch (_context9.prev = _context9.next) {\n              case 0:\n                clearTimeout(this._maxChunkWaitTimer);\n                clearTimeout(this._tickTimer);\n                if (!this._mediaLoader) {\n                  _context9.next = 5;\n                  break;\n                }\n                _context9.next = 5;\n                return this._mediaLoader.cancel();\n              case 5:\n                this._loading = false;\n              case 6:\n              case \"end\":\n                return _context9.stop();\n            }\n        }, _callee9, this);\n      }));\n      function _clear() {\n        return _clear2.apply(this, arguments);\n      }\n      return _clear;\n    }()\n  }], [{\n    key: \"isSupported\",\n    value: function isSupported(mediaType) {\n      if (!mediaType || mediaType === \"video\" || mediaType === \"audio\") {\n        return MSE.isSupported();\n      }\n      return typeof WebAssembly !== \"undefined\";\n    }\n  }, {\n    key: \"enableLogger\",\n    value: function enableLogger() {\n      Logger.enable();\n      Logger$1.enable();\n    }\n  }, {\n    key: \"disableLogger\",\n    value: function disableLogger() {\n      Logger.disable();\n      Logger$1.disable();\n    }\n  }]);\n  return Flv2;\n}(EventEmitter);\ntry {\n  if (localStorage.getItem(\"xgd\")) {\n    Flv.enableLogger();\n  } else {\n    Flv.disableLogger();\n  }\n} catch (error) {\n}\nexport { Flv, logger };\n", "import { createClass as _createClass, objectSpread2 as _objectSpread2, classCallCheck as _classCallCheck, defineProperty as _defineProperty, asyncToGenerator as _asyncToGenerator, regeneratorRuntime as _regeneratorRuntime } from \"../../_virtual/_rollupPluginBabelHelpers.js\";\nimport { WarningType, FlvDemuxer, FMP4Remuxer } from \"xgplayer-transmuxer\";\nimport { Logger, EVENT, MSE, StreamingError, ERR, concatUint8Array, Buffer } from \"xgplayer-streaming-shared\";\nvar logger = new Logger(\"BufferService\");\nvar BufferService = /* @__PURE__ */ function() {\n  function BufferService2(flv, softVideo, opts) {\n    _classCallCheck(this, BufferService2);\n    _defineProperty(this, \"flv\", null);\n    _defineProperty(this, \"_demuxer\", new FlvDemuxer());\n    _defineProperty(this, \"_remuxer\", null);\n    _defineProperty(this, \"_mse\", null);\n    _defineProperty(this, \"_softVideo\", null);\n    _defineProperty(this, \"_sourceCreated\", false);\n    _defineProperty(this, \"_needInitSegment\", true);\n    _defineProperty(this, \"_discontinuity\", true);\n    _defineProperty(this, \"_contiguous\", false);\n    _defineProperty(this, \"_initSegmentId\", \"\");\n    _defineProperty(this, \"_cachedBuffer\", null);\n    _defineProperty(this, \"_demuxStartTime\", 0);\n    _defineProperty(this, \"_opts\", null);\n    this.flv = flv;\n    this._opts = opts;\n    if (softVideo) {\n      this._softVideo = softVideo;\n    } else {\n      this._remuxer = new FMP4Remuxer(this._demuxer.videoTrack, this._demuxer.audioTrack);\n      this._mse = new MSE();\n      this._mse.bindMedia(flv.media);\n    }\n  }\n  _createClass(BufferService2, [{\n    key: \"baseDts\",\n    get: function get() {\n      var _this$_demuxer, _this$_demuxer$_fixer;\n      return (_this$_demuxer = this._demuxer) === null || _this$_demuxer === void 0 ? void 0 : (_this$_demuxer$_fixer = _this$_demuxer._fixer) === null || _this$_demuxer$_fixer === void 0 ? void 0 : _this$_demuxer$_fixer._baseDts;\n    }\n  }, {\n    key: \"seamlessSwitch\",\n    value: function seamlessSwitch() {\n      this._needInitSegment = true;\n      this._discontinuity = true;\n      this._contiguous = true;\n      this._initSegmentId = \"\";\n    }\n  }, {\n    key: \"unContiguous\",\n    value: function unContiguous(startTime) {\n      this._contiguous = false;\n      this._demuxStartTime = startTime;\n    }\n  }, {\n    key: \"reset\",\n    value: function() {\n      var _reset = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee() {\n        var reuseMse, _args = arguments;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1)\n            switch (_context.prev = _context.next) {\n              case 0:\n                reuseMse = _args.length > 0 && _args[0] !== void 0 ? _args[0] : false;\n                if (!(this._mse && !reuseMse)) {\n                  _context.next = 6;\n                  break;\n                }\n                _context.next = 4;\n                return this._mse.unbindMedia();\n              case 4:\n                _context.next = 6;\n                return this._mse.bindMedia(this.flv.media);\n              case 6:\n                this._needInitSegment = true;\n                this._discontinuity = true;\n                this._contiguous = false;\n                this._sourceCreated = false;\n                this._initSegmentId = \"\";\n              case 11:\n              case \"end\":\n                return _context.stop();\n            }\n        }, _callee, this);\n      }));\n      function reset() {\n        return _reset.apply(this, arguments);\n      }\n      return reset;\n    }()\n  }, {\n    key: \"endOfStream\",\n    value: function() {\n      var _endOfStream = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee2() {\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1)\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                if (!this._mse) {\n                  _context2.next = 7;\n                  break;\n                }\n                if (!this._sourceCreated) {\n                  _context2.next = 5;\n                  break;\n                }\n                _context2.next = 4;\n                return this._mse.endOfStream();\n              case 4:\n                this.flv.emit(EVENT.BUFFEREOS);\n              case 5:\n                _context2.next = 8;\n                break;\n              case 7:\n                if (this._softVideo) {\n                  this._softVideo.endOfStream();\n                }\n              case 8:\n              case \"end\":\n                return _context2.stop();\n            }\n        }, _callee2, this);\n      }));\n      function endOfStream() {\n        return _endOfStream.apply(this, arguments);\n      }\n      return endOfStream;\n    }()\n  }, {\n    key: \"updateDuration\",\n    value: function() {\n      var _updateDuration = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee3(duration) {\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1)\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                if (!this._mse) {\n                  _context3.next = 7;\n                  break;\n                }\n                if (this._mse.isOpened) {\n                  _context3.next = 4;\n                  break;\n                }\n                _context3.next = 4;\n                return this._mse.open();\n              case 4:\n                logger.debug(\"update duration\", duration);\n                _context3.next = 7;\n                return this._mse.updateDuration(duration);\n              case 7:\n              case \"end\":\n                return _context3.stop();\n            }\n        }, _callee3, this);\n      }));\n      function updateDuration(_x) {\n        return _updateDuration.apply(this, arguments);\n      }\n      return updateDuration;\n    }()\n  }, {\n    key: \"destroy\",\n    value: function() {\n      var _destroy = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee4() {\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1)\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                if (!this._mse) {\n                  _context4.next = 3;\n                  break;\n                }\n                _context4.next = 3;\n                return this._mse.unbindMedia();\n              case 3:\n                this._mse = null;\n                this._softVideo = null;\n                this._demuxer = null;\n                this._remuxer = null;\n              case 7:\n              case \"end\":\n                return _context4.stop();\n            }\n        }, _callee4, this);\n      }));\n      function destroy() {\n        return _destroy.apply(this, arguments);\n      }\n      return destroy;\n    }()\n  }, {\n    key: \"appendBuffer\",\n    value: function() {\n      var _appendBuffer = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee5(chunk) {\n        var demuxer, videoTrack, audioTrack, metadataTrack, videoExist, audioExist, duration, track, videoType, audioType, mse, newId, remuxResult, p;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1)\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                if (this._cachedBuffer) {\n                  chunk = concatUint8Array(this._cachedBuffer, chunk);\n                  this._cachedBuffer = null;\n                }\n                demuxer = this._demuxer;\n                if (!(!chunk || !chunk.length || !demuxer)) {\n                  _context5.next = 4;\n                  break;\n                }\n                return _context5.abrupt(\"return\");\n              case 4:\n                _context5.prev = 4;\n                demuxer.demuxAndFix(chunk, this._discontinuity, this._contiguous, this._demuxStartTime);\n                _context5.next = 11;\n                break;\n              case 8:\n                _context5.prev = 8;\n                _context5.t0 = _context5[\"catch\"](4);\n                throw new StreamingError(ERR.DEMUX, ERR.SUB_TYPES.FLV, _context5.t0);\n              case 11:\n                videoTrack = demuxer.videoTrack, audioTrack = demuxer.audioTrack, metadataTrack = demuxer.metadataTrack;\n                videoExist = videoTrack.exist();\n                audioExist = audioTrack.exist();\n                if (this._opts.onlyAudio) {\n                  videoExist = false;\n                  videoTrack.present = false;\n                }\n                if (this._opts.onlyVideo) {\n                  audioExist = false;\n                  audioTrack.present = false;\n                }\n                if (!(!videoExist && videoTrack.present || !audioExist && audioTrack.present)) {\n                  _context5.next = 29;\n                  break;\n                }\n                duration = 0;\n                track = videoExist ? videoTrack : audioExist ? audioTrack : void 0;\n                if (track && track.samples.length) {\n                  duration = (track.samples[track.samples.length - 1].originPts - track.samples[0].originPts) / track.timescale * 1e3;\n                }\n                if (!(duration > this._opts.analyzeDuration)) {\n                  _context5.next = 27;\n                  break;\n                }\n                logger.warn(\"analyze duration exceeded, \".concat(duration, \"ms\"), track);\n                videoTrack.present = videoExist;\n                audioTrack.present = audioExist;\n                this.flv.emit(EVENT.ANALYZE_DURATION_EXCEEDED, {\n                  duration\n                });\n                _context5.next = 29;\n                break;\n              case 27:\n                this._cachedBuffer = chunk;\n                return _context5.abrupt(\"return\");\n              case 29:\n                videoType = videoTrack.type;\n                audioType = audioTrack.type;\n                this._fireEvents(videoTrack, audioTrack, metadataTrack);\n                this._discontinuity = false;\n                this._contiguous = true;\n                this._demuxStartTime = 0;\n                mse = this._mse;\n                this.flv.emit(EVENT.DEMUXED_TRACK, {\n                  videoTrack\n                });\n                newId = \"\".concat(videoTrack.codec, \"/\").concat(videoTrack.width, \"/\").concat(videoTrack.height, \"/\").concat(audioTrack.codec, \"/\").concat(audioTrack.config);\n                if (newId !== this._initSegmentId) {\n                  this._needInitSegment = true;\n                  this._initSegmentId = newId;\n                  this._emitMetaParsedEvent(videoTrack, audioTrack);\n                }\n                if (!mse) {\n                  _context5.next = 65;\n                  break;\n                }\n                if (this._sourceCreated) {\n                  _context5.next = 47;\n                  break;\n                }\n                _context5.next = 43;\n                return mse.open();\n              case 43:\n                if (videoExist) {\n                  logger.log(\"codec: video/mp4;codecs=\".concat(videoTrack.codec));\n                  mse.createSource(videoType, \"video/mp4;codecs=\".concat(videoTrack.codec));\n                }\n                if (audioExist) {\n                  logger.log(\"codec: audio/mp4;codecs=\".concat(audioTrack.codec));\n                  mse.createSource(audioType, \"audio/mp4;codecs=\".concat(audioTrack.codec));\n                }\n                this._sourceCreated = true;\n                this.flv.emit(EVENT.SOURCEBUFFER_CREATED);\n              case 47:\n                _context5.prev = 47;\n                remuxResult = this._remuxer.remux(this._needInitSegment);\n                _context5.next = 54;\n                break;\n              case 51:\n                _context5.prev = 51;\n                _context5.t1 = _context5[\"catch\"](47);\n                throw new StreamingError(ERR.REMUX, ERR.SUB_TYPES.FMP4, _context5.t1);\n              case 54:\n                if (!(this._needInitSegment && !remuxResult.videoInitSegment && !remuxResult.audioInitSegment)) {\n                  _context5.next = 56;\n                  break;\n                }\n                return _context5.abrupt(\"return\");\n              case 56:\n                this._needInitSegment = false;\n                p = [];\n                if (remuxResult.videoInitSegment)\n                  p.push(mse.append(videoType, remuxResult.videoInitSegment));\n                if (remuxResult.audioInitSegment)\n                  p.push(mse.append(audioType, remuxResult.audioInitSegment));\n                if (remuxResult.videoSegment)\n                  p.push(mse.append(videoType, remuxResult.videoSegment));\n                if (remuxResult.audioSegment)\n                  p.push(mse.append(audioType, remuxResult.audioSegment));\n                return _context5.abrupt(\"return\", Promise.all(p));\n              case 65:\n                if (this._softVideo) {\n                  this._softVideo.appendBuffer(videoTrack, audioTrack);\n                }\n              case 66:\n              case \"end\":\n                return _context5.stop();\n            }\n        }, _callee5, this, [[4, 8], [47, 51]]);\n      }));\n      function appendBuffer(_x2) {\n        return _appendBuffer.apply(this, arguments);\n      }\n      return appendBuffer;\n    }()\n  }, {\n    key: \"evictBuffer\",\n    value: function() {\n      var _evictBuffer = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee6(bufferBehind) {\n        var _this = this;\n        var media, currentTime, removeEnd, start;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1)\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                media = this.flv.media;\n                if (!(!this._mse || !this._demuxer || !media || !bufferBehind || bufferBehind < 0)) {\n                  _context6.next = 3;\n                  break;\n                }\n                return _context6.abrupt(\"return\");\n              case 3:\n                currentTime = media.currentTime;\n                removeEnd = currentTime - bufferBehind;\n                if (!(removeEnd <= 0)) {\n                  _context6.next = 7;\n                  break;\n                }\n                return _context6.abrupt(\"return\");\n              case 7:\n                start = Buffer.start(Buffer.get(media));\n                if (!(start + 1 >= removeEnd)) {\n                  _context6.next = 10;\n                  break;\n                }\n                return _context6.abrupt(\"return\");\n              case 10:\n                return _context6.abrupt(\"return\", this._mse.clearBuffer(0, removeEnd).then(function() {\n                  return _this.flv.emit(EVENT.REMOVE_BUFFER, {\n                    removeEnd\n                  });\n                }));\n              case 11:\n              case \"end\":\n                return _context6.stop();\n            }\n        }, _callee6, this);\n      }));\n      function evictBuffer(_x3) {\n        return _evictBuffer.apply(this, arguments);\n      }\n      return evictBuffer;\n    }()\n  }, {\n    key: \"_emitMetaParsedEvent\",\n    value: function _emitMetaParsedEvent(videoTrack, audioTrack) {\n      if (videoTrack.exist()) {\n        this.flv.emit(EVENT.METADATA_PARSED, {\n          type: \"video\",\n          track: videoTrack,\n          meta: {\n            codec: videoTrack.codec,\n            timescale: videoTrack.timescale,\n            width: videoTrack.width,\n            height: videoTrack.height,\n            sarRatio: videoTrack.sarRatio,\n            baseDts: videoTrack.baseDts\n          }\n        });\n      }\n      if (audioTrack.exist()) {\n        this.flv.emit(EVENT.METADATA_PARSED, {\n          type: \"audio\",\n          track: audioTrack,\n          meta: {\n            codec: audioTrack.codec,\n            channelCount: audioTrack.channelCount,\n            sampleRate: audioTrack.sampleRate,\n            timescale: audioTrack.timescale,\n            baseDts: audioTrack.baseDts\n          }\n        });\n      }\n      logger.debug(\"track parsed\", videoTrack, audioTrack);\n    }\n  }, {\n    key: \"_fireEvents\",\n    value: function _fireEvents(videoTrack, audioTrack, metadataTrack) {\n      var _this2 = this;\n      logger.debug(videoTrack.samples, audioTrack.samples);\n      metadataTrack.flvScriptSamples.forEach(function(sample) {\n        _this2.flv.emit(EVENT.FLV_SCRIPT_DATA, sample);\n        logger.debug(\"flvScriptData\", sample);\n      });\n      videoTrack.samples.forEach(function(sample) {\n        if (sample.keyframe) {\n          _this2.flv.emit(EVENT.KEYFRAME, {\n            pts: sample.pts\n          });\n        }\n      });\n      videoTrack.warnings.forEach(function(warn) {\n        var type;\n        switch (warn.type) {\n          case WarningType.LARGE_AV_SHIFT:\n            type = EVENT.LARGE_AV_FIRST_FRAME_GAP_DETECT;\n            break;\n          case WarningType.LARGE_VIDEO_GAP:\n            type = EVENT.LARGE_VIDEO_DTS_GAP_DETECT;\n            break;\n          case WarningType.LARGE_VIDEO_GAP_BETWEEN_CHUNK:\n            type = EVENT.MAX_DTS_DELTA_WITH_NEXT_SEGMENT_DETECT;\n            break;\n        }\n        if (type)\n          _this2.flv.emit(EVENT.STREAM_EXCEPTION, _objectSpread2(_objectSpread2({}, warn), {}, {\n            type\n          }));\n        logger.warn(\"video exception\", warn);\n      });\n      audioTrack.warnings.forEach(function(warn) {\n        var type;\n        switch (warn.type) {\n          case WarningType.LARGE_AUDIO_GAP:\n            type = EVENT.LARGE_AUDIO_DTS_GAP_DETECT;\n            break;\n          case WarningType.AUDIO_FILLED:\n            type = EVENT.AUDIO_GAP_DETECT;\n            break;\n          case WarningType.AUDIO_DROPPED:\n            type = EVENT.AUDIO_OVERLAP_DETECT;\n            break;\n        }\n        if (type)\n          _this2.flv.emit(EVENT.STREAM_EXCEPTION, _objectSpread2(_objectSpread2({}, warn), {}, {\n            type\n          }));\n        logger.warn(\"audio exception\", warn);\n      });\n      metadataTrack.seiSamples.forEach(function(sei) {\n        _this2.flv.emit(EVENT.SEI, _objectSpread2(_objectSpread2({}, sei), {}, {\n          sei: {\n            code: sei.data.type,\n            content: sei.data.payload,\n            dts: sei.pts\n          }\n        }));\n      });\n    }\n  }]);\n  return BufferService2;\n}();\nexport { BufferService };\n", "import { objectSpread2 as _objectSpread2 } from \"../_virtual/_rollupPluginBabelHelpers.js\";\nfunction getOption(opts) {\n  var ret = _objectSpread2({\n    retryCount: 3,\n    retryDelay: 1e3,\n    loadTimeout: 1e4,\n    maxReaderInterval: 5e3,\n    preloadTime: 5,\n    isLive: false,\n    softDecode: false,\n    bufferBehind: 10,\n    maxJumpDistance: 3,\n    analyzeDuration: 2e4,\n    seamlesslyReload: false,\n    keepStatusAfterSwitch: true,\n    onlyVideo: false,\n    onlyAudio: false\n  }, opts);\n  if (ret.isLive) {\n    if (ret.preloadTime) {\n      if (!ret.maxLatency) {\n        ret.maxLatency = ret.preloadTime * 2;\n      }\n      if (!ret.targetLatency) {\n        ret.targetLatency = ret.preloadTime;\n      }\n      if (ret.disconnectTime === null || ret.disconnectTime === void 0) {\n        ret.disconnectTime = ret.maxLatency;\n      }\n    }\n  }\n  return ret;\n}\nexport { getOption };\n", "function searchKeyframeIndex(list, value) {\n  var idx = 0;\n  var last = list.length - 1;\n  var mid = 0;\n  var lbound = 0;\n  var ubound = last;\n  if (value < list[0]) {\n    idx = 0;\n    lbound = ubound + 1;\n  }\n  while (lbound <= ubound) {\n    mid = lbound + Math.floor((ubound - lbound) / 2);\n    if (mid === last || value >= list[mid] && value < list[mid + 1]) {\n      idx = mid;\n      break;\n    } else if (list[mid] < value) {\n      lbound = mid + 1;\n    } else {\n      ubound = mid - 1;\n    }\n  }\n  return idx;\n}\nexport { searchKeyframeIndex };\n", "import { createClass as _createClass, classCallCheck as _classCallCheck, defineProperty as _defineProperty, objectSpread2 as _objectSpread2 } from \"./_virtual/_rollupPluginBabelHelpers.js\";\nimport { EVENT } from \"xgplayer-streaming-shared\";\nvar PluginExtension = /* @__PURE__ */ function() {\n  function PluginExtension2(opts, plugin) {\n    var _this = this;\n    _classCallCheck(this, PluginExtension2);\n    _defineProperty(this, \"_onLowDecode\", function() {\n      var _this$_plugin, _this$_plugin$player, _this$_plugin2, _this$_plugin2$player;\n      var _this$_opts = _this._opts, media = _this$_opts.media, innerDegrade = _this$_opts.innerDegrade, backupURL = _this$_opts.backupURL;\n      (_this$_plugin = _this._plugin) === null || _this$_plugin === void 0 ? void 0 : (_this$_plugin$player = _this$_plugin.player) === null || _this$_plugin$player === void 0 ? void 0 : _this$_plugin$player.emit(\"lowdecode\", media.degradeInfo);\n      (_this$_plugin2 = _this._plugin) === null || _this$_plugin2 === void 0 ? void 0 : (_this$_plugin2$player = _this$_plugin2.player) === null || _this$_plugin2$player === void 0 ? void 0 : _this$_plugin2$player.emit(\"core_event\", _objectSpread2(_objectSpread2({}, media.degradeInfo), {}, {\n        eventName: EVENT.LOWDECODE\n      }));\n      if ((innerDegrade === 1 || innerDegrade === 3) && backupURL) {\n        _this._degrade(backupURL);\n      }\n    });\n    _defineProperty(this, \"_degrade\", function(url) {\n      var player = _this._plugin.player;\n      var originVideo = player.video;\n      if ((originVideo === null || originVideo === void 0 ? void 0 : originVideo.TAG) !== \"MVideo\")\n        return;\n      var newVideo = player.video.degradeVideo;\n      player.video = newVideo;\n      originVideo.degrade(url);\n      if (url) {\n        player.config.url = url;\n      }\n      var firstChild = player.root.firstChild;\n      if (firstChild.TAG === \"MVideo\") {\n        player.root.replaceChild(newVideo, firstChild);\n      }\n      var flvPlugin = _this._plugin.constructor.pluginName.toLowerCase();\n      player.unRegisterPlugin(flvPlugin);\n      player.once(\"canplay\", function() {\n        player.play();\n      });\n    });\n    _defineProperty(this, \"forceDegradeToVideo\", function(url) {\n      var innerDegrade = _this._opts.innerDegrade;\n      if (innerDegrade === 1 || innerDegrade === 3) {\n        _this._degrade(url);\n      }\n    });\n    this._opts = opts;\n    this._plugin = plugin;\n    this._init();\n  }\n  _createClass(PluginExtension2, [{\n    key: \"_init\",\n    value: function _init() {\n      var _this$_opts2 = this._opts, media = _this$_opts2.media, preloadTime = _this$_opts2.preloadTime, innerDegrade = _this$_opts2.innerDegrade, decodeMode = _this$_opts2.decodeMode;\n      if (!media)\n        return;\n      if (innerDegrade) {\n        media.setAttribute(\"innerdegrade\", innerDegrade);\n      }\n      if (preloadTime) {\n        media.setAttribute(\"preloadtime\", preloadTime);\n      }\n      if (media.setDecodeMode) {\n        media.setDecodeMode(decodeMode);\n      }\n      this._bindEvents();\n    }\n  }, {\n    key: \"_bindEvents\",\n    value: function _bindEvents() {\n      var media = this._opts.media;\n      media.addEventListener(\"lowdecode\", this._onLowDecode);\n    }\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      var _this$_opts3, _this$_opts3$media;\n      (_this$_opts3 = this._opts) === null || _this$_opts3 === void 0 ? void 0 : (_this$_opts3$media = _this$_opts3.media) === null || _this$_opts3$media === void 0 ? void 0 : _this$_opts3$media.removeEventListener(\"lowdecode\", this._onLowDecode);\n      this._plugin = null;\n    }\n  }]);\n  return PluginExtension2;\n}();\nexport { PluginExtension as default };\n", "import { defineProperty as _defineProperty, inherits as _inherits, createSuper as _createSuper, createClass as _createClass, classCallCheck as _classCallCheck, assertThisInitialized as _assertThisInitialized, objectSpread2 as _objectSpread2 } from \"./_virtual/_rollupPluginBabelHelpers.js\";\nimport { BasePlugin, Events, Errors } from \"xgplayer\";\nimport { EVENT } from \"xgplayer-streaming-shared\";\nimport { Flv } from \"./flv/index.js\";\nimport PluginExtension from \"./plugin-extension.js\";\nvar FlvPlugin = /* @__PURE__ */ function(_BasePlugin) {\n  _inherits(FlvPlugin2, _BasePlugin);\n  var _super = _createSuper(FlvPlugin2);\n  function FlvPlugin2() {\n    var _this;\n    _classCallCheck(this, FlvPlugin2);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"flv\", null);\n    _defineProperty(_assertThisInitialized(_this), \"pluginExtension\", null);\n    _defineProperty(_assertThisInitialized(_this), \"getStats\", function() {\n      var _this$flv;\n      return (_this$flv = _this.flv) === null || _this$flv === void 0 ? void 0 : _this$flv.getStats();\n    });\n    _defineProperty(_assertThisInitialized(_this), \"destroy\", function() {\n      var _this$pluginExtension;\n      if (_this.flv) {\n        _this.flv.destroy();\n        _this.flv = null;\n      }\n      (_this$pluginExtension = _this.pluginExtension) === null || _this$pluginExtension === void 0 ? void 0 : _this$pluginExtension.destroy();\n      _this.pluginExtension = null;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"_onSwitchURL\", function(url, seamless) {\n      if (_this.flv) {\n        var _this$player$config, _this$player$config$f;\n        _this.player.config.url = url;\n        _this.flv.switchURL(url, seamless);\n        if (!seamless && (_this$player$config = _this.player.config) !== null && _this$player$config !== void 0 && (_this$player$config$f = _this$player$config.flv) !== null && _this$player$config$f !== void 0 && _this$player$config$f.keepStatusAfterSwitch) {\n          _this._keepPauseStatus();\n        }\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"_keepPauseStatus\", function() {\n      var paused = _this.player.paused;\n      if (!paused)\n        return;\n      _this.player.once(\"canplay\", function() {\n        _this.player.pause();\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"_onDefinitionChange\", function(_ref) {\n      var to = _ref.to;\n      if (_this.flv)\n        _this.flv.switchURL(to);\n    });\n    return _this;\n  }\n  _createClass(FlvPlugin2, [{\n    key: \"core\",\n    get: function get() {\n      return this.flv;\n    }\n  }, {\n    key: \"version\",\n    get: function get() {\n      var _this$flv2;\n      return (_this$flv2 = this.flv) === null || _this$flv2 === void 0 ? void 0 : _this$flv2.version;\n    }\n  }, {\n    key: \"softDecode\",\n    get: function get() {\n      var _this$player, _this$player$config2;\n      var mediaType = (_this$player = this.player) === null || _this$player === void 0 ? void 0 : (_this$player$config2 = _this$player.config) === null || _this$player$config2 === void 0 ? void 0 : _this$player$config2.mediaType;\n      return !!mediaType && mediaType !== \"video\" && mediaType !== \"audio\";\n    }\n  }, {\n    key: \"beforePlayerInit\",\n    value: function beforePlayerInit() {\n      var _this2 = this;\n      var config = this.player.config;\n      if (!config.url)\n        return;\n      if (this.flv)\n        this.flv.destroy();\n      this.player.switchURL = this._onSwitchURL;\n      var flvOpts = config.flv || {};\n      if (flvOpts.disconnectTime === null || flvOpts.disconnectTime === void 0) {\n        flvOpts.disconnectTime = 0;\n      }\n      this.flv = new Flv(_objectSpread2({\n        softDecode: this.softDecode,\n        isLive: config.isLive,\n        media: this.player.video\n      }, flvOpts));\n      if (!this.softDecode) {\n        BasePlugin.defineGetterOrSetter(this.player, {\n          url: {\n            get: function get() {\n              var _this2$flv, _this2$flv$media;\n              return (_this2$flv = _this2.flv) === null || _this2$flv === void 0 ? void 0 : (_this2$flv$media = _this2$flv.media) === null || _this2$flv$media === void 0 ? void 0 : _this2$flv$media.src;\n            },\n            configurable: true\n          }\n        });\n      }\n      if (this.softDecode) {\n        this.pluginExtension = new PluginExtension(_objectSpread2({\n          media: this.player.video\n        }, config.flv), this);\n        this.player.forceDegradeToVideo = function() {\n          var _this2$pluginExtensio;\n          for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n            args[_key2] = arguments[_key2];\n          }\n          return (_this2$pluginExtensio = _this2.pluginExtension) === null || _this2$pluginExtensio === void 0 ? void 0 : _this2$pluginExtensio.forceDegradeToVideo.apply(_this2$pluginExtensio, args);\n        };\n      }\n      if (config.isLive) {\n        var _this$player2;\n        (_this$player2 = this.player) === null || _this$player2 === void 0 ? void 0 : _this$player2.useHooks(\"replay\", function() {\n          var _this2$flv2;\n          return (_this2$flv2 = _this2.flv) === null || _this2$flv2 === void 0 ? void 0 : _this2$flv2.replay();\n        });\n      }\n      this.on(Events.URL_CHANGE, this._onSwitchURL);\n      this.on(Events.DESTROY, this.destroy);\n      this._transError();\n      this._transCoreEvent(EVENT.TTFB);\n      this._transCoreEvent(EVENT.LOAD_START);\n      this._transCoreEvent(EVENT.LOAD_RESPONSE_HEADERS);\n      this._transCoreEvent(EVENT.LOAD_COMPLETE);\n      this._transCoreEvent(EVENT.LOAD_RETRY);\n      this._transCoreEvent(EVENT.SOURCEBUFFER_CREATED);\n      this._transCoreEvent(EVENT.ANALYZE_DURATION_EXCEEDED);\n      this._transCoreEvent(EVENT.REMOVE_BUFFER);\n      this._transCoreEvent(EVENT.BUFFEREOS);\n      this._transCoreEvent(EVENT.KEYFRAME);\n      this._transCoreEvent(EVENT.METADATA_PARSED);\n      this._transCoreEvent(EVENT.SEI);\n      this._transCoreEvent(EVENT.SEI_IN_TIME);\n      this._transCoreEvent(EVENT.FLV_SCRIPT_DATA);\n      this._transCoreEvent(EVENT.STREAM_EXCEPTION);\n      this._transCoreEvent(EVENT.SWITCH_URL_SUCCESS);\n      this._transCoreEvent(EVENT.SWITCH_URL_FAILED);\n      this.flv.load(config.url, true);\n    }\n  }, {\n    key: \"_transError\",\n    value: function _transError() {\n      var _this3 = this;\n      this.flv.on(EVENT.ERROR, function(err) {\n        if (_this3.player) {\n          _this3.player.emit(Events.ERROR, new Errors(_this3.player, err));\n        }\n      });\n    }\n  }, {\n    key: \"_transCoreEvent\",\n    value: function _transCoreEvent(eventName) {\n      var _this4 = this;\n      this.flv.on(eventName, function(e) {\n        if (_this4.player) {\n          _this4.player.emit(\"core_event\", _objectSpread2(_objectSpread2({}, e), {}, {\n            eventName\n          }));\n        }\n      });\n    }\n  }], [{\n    key: \"pluginName\",\n    get: function get() {\n      return \"flv\";\n    }\n  }, {\n    key: \"isSupported\",\n    value: function isSupported(mediaType, codec) {\n      return Flv.isSupported(mediaType, codec);\n    }\n  }]);\n  return FlvPlugin2;\n}(BasePlugin);\n_defineProperty(FlvPlugin, \"Flv\", Flv);\nexport { FlvPlugin };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAC7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AACjD,uBAAmB,UAAU,QAAQ,OAAO,SAAS,KAAK;AACxD,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IACtD,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EACpC;AACA,SAAO;AACT;AACA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AACpD,QAAI,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAS,KAAK;AAC1D,sBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAC1C,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAS,KAAK;AAChK,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,IACjF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,sBAAsB;AAC7B,wBAAsB,WAAW;AAC/B,WAAO;AAAA,EACT;AACA,MAAI,UAAU,CAAC,GAAG,KAAK,OAAO,WAAW,SAAS,GAAG,gBAAgB,iBAAiB,OAAO,kBAAkB,SAAS,KAAK,KAAK,MAAM;AACtI,QAAI,GAAG,IAAI,KAAK;AAAA,EAClB,GAAG,UAAU,cAAc,OAAO,SAAS,SAAS,CAAC,GAAG,iBAAiB,QAAQ,YAAY,cAAc,sBAAsB,QAAQ,iBAAiB,mBAAmB,oBAAoB,QAAQ,eAAe;AACxN,WAAS,OAAO,KAAK,KAAK,OAAO;AAC/B,WAAO,OAAO,eAAe,KAAK,KAAK;AAAA,MACrC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC,GAAG,IAAI,GAAG;AAAA,EACb;AACA,MAAI;AACF,WAAO,CAAC,GAAG,EAAE;AAAA,EACf,SAAS,KAAK;AACZ,aAAS,SAAS,KAAK,KAAK,OAAO;AACjC,aAAO,IAAI,GAAG,IAAI;AAAA,IACpB;AAAA,EACF;AACA,WAAS,KAAK,SAAS,SAAS,MAAM,aAAa;AACjD,QAAI,iBAAiB,WAAW,QAAQ,qBAAqB,YAAY,UAAU,WAAW,YAAY,OAAO,OAAO,eAAe,SAAS,GAAG,UAAU,IAAI,QAAQ,eAAe,CAAC,CAAC;AAC1L,WAAO,eAAe,WAAW,WAAW;AAAA,MAC1C,OAAO,iBAAiB,SAAS,MAAM,OAAO;AAAA,IAChD,CAAC,GAAG;AAAA,EACN;AACA,WAAS,SAAS,IAAI,KAAK,KAAK;AAC9B,QAAI;AACF,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,GAAG,KAAK,KAAK,GAAG;AAAA,MACvB;AAAA,IACF,SAAS,KAAK;AACZ,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AACA,UAAQ,OAAO;AACf,MAAI,mBAAmB,CAAC;AACxB,WAAS,YAAY;AAAA,EACrB;AACA,WAAS,oBAAoB;AAAA,EAC7B;AACA,WAAS,6BAA6B;AAAA,EACtC;AACA,MAAI,oBAAoB,CAAC;AACzB,SAAO,mBAAmB,gBAAgB,WAAW;AACnD,WAAO;AAAA,EACT,CAAC;AACD,MAAI,WAAW,OAAO,gBAAgB,0BAA0B,YAAY,SAAS,SAAS,OAAO,CAAC,CAAC,CAAC,CAAC;AACzG,6BAA2B,4BAA4B,MAAM,OAAO,KAAK,yBAAyB,cAAc,MAAM,oBAAoB;AAC1I,MAAI,KAAK,2BAA2B,YAAY,UAAU,YAAY,OAAO,OAAO,iBAAiB;AACrG,WAAS,sBAAsB,WAAW;AACxC,KAAC,QAAQ,SAAS,QAAQ,EAAE,QAAQ,SAAS,QAAQ;AACnD,aAAO,WAAW,QAAQ,SAAS,KAAK;AACtC,eAAO,KAAK,QAAQ,QAAQ,GAAG;AAAA,MACjC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,WAAS,cAAc,WAAW,aAAa;AAC7C,aAAS,OAAO,QAAQ,KAAK,SAAS,QAAQ;AAC5C,UAAI,SAAS,SAAS,UAAU,MAAM,GAAG,WAAW,GAAG;AACvD,UAAI,YAAY,OAAO,MAAM;AAC3B,YAAI,SAAS,OAAO,KAAK,QAAQ,OAAO;AACxC,eAAO,SAAS,YAAY,OAAO,SAAS,OAAO,KAAK,OAAO,SAAS,IAAI,YAAY,QAAQ,MAAM,OAAO,EAAE,KAAK,SAAS,QAAQ;AACnI,iBAAO,QAAQ,QAAQ,SAAS,MAAM;AAAA,QACxC,GAAG,SAAS,KAAK;AACf,iBAAO,SAAS,KAAK,SAAS,MAAM;AAAA,QACtC,CAAC,IAAI,YAAY,QAAQ,KAAK,EAAE,KAAK,SAAS,WAAW;AACvD,iBAAO,QAAQ,WAAW,QAAQ,MAAM;AAAA,QAC1C,GAAG,SAAS,OAAO;AACjB,iBAAO,OAAO,SAAS,OAAO,SAAS,MAAM;AAAA,QAC/C,CAAC;AAAA,MACH;AACA,aAAO,OAAO,GAAG;AAAA,IACnB;AACA,QAAI;AACJ,mBAAe,MAAM,WAAW;AAAA,MAC9B,OAAO,SAAS,QAAQ,KAAK;AAC3B,iBAAS,6BAA6B;AACpC,iBAAO,IAAI,YAAY,SAAS,SAAS,QAAQ;AAC/C,mBAAO,QAAQ,KAAK,SAAS,MAAM;AAAA,UACrC,CAAC;AAAA,QACH;AACA,eAAO,kBAAkB,kBAAkB,gBAAgB,KAAK,4BAA4B,0BAA0B,IAAI,2BAA2B;AAAA,MACvJ;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,iBAAiB,SAAS,MAAM,SAAS;AAChD,QAAI,QAAQ;AACZ,WAAO,SAAS,QAAQ,KAAK;AAC3B,UAAI,gBAAgB;AAClB,cAAM,IAAI,MAAM,8BAA8B;AAChD,UAAI,gBAAgB,OAAO;AACzB,YAAI,YAAY;AACd,gBAAM;AACR,eAAO,WAAW;AAAA,MACpB;AACA,WAAK,QAAQ,SAAS,QAAQ,QAAQ,MAAM,SAAS;AACnD,YAAI,WAAW,QAAQ;AACvB,YAAI,UAAU;AACZ,cAAI,iBAAiB,oBAAoB,UAAU,OAAO;AAC1D,cAAI,gBAAgB;AAClB,gBAAI,mBAAmB;AACrB;AACF,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,WAAW,QAAQ;AACrB,kBAAQ,OAAO,QAAQ,QAAQ,QAAQ;AAAA,iBAChC,YAAY,QAAQ,QAAQ;AACnC,cAAI,qBAAqB;AACvB,kBAAM,QAAQ,aAAa,QAAQ;AACrC,kBAAQ,kBAAkB,QAAQ,GAAG;AAAA,QACvC;AACE,uBAAa,QAAQ,UAAU,QAAQ,OAAO,UAAU,QAAQ,GAAG;AACrE,gBAAQ;AACR,YAAI,SAAS,SAAS,SAAS,MAAM,OAAO;AAC5C,YAAI,aAAa,OAAO,MAAM;AAC5B,cAAI,QAAQ,QAAQ,OAAO,cAAc,kBAAkB,OAAO,QAAQ;AACxE;AACF,iBAAO;AAAA,YACL,OAAO,OAAO;AAAA,YACd,MAAM,QAAQ;AAAA,UAChB;AAAA,QACF;AACA,oBAAY,OAAO,SAAS,QAAQ,aAAa,QAAQ,SAAS,SAAS,QAAQ,MAAM,OAAO;AAAA,MAClG;AAAA,IACF;AAAA,EACF;AACA,WAAS,oBAAoB,UAAU,SAAS;AAC9C,QAAI,aAAa,QAAQ,QAAQ,SAAS,SAAS,SAAS,UAAU;AACtE,QAAI,WAAW;AACb,aAAO,QAAQ,WAAW,MAAM,YAAY,cAAc,SAAS,SAAS,WAAW,QAAQ,SAAS,UAAU,QAAQ,MAAM,QAAQ,oBAAoB,UAAU,OAAO,GAAG,YAAY,QAAQ,WAAW,aAAa,eAAe,QAAQ,SAAS,SAAS,QAAQ,MAAM,IAAI,UAAU,sCAAsC,aAAa,UAAU,IAAI;AACpW,QAAI,SAAS,SAAS,QAAQ,SAAS,UAAU,QAAQ,GAAG;AAC5D,QAAI,YAAY,OAAO;AACrB,aAAO,QAAQ,SAAS,SAAS,QAAQ,MAAM,OAAO,KAAK,QAAQ,WAAW,MAAM;AACtF,QAAI,OAAO,OAAO;AAClB,WAAO,OAAO,KAAK,QAAQ,QAAQ,SAAS,UAAU,IAAI,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,aAAa,QAAQ,WAAW,QAAQ,SAAS,QAAQ,QAAQ,MAAM,SAAS,QAAQ,WAAW,MAAM,oBAAoB,QAAQ,QAAQ,SAAS,SAAS,QAAQ,MAAM,IAAI,UAAU,kCAAkC,GAAG,QAAQ,WAAW,MAAM;AAAA,EAClW;AACA,WAAS,aAAa,MAAM;AAC1B,QAAI,QAAQ;AAAA,MACV,QAAQ,KAAK,CAAC;AAAA,IAChB;AACA,SAAK,SAAS,MAAM,WAAW,KAAK,CAAC,IAAI,KAAK,SAAS,MAAM,aAAa,KAAK,CAAC,GAAG,MAAM,WAAW,KAAK,CAAC,IAAI,KAAK,WAAW,KAAK,KAAK;AAAA,EAC1I;AACA,WAAS,cAAc,OAAO;AAC5B,QAAI,SAAS,MAAM,cAAc,CAAC;AAClC,WAAO,OAAO,UAAU,OAAO,OAAO,KAAK,MAAM,aAAa;AAAA,EAChE;AACA,WAAS,QAAQ,aAAa;AAC5B,SAAK,aAAa,CAAC;AAAA,MACjB,QAAQ;AAAA,IACV,CAAC,GAAG,YAAY,QAAQ,cAAc,IAAI,GAAG,KAAK,MAAM,IAAI;AAAA,EAC9D;AACA,WAAS,OAAO,UAAU;AACxB,QAAI,UAAU;AACZ,UAAI,iBAAiB,SAAS,cAAc;AAC5C,UAAI;AACF,eAAO,eAAe,KAAK,QAAQ;AACrC,UAAI,cAAc,OAAO,SAAS;AAChC,eAAO;AACT,UAAI,CAAC,MAAM,SAAS,MAAM,GAAG;AAC3B,YAAI,IAAI,IAAI,OAAO,SAAS,QAAQ;AAClC,iBAAO,EAAE,IAAI,SAAS;AACpB,gBAAI,OAAO,KAAK,UAAU,CAAC;AACzB,qBAAO,MAAM,QAAQ,SAAS,CAAC,GAAG,MAAM,OAAO,OAAO;AAC1D,iBAAO,MAAM,QAAQ,QAAQ,MAAM,OAAO,MAAM;AAAA,QAClD;AACA,eAAO,KAAK,OAAO;AAAA,MACrB;AAAA,IACF;AACA,WAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AACA,WAAS,aAAa;AACpB,WAAO;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,EACF;AACA,SAAO,kBAAkB,YAAY,4BAA4B,eAAe,IAAI,eAAe;AAAA,IACjG,OAAO;AAAA,IACP,cAAc;AAAA,EAChB,CAAC,GAAG,eAAe,4BAA4B,eAAe;AAAA,IAC5D,OAAO;AAAA,IACP,cAAc;AAAA,EAChB,CAAC,GAAG,kBAAkB,cAAc,OAAO,4BAA4B,mBAAmB,mBAAmB,GAAG,QAAQ,sBAAsB,SAAS,QAAQ;AAC7J,QAAI,OAAO,cAAc,OAAO,UAAU,OAAO;AACjD,WAAO,CAAC,CAAC,SAAS,SAAS,qBAAqB,yBAAyB,KAAK,eAAe,KAAK;AAAA,EACpG,GAAG,QAAQ,OAAO,SAAS,QAAQ;AACjC,WAAO,OAAO,iBAAiB,OAAO,eAAe,QAAQ,0BAA0B,KAAK,OAAO,YAAY,4BAA4B,OAAO,QAAQ,mBAAmB,mBAAmB,IAAI,OAAO,YAAY,OAAO,OAAO,EAAE,GAAG;AAAA,EAC5O,GAAG,QAAQ,QAAQ,SAAS,KAAK;AAC/B,WAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,EACF,GAAG,sBAAsB,cAAc,SAAS,GAAG,OAAO,cAAc,WAAW,qBAAqB,WAAW;AACjH,WAAO;AAAA,EACT,CAAC,GAAG,QAAQ,gBAAgB,eAAe,QAAQ,QAAQ,SAAS,SAAS,SAAS,MAAM,aAAa,aAAa;AACpH,eAAW,gBAAgB,cAAc;AACzC,QAAI,OAAO,IAAI,cAAc,KAAK,SAAS,SAAS,MAAM,WAAW,GAAG,WAAW;AACnF,WAAO,QAAQ,oBAAoB,OAAO,IAAI,OAAO,KAAK,KAAK,EAAE,KAAK,SAAS,QAAQ;AACrF,aAAO,OAAO,OAAO,OAAO,QAAQ,KAAK,KAAK;AAAA,IAChD,CAAC;AAAA,EACH,GAAG,sBAAsB,EAAE,GAAG,OAAO,IAAI,mBAAmB,WAAW,GAAG,OAAO,IAAI,gBAAgB,WAAW;AAC9G,WAAO;AAAA,EACT,CAAC,GAAG,OAAO,IAAI,YAAY,WAAW;AACpC,WAAO;AAAA,EACT,CAAC,GAAG,QAAQ,OAAO,SAAS,KAAK;AAC/B,QAAI,SAAS,OAAO,GAAG,GAAG,OAAO,CAAC;AAClC,aAAS,OAAO;AACd,WAAK,KAAK,GAAG;AACf,WAAO,KAAK,QAAQ,GAAG,SAAS,OAAO;AACrC,aAAO,KAAK,UAAU;AACpB,YAAI,OAAO,KAAK,IAAI;AACpB,YAAI,QAAQ;AACV,iBAAO,KAAK,QAAQ,MAAM,KAAK,OAAO,OAAO;AAAA,MACjD;AACA,aAAO,KAAK,OAAO,MAAM;AAAA,IAC3B;AAAA,EACF,GAAG,QAAQ,SAAS,QAAQ,QAAQ,YAAY;AAAA,IAC9C,aAAa;AAAA,IACb,OAAO,SAAS,eAAe;AAC7B,UAAI,KAAK,OAAO,GAAG,KAAK,OAAO,GAAG,KAAK,OAAO,KAAK,QAAQ,QAAQ,KAAK,OAAO,OAAO,KAAK,WAAW,MAAM,KAAK,SAAS,QAAQ,KAAK,MAAM,QAAQ,KAAK,WAAW,QAAQ,aAAa,GAAG,CAAC;AAC5L,iBAAS,QAAQ;AACf,kBAAQ,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,CAAC,MAAM,KAAK,IAAI,IAAI;AAAA,IACnG;AAAA,IACA,MAAM,WAAW;AACf,WAAK,OAAO;AACZ,UAAI,aAAa,KAAK,WAAW,CAAC,EAAE;AACpC,UAAI,YAAY,WAAW;AACzB,cAAM,WAAW;AACnB,aAAO,KAAK;AAAA,IACd;AAAA,IACA,mBAAmB,SAAS,WAAW;AACrC,UAAI,KAAK;AACP,cAAM;AACR,UAAI,UAAU;AACd,eAAS,OAAO,KAAK,QAAQ;AAC3B,eAAO,OAAO,OAAO,SAAS,OAAO,MAAM,WAAW,QAAQ,OAAO,KAAK,WAAW,QAAQ,SAAS,QAAQ,QAAQ,MAAM,SAAS,CAAC,CAAC;AAAA,MACzI;AACA,eAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,YAAI,QAAQ,KAAK,WAAW,CAAC,GAAG,SAAS,MAAM;AAC/C,YAAI,WAAW,MAAM;AACnB,iBAAO,OAAO,KAAK;AACrB,YAAI,MAAM,UAAU,KAAK,MAAM;AAC7B,cAAI,WAAW,OAAO,KAAK,OAAO,UAAU,GAAG,aAAa,OAAO,KAAK,OAAO,YAAY;AAC3F,cAAI,YAAY,YAAY;AAC1B,gBAAI,KAAK,OAAO,MAAM;AACpB,qBAAO,OAAO,MAAM,UAAU,IAAI;AACpC,gBAAI,KAAK,OAAO,MAAM;AACpB,qBAAO,OAAO,MAAM,UAAU;AAAA,UAClC,WAAW,UAAU;AACnB,gBAAI,KAAK,OAAO,MAAM;AACpB,qBAAO,OAAO,MAAM,UAAU,IAAI;AAAA,UACtC,OAAO;AACL,gBAAI,CAAC;AACH,oBAAM,IAAI,MAAM,wCAAwC;AAC1D,gBAAI,KAAK,OAAO,MAAM;AACpB,qBAAO,OAAO,MAAM,UAAU;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,QAAQ,SAAS,MAAM,KAAK;AAC1B,eAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,YAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,YAAI,MAAM,UAAU,KAAK,QAAQ,OAAO,KAAK,OAAO,YAAY,KAAK,KAAK,OAAO,MAAM,YAAY;AACjG,cAAI,eAAe;AACnB;AAAA,QACF;AAAA,MACF;AACA,uBAAiB,YAAY,QAAQ,eAAe,SAAS,aAAa,UAAU,OAAO,OAAO,aAAa,eAAe,eAAe;AAC7I,UAAI,SAAS,eAAe,aAAa,aAAa,CAAC;AACvD,aAAO,OAAO,OAAO,MAAM,OAAO,MAAM,KAAK,gBAAgB,KAAK,SAAS,QAAQ,KAAK,OAAO,aAAa,YAAY,oBAAoB,KAAK,SAAS,MAAM;AAAA,IAClK;AAAA,IACA,UAAU,SAAS,QAAQ,UAAU;AACnC,UAAI,YAAY,OAAO;AACrB,cAAM,OAAO;AACf,aAAO,YAAY,OAAO,QAAQ,eAAe,OAAO,OAAO,KAAK,OAAO,OAAO,MAAM,aAAa,OAAO,QAAQ,KAAK,OAAO,KAAK,MAAM,OAAO,KAAK,KAAK,SAAS,UAAU,KAAK,OAAO,SAAS,aAAa,OAAO,QAAQ,aAAa,KAAK,OAAO,WAAW;AAAA,IACtQ;AAAA,IACA,QAAQ,SAAS,YAAY;AAC3B,eAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,YAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,YAAI,MAAM,eAAe;AACvB,iBAAO,KAAK,SAAS,MAAM,YAAY,MAAM,QAAQ,GAAG,cAAc,KAAK,GAAG;AAAA,MAClF;AAAA,IACF;AAAA,IACA,OAAO,SAAS,QAAQ;AACtB,eAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,YAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,YAAI,MAAM,WAAW,QAAQ;AAC3B,cAAI,SAAS,MAAM;AACnB,cAAI,YAAY,OAAO,MAAM;AAC3B,gBAAI,SAAS,OAAO;AACpB,0BAAc,KAAK;AAAA,UACrB;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACzC;AAAA,IACA,eAAe,SAAS,UAAU,YAAY,SAAS;AACrD,aAAO,KAAK,WAAW;AAAA,QACrB,UAAU,OAAO,QAAQ;AAAA,QACzB;AAAA,QACA;AAAA,MACF,GAAG,WAAW,KAAK,WAAW,KAAK,MAAM,SAAS;AAAA,IACpD;AAAA,EACF,GAAG;AACL;AACA,SAAS,mBAAmB,KAAK,SAAS,QAAQ,OAAO,QAAQ,KAAK,KAAK;AACzE,MAAI;AACF,QAAI,OAAO,IAAI,GAAG,EAAE,GAAG;AACvB,QAAI,QAAQ,KAAK;AAAA,EACnB,SAAS,OAAO;AACd,WAAO,KAAK;AACZ;AAAA,EACF;AACA,MAAI,KAAK,MAAM;AACb,YAAQ,KAAK;AAAA,EACf,OAAO;AACL,YAAQ,QAAQ,KAAK,EAAE,KAAK,OAAO,MAAM;AAAA,EAC3C;AACF;AACA,SAAS,kBAAkB,IAAI;AAC7B,SAAO,WAAW;AAChB,QAAI,OAAO,MAAM,OAAO;AACxB,WAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,UAAI,MAAM,GAAG,MAAM,MAAM,IAAI;AAC7B,eAAS,MAAM,OAAO;AACpB,2BAAmB,KAAK,SAAS,QAAQ,OAAO,QAAQ,QAAQ,KAAK;AAAA,MACvE;AACA,eAAS,OAAO,KAAK;AACnB,2BAAmB,KAAK,SAAS,QAAQ,OAAO,QAAQ,SAAS,GAAG;AAAA,MACtE;AACA,YAAM,MAAM;AAAA,IACd,CAAC;AAAA,EACH;AACF;AACA,SAAS,gBAAgB,UAAU,aAAa;AAC9C,MAAI,EAAE,oBAAoB,cAAc;AACtC,UAAM,IAAI,UAAU,mCAAmC;AAAA,EACzD;AACF;AACA,SAAS,kBAAkB,QAAQ,OAAO;AACxC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,aAAa,MAAM,CAAC;AACxB,eAAW,aAAa,WAAW,cAAc;AACjD,eAAW,eAAe;AAC1B,QAAI,WAAW;AACb,iBAAW,WAAW;AACxB,WAAO,eAAe,QAAQ,eAAe,WAAW,GAAG,GAAG,UAAU;AAAA,EAC1E;AACF;AACA,SAAS,aAAa,aAAa,YAAY,aAAa;AAC1D,MAAI;AACF,sBAAkB,YAAY,WAAW,UAAU;AACrD,MAAI;AACF,sBAAkB,aAAa,WAAW;AAC5C,SAAO,eAAe,aAAa,aAAa;AAAA,IAC9C,UAAU;AAAA,EACZ,CAAC;AACD,SAAO;AACT;AACA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,QAAM,eAAe,GAAG;AACxB,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AACA,SAAO;AACT;AACA,SAAS,UAAU,UAAU,YAAY;AACvC,MAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAC3D,UAAM,IAAI,UAAU,oDAAoD;AAAA,EAC1E;AACA,WAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW;AAAA,IACrE,aAAa;AAAA,MACX,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO,eAAe,UAAU,aAAa;AAAA,IAC3C,UAAU;AAAA,EACZ,CAAC;AACD,MAAI;AACF,oBAAgB,UAAU,UAAU;AACxC;AACA,SAAS,gBAAgB,GAAG;AAC1B,oBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAS,iBAAiB,IAAI;AACrG,WAAO,GAAG,aAAa,OAAO,eAAe,EAAE;AAAA,EACjD;AACA,SAAO,gBAAgB,CAAC;AAC1B;AACA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,oBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAS,iBAAiB,IAAI,IAAI;AACzG,OAAG,YAAY;AACf,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,GAAG,CAAC;AAC7B;AACA,SAAS,4BAA4B;AACnC,MAAI,OAAO,YAAY,eAAe,CAAC,QAAQ;AAC7C,WAAO;AACT,MAAI,QAAQ,UAAU;AACpB,WAAO;AACT,MAAI,OAAO,UAAU;AACnB,WAAO;AACT,MAAI;AACF,YAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAC,GAAG,WAAW;AAAA,IACzE,CAAC,CAAC;AACF,WAAO;AAAA,EACT,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AACA,SAAS,uBAAuB,MAAM;AACpC,MAAI,SAAS,QAAQ;AACnB,UAAM,IAAI,eAAe,2DAA2D;AAAA,EACtF;AACA,SAAO;AACT;AACA,SAAS,2BAA2B,MAAM,MAAM;AAC9C,MAAI,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,aAAa;AACpE,WAAO;AAAA,EACT,WAAW,SAAS,QAAQ;AAC1B,UAAM,IAAI,UAAU,0DAA0D;AAAA,EAChF;AACA,SAAO,uBAAuB,IAAI;AACpC;AACA,SAAS,aAAa,SAAS;AAC7B,MAAI,4BAA4B,0BAA0B;AAC1D,SAAO,SAAS,uBAAuB;AACrC,QAAI,QAAQ,gBAAgB,OAAO,GAAG;AACtC,QAAI,2BAA2B;AAC7B,UAAI,YAAY,gBAAgB,IAAI,EAAE;AACtC,eAAS,QAAQ,UAAU,OAAO,WAAW,SAAS;AAAA,IACxD,OAAO;AACL,eAAS,MAAM,MAAM,MAAM,SAAS;AAAA,IACtC;AACA,WAAO,2BAA2B,MAAM,MAAM;AAAA,EAChD;AACF;AACA,SAAS,aAAa,OAAO,MAAM;AACjC,MAAI,OAAO,UAAU,YAAY,UAAU;AACzC,WAAO;AACT,MAAI,OAAO,MAAM,OAAO,WAAW;AACnC,MAAI,SAAS,QAAQ;AACnB,QAAI,MAAM,KAAK,KAAK,OAAO,QAAQ,SAAS;AAC5C,QAAI,OAAO,QAAQ;AACjB,aAAO;AACT,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACpE;AACA,UAAQ,SAAS,WAAW,SAAS,QAAQ,KAAK;AACpD;AACA,SAAS,eAAe,KAAK;AAC3B,MAAI,MAAM,aAAa,KAAK,QAAQ;AACpC,SAAO,OAAO,QAAQ,WAAW,MAAM,OAAO,GAAG;AACnD;;;AC3eA,2BAAyB;;;ACEzB,IAAI,SAAS,IAAI,OAAO,eAAe;AACvC,IAAI,gBAAgC,WAAW;AAC7C,WAAS,eAAe,KAAK,WAAW,MAAM;AAC5C,oBAAgB,MAAM,cAAc;AACpC,oBAAgB,MAAM,OAAO,IAAI;AACjC,oBAAgB,MAAM,YAAY,IAAI,WAAW,CAAC;AAClD,oBAAgB,MAAM,YAAY,IAAI;AACtC,oBAAgB,MAAM,QAAQ,IAAI;AAClC,oBAAgB,MAAM,cAAc,IAAI;AACxC,oBAAgB,MAAM,kBAAkB,KAAK;AAC7C,oBAAgB,MAAM,oBAAoB,IAAI;AAC9C,oBAAgB,MAAM,kBAAkB,IAAI;AAC5C,oBAAgB,MAAM,eAAe,KAAK;AAC1C,oBAAgB,MAAM,kBAAkB,EAAE;AAC1C,oBAAgB,MAAM,iBAAiB,IAAI;AAC3C,oBAAgB,MAAM,mBAAmB,CAAC;AAC1C,oBAAgB,MAAM,SAAS,IAAI;AACnC,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,QAAI,WAAW;AACb,WAAK,aAAa;AAAA,IACpB,OAAO;AACL,WAAK,WAAW,IAAI,YAAY,KAAK,SAAS,YAAY,KAAK,SAAS,UAAU;AAClF,WAAK,OAAO,IAAI,IAAI;AACpB,WAAK,KAAK,UAAU,IAAI,KAAK;AAAA,IAC/B;AAAA,EACF;AACA,eAAa,gBAAgB,CAAC;AAAA,IAC5B,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI,gBAAgB;AACpB,cAAQ,iBAAiB,KAAK,cAAc,QAAQ,mBAAmB,SAAS,UAAU,wBAAwB,eAAe,YAAY,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AAAA,IACzN;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB;AAC/B,WAAK,mBAAmB;AACxB,WAAK,iBAAiB;AACtB,WAAK,cAAc;AACnB,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa,WAAW;AACtC,WAAK,cAAc;AACnB,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,SAAS,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,UAAU;AAC3F,YAAI,UAAU,QAAQ;AACtB,eAAO,oBAAoB,EAAE,KAAK,SAAS,SAAS,UAAU;AAC5D,iBAAO;AACL,oBAAQ,SAAS,OAAO,SAAS,MAAM;AAAA,cACrC,KAAK;AACH,2BAAW,MAAM,SAAS,KAAK,MAAM,CAAC,MAAM,SAAS,MAAM,CAAC,IAAI;AAChE,oBAAI,EAAE,KAAK,QAAQ,CAAC,WAAW;AAC7B,2BAAS,OAAO;AAChB;AAAA,gBACF;AACA,yBAAS,OAAO;AAChB,uBAAO,KAAK,KAAK,YAAY;AAAA,cAC/B,KAAK;AACH,yBAAS,OAAO;AAChB,uBAAO,KAAK,KAAK,UAAU,KAAK,IAAI,KAAK;AAAA,cAC3C,KAAK;AACH,qBAAK,mBAAmB;AACxB,qBAAK,iBAAiB;AACtB,qBAAK,cAAc;AACnB,qBAAK,iBAAiB;AACtB,qBAAK,iBAAiB;AAAA,cACxB,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,SAAS,KAAK;AAAA,YACzB;AAAA,QACJ,GAAG,SAAS,IAAI;AAAA,MAClB,CAAC,CAAC;AACF,eAAS,QAAQ;AACf,eAAO,OAAO,MAAM,MAAM,SAAS;AAAA,MACrC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,eAAe,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AAClG,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,oBAAI,CAAC,KAAK,MAAM;AACd,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,oBAAI,CAAC,KAAK,gBAAgB;AACxB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,KAAK,KAAK,YAAY;AAAA,cAC/B,KAAK;AACH,qBAAK,IAAI,KAAK,MAAM,SAAS;AAAA,cAC/B,KAAK;AACH,0BAAU,OAAO;AACjB;AAAA,cACF,KAAK;AACH,oBAAI,KAAK,YAAY;AACnB,uBAAK,WAAW,YAAY;AAAA,gBAC9B;AAAA,cACF,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,cAAc;AACrB,eAAO,aAAa,MAAM,MAAM,SAAS;AAAA,MAC3C;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,kBAAkB,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,SAAS,UAAU;AAC7G,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,oBAAI,CAAC,KAAK,MAAM;AACd,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,oBAAI,KAAK,KAAK,UAAU;AACtB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,KAAK,KAAK,KAAK;AAAA,cACxB,KAAK;AACH,uBAAO,MAAM,mBAAmB,QAAQ;AACxC,0BAAU,OAAO;AACjB,uBAAO,KAAK,KAAK,eAAe,QAAQ;AAAA,cAC1C,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,eAAe,IAAI;AAC1B,eAAO,gBAAgB,MAAM,MAAM,SAAS;AAAA,MAC9C;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,WAAW,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AAC9F,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,oBAAI,CAAC,KAAK,MAAM;AACd,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,KAAK,KAAK,YAAY;AAAA,cAC/B,KAAK;AACH,qBAAK,OAAO;AACZ,qBAAK,aAAa;AAClB,qBAAK,WAAW;AAChB,qBAAK,WAAW;AAAA,cAClB,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,UAAU;AACjB,eAAO,SAAS,MAAM,MAAM,SAAS;AAAA,MACvC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,gBAAgB,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,SAAS,OAAO;AACxG,YAAI,SAAS,YAAY,YAAY,eAAe,YAAY,YAAY,UAAU,OAAO,WAAW,WAAW,KAAK,OAAO,aAAa;AAC5I,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,oBAAI,KAAK,eAAe;AACtB,0BAAQ,iBAAiB,KAAK,eAAe,KAAK;AAClD,uBAAK,gBAAgB;AAAA,gBACvB;AACA,0BAAU,KAAK;AACf,oBAAI,EAAE,CAAC,SAAS,CAAC,MAAM,UAAU,CAAC,UAAU;AAC1C,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,0BAAU,OAAO;AACjB,wBAAQ,YAAY,OAAO,KAAK,gBAAgB,KAAK,aAAa,KAAK,eAAe;AACtF,0BAAU,OAAO;AACjB;AAAA,cACF,KAAK;AACH,0BAAU,OAAO;AACjB,0BAAU,KAAK,UAAU,OAAO,EAAE,CAAC;AACnC,sBAAM,IAAI,eAAe,IAAI,OAAO,IAAI,UAAU,KAAK,UAAU,EAAE;AAAA,cACrE,KAAK;AACH,6BAAa,QAAQ,YAAY,aAAa,QAAQ,YAAY,gBAAgB,QAAQ;AAC1F,6BAAa,WAAW,MAAM;AAC9B,6BAAa,WAAW,MAAM;AAC9B,oBAAI,KAAK,MAAM,WAAW;AACxB,+BAAa;AACb,6BAAW,UAAU;AAAA,gBACvB;AACA,oBAAI,KAAK,MAAM,WAAW;AACxB,+BAAa;AACb,6BAAW,UAAU;AAAA,gBACvB;AACA,oBAAI,EAAE,CAAC,cAAc,WAAW,WAAW,CAAC,cAAc,WAAW,UAAU;AAC7E,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,2BAAW;AACX,wBAAQ,aAAa,aAAa,aAAa,aAAa;AAC5D,oBAAI,SAAS,MAAM,QAAQ,QAAQ;AACjC,8BAAY,MAAM,QAAQ,MAAM,QAAQ,SAAS,CAAC,EAAE,YAAY,MAAM,QAAQ,CAAC,EAAE,aAAa,MAAM,YAAY;AAAA,gBAClH;AACA,oBAAI,EAAE,WAAW,KAAK,MAAM,kBAAkB;AAC5C,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,KAAK,8BAA8B,OAAO,UAAU,IAAI,GAAG,KAAK;AACvE,2BAAW,UAAU;AACrB,2BAAW,UAAU;AACrB,qBAAK,IAAI,KAAK,MAAM,2BAA2B;AAAA,kBAC7C;AAAA,gBACF,CAAC;AACD,0BAAU,OAAO;AACjB;AAAA,cACF,KAAK;AACH,qBAAK,gBAAgB;AACrB,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,4BAAY,WAAW;AACvB,4BAAY,WAAW;AACvB,qBAAK,YAAY,YAAY,YAAY,aAAa;AACtD,qBAAK,iBAAiB;AACtB,qBAAK,cAAc;AACnB,qBAAK,kBAAkB;AACvB,sBAAM,KAAK;AACX,qBAAK,IAAI,KAAK,MAAM,eAAe;AAAA,kBACjC;AAAA,gBACF,CAAC;AACD,wBAAQ,GAAG,OAAO,WAAW,OAAO,GAAG,EAAE,OAAO,WAAW,OAAO,GAAG,EAAE,OAAO,WAAW,QAAQ,GAAG,EAAE,OAAO,WAAW,OAAO,GAAG,EAAE,OAAO,WAAW,MAAM;AAC5J,oBAAI,UAAU,KAAK,gBAAgB;AACjC,uBAAK,mBAAmB;AACxB,uBAAK,iBAAiB;AACtB,uBAAK,qBAAqB,YAAY,UAAU;AAAA,gBAClD;AACA,oBAAI,CAAC,KAAK;AACR,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,oBAAI,KAAK,gBAAgB;AACvB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,IAAI,KAAK;AAAA,cAClB,KAAK;AACH,oBAAI,YAAY;AACd,yBAAO,IAAI,2BAA2B,OAAO,WAAW,KAAK,CAAC;AAC9D,sBAAI,aAAa,WAAW,oBAAoB,OAAO,WAAW,KAAK,CAAC;AAAA,gBAC1E;AACA,oBAAI,YAAY;AACd,yBAAO,IAAI,2BAA2B,OAAO,WAAW,KAAK,CAAC;AAC9D,sBAAI,aAAa,WAAW,oBAAoB,OAAO,WAAW,KAAK,CAAC;AAAA,gBAC1E;AACA,qBAAK,iBAAiB;AACtB,qBAAK,IAAI,KAAK,MAAM,oBAAoB;AAAA,cAC1C,KAAK;AACH,0BAAU,OAAO;AACjB,8BAAc,KAAK,SAAS,MAAM,KAAK,gBAAgB;AACvD,0BAAU,OAAO;AACjB;AAAA,cACF,KAAK;AACH,0BAAU,OAAO;AACjB,0BAAU,KAAK,UAAU,OAAO,EAAE,EAAE;AACpC,sBAAM,IAAI,eAAe,IAAI,OAAO,IAAI,UAAU,MAAM,UAAU,EAAE;AAAA,cACtE,KAAK;AACH,oBAAI,EAAE,KAAK,oBAAoB,CAAC,YAAY,oBAAoB,CAAC,YAAY,mBAAmB;AAC9F,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,qBAAK,mBAAmB;AACxB,oBAAI,CAAC;AACL,oBAAI,YAAY;AACd,oBAAE,KAAK,IAAI,OAAO,WAAW,YAAY,gBAAgB,CAAC;AAC5D,oBAAI,YAAY;AACd,oBAAE,KAAK,IAAI,OAAO,WAAW,YAAY,gBAAgB,CAAC;AAC5D,oBAAI,YAAY;AACd,oBAAE,KAAK,IAAI,OAAO,WAAW,YAAY,YAAY,CAAC;AACxD,oBAAI,YAAY;AACd,oBAAE,KAAK,IAAI,OAAO,WAAW,YAAY,YAAY,CAAC;AACxD,uBAAO,UAAU,OAAO,UAAU,QAAQ,IAAI,CAAC,CAAC;AAAA,cAClD,KAAK;AACH,oBAAI,KAAK,YAAY;AACnB,uBAAK,WAAW,aAAa,YAAY,UAAU;AAAA,gBACrD;AAAA,cACF,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;AAAA,MACvC,CAAC,CAAC;AACF,eAAS,aAAa,KAAK;AACzB,eAAO,cAAc,MAAM,MAAM,SAAS;AAAA,MAC5C;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,eAAe,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,SAAS,cAAc;AAC9G,YAAI,QAAQ;AACZ,YAAI,OAAO,aAAa,WAAW;AACnC,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,wBAAQ,KAAK,IAAI;AACjB,oBAAI,EAAE,CAAC,KAAK,QAAQ,CAAC,KAAK,YAAY,CAAC,SAAS,CAAC,gBAAgB,eAAe,IAAI;AAClF,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,8BAAc,MAAM;AACpB,4BAAY,cAAc;AAC1B,oBAAI,EAAE,aAAa,IAAI;AACrB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,wBAAQ,OAAO,MAAM,OAAO,IAAI,KAAK,CAAC;AACtC,oBAAI,EAAE,QAAQ,KAAK,YAAY;AAC7B,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,uBAAO,UAAU,OAAO,UAAU,KAAK,KAAK,YAAY,GAAG,SAAS,EAAE,KAAK,WAAW;AACpF,yBAAO,MAAM,IAAI,KAAK,MAAM,eAAe;AAAA,oBACzC;AAAA,kBACF,CAAC;AAAA,gBACH,CAAC,CAAC;AAAA,cACJ,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,YAAY,KAAK;AACxB,eAAO,aAAa,MAAM,MAAM,SAAS;AAAA,MAC3C;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,qBAAqB,YAAY,YAAY;AAC3D,UAAI,WAAW,MAAM,GAAG;AACtB,aAAK,IAAI,KAAK,MAAM,iBAAiB;AAAA,UACnC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,YACJ,OAAO,WAAW;AAAA,YAClB,WAAW,WAAW;AAAA,YACtB,OAAO,WAAW;AAAA,YAClB,QAAQ,WAAW;AAAA,YACnB,UAAU,WAAW;AAAA,YACrB,SAAS,WAAW;AAAA,UACtB;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,WAAW,MAAM,GAAG;AACtB,aAAK,IAAI,KAAK,MAAM,iBAAiB;AAAA,UACnC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,YACJ,OAAO,WAAW;AAAA,YAClB,cAAc,WAAW;AAAA,YACzB,YAAY,WAAW;AAAA,YACvB,WAAW,WAAW;AAAA,YACtB,SAAS,WAAW;AAAA,UACtB;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO,MAAM,gBAAgB,YAAY,UAAU;AAAA,IACrD;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,YAAY,YAAY,eAAe;AACjE,UAAI,SAAS;AACb,aAAO,MAAM,WAAW,SAAS,WAAW,OAAO;AACnD,oBAAc,iBAAiB,QAAQ,SAAS,QAAQ;AACtD,eAAO,IAAI,KAAK,MAAM,iBAAiB,MAAM;AAC7C,eAAO,MAAM,iBAAiB,MAAM;AAAA,MACtC,CAAC;AACD,iBAAW,QAAQ,QAAQ,SAAS,QAAQ;AAC1C,YAAI,OAAO,UAAU;AACnB,iBAAO,IAAI,KAAK,MAAM,UAAU;AAAA,YAC9B,KAAK,OAAO;AAAA,UACd,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,iBAAW,SAAS,QAAQ,SAAS,MAAM;AACzC,YAAI;AACJ,gBAAQ,KAAK,MAAM;AAAA,UACjB,KAAK,YAAY;AACf,mBAAO,MAAM;AACb;AAAA,UACF,KAAK,YAAY;AACf,mBAAO,MAAM;AACb;AAAA,UACF,KAAK,YAAY;AACf,mBAAO,MAAM;AACb;AAAA,QACJ;AACA,YAAI;AACF,iBAAO,IAAI,KAAK,MAAM,kBAAkB,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,YACnF;AAAA,UACF,CAAC,CAAC;AACJ,eAAO,KAAK,mBAAmB,IAAI;AAAA,MACrC,CAAC;AACD,iBAAW,SAAS,QAAQ,SAAS,MAAM;AACzC,YAAI;AACJ,gBAAQ,KAAK,MAAM;AAAA,UACjB,KAAK,YAAY;AACf,mBAAO,MAAM;AACb;AAAA,UACF,KAAK,YAAY;AACf,mBAAO,MAAM;AACb;AAAA,UACF,KAAK,YAAY;AACf,mBAAO,MAAM;AACb;AAAA,QACJ;AACA,YAAI;AACF,iBAAO,IAAI,KAAK,MAAM,kBAAkB,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,YACnF;AAAA,UACF,CAAC,CAAC;AACJ,eAAO,KAAK,mBAAmB,IAAI;AAAA,MACrC,CAAC;AACD,oBAAc,WAAW,QAAQ,SAAS,KAAK;AAC7C,eAAO,IAAI,KAAK,MAAM,KAAK,eAAe,eAAe,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG;AAAA,UACrE,KAAK;AAAA,YACH,MAAM,IAAI,KAAK;AAAA,YACf,SAAS,IAAI,KAAK;AAAA,YAClB,KAAK,IAAI;AAAA,UACX;AAAA,QACF,CAAC,CAAC;AAAA,MACJ,CAAC;AAAA,IACH;AAAA,EACF,CAAC,CAAC;AACF,SAAO;AACT,EAAE;;;AC5dF,SAAS,UAAU,MAAM;AACvB,MAAI,MAAM,eAAe;AAAA,IACvB,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,uBAAuB;AAAA,IACvB,WAAW;AAAA,IACX,WAAW;AAAA,EACb,GAAG,IAAI;AACP,MAAI,IAAI,QAAQ;AACd,QAAI,IAAI,aAAa;AACnB,UAAI,CAAC,IAAI,YAAY;AACnB,YAAI,aAAa,IAAI,cAAc;AAAA,MACrC;AACA,UAAI,CAAC,IAAI,eAAe;AACtB,YAAI,gBAAgB,IAAI;AAAA,MAC1B;AACA,UAAI,IAAI,mBAAmB,QAAQ,IAAI,mBAAmB,QAAQ;AAChE,YAAI,iBAAiB,IAAI;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;AChCA,SAAS,oBAAoB,MAAM,OAAO;AACxC,MAAI,MAAM;AACV,MAAI,OAAO,KAAK,SAAS;AACzB,MAAI,MAAM;AACV,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAI,QAAQ,KAAK,CAAC,GAAG;AACnB,UAAM;AACN,aAAS,SAAS;AAAA,EACpB;AACA,SAAO,UAAU,QAAQ;AACvB,UAAM,SAAS,KAAK,OAAO,SAAS,UAAU,CAAC;AAC/C,QAAI,QAAQ,QAAQ,SAAS,KAAK,GAAG,KAAK,QAAQ,KAAK,MAAM,CAAC,GAAG;AAC/D,YAAM;AACN;AAAA,IACF,WAAW,KAAK,GAAG,IAAI,OAAO;AAC5B,eAAS,MAAM;AAAA,IACjB,OAAO;AACL,eAAS,MAAM;AAAA,IACjB;AAAA,EACF;AACA,SAAO;AACT;;;AHfA,IAAIA,UAAS,IAAI,OAAO,KAAK;AAC7B,IAAI,MAAsB,SAAS,eAAe;AAChD,YAAU,MAAM,aAAa;AAC7B,MAAI,SAAS,aAAa,IAAI;AAC9B,WAAS,KAAK,OAAO;AACnB,QAAI;AACJ,oBAAgB,MAAM,IAAI;AAC1B,YAAQ,OAAO,KAAK,IAAI;AACxB,oBAAgB,uBAAuB,KAAK,GAAG,SAAS,IAAI;AAC5D,oBAAgB,uBAAuB,KAAK,GAAG,YAAY,KAAK;AAChE,oBAAgB,uBAAuB,KAAK,GAAG,SAAS,IAAI;AAC5D,oBAAgB,uBAAuB,KAAK,GAAG,kBAAkB,IAAI;AACrE,oBAAgB,uBAAuB,KAAK,GAAG,eAAe,IAAI;AAClE,oBAAgB,uBAAuB,KAAK,GAAG,UAAU,IAAI;AAC7D,oBAAgB,uBAAuB,KAAK,GAAG,gBAAgB,IAAI;AACnE,oBAAgB,uBAAuB,KAAK,GAAG,sBAAsB,IAAI;AACzE,oBAAgB,uBAAuB,KAAK,GAAG,cAAc,IAAI;AACjE,oBAAgB,uBAAuB,KAAK,GAAG,iBAAiB,GAAG;AACnE,oBAAgB,uBAAuB,KAAK,GAAG,iBAAiB,KAAK;AACrE,oBAAgB,uBAAuB,KAAK,GAAG,sBAAsB,KAAK;AAC1E,oBAAgB,uBAAuB,KAAK,GAAG,cAAc,IAAI;AACjE,oBAAgB,uBAAuB,KAAK,GAAG,iBAAiB,IAAI;AACpE,oBAAgB,uBAAuB,KAAK,GAAG,eAA+B,WAAW;AACvF,UAAI,QAAQ,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,QAAQ,OAAO,MAAM,MAAM,UAAU;AACrH,YAAI,WAAW,SAAS,IAAI,eAAe,oBAAoB,SAAS,sBAAsB;AAC9F,eAAO,oBAAoB,EAAE,KAAK,SAAS,SAAS,UAAU;AAC5D,iBAAO;AACL,oBAAQ,SAAS,OAAO,SAAS,MAAM;AAAA,cACrC,KAAK;AACH,4BAAY,KAAK,WAAW,UAAU,KAAK,SAAS,KAAK,KAAK,IAAI,gBAAgB,KAAK;AACvF,sBAAM,WAAW,CAAC;AAClB,oBAAI,MAAM,oBAAoB;AAC5B,2BAAS,OAAO;AAChB;AAAA,gBACF;AACA,oBAAI,MAAM,OAAO;AACf,2BAAS,OAAO;AAChB;AAAA,gBACF;AACA,iBAAC,qBAAqB,MAAM,kBAAkB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,OAAO;AACzH,uBAAO,SAAS,OAAO,QAAQ;AAAA,cACjC,KAAK;AACH,0BAAU,SAAS;AACnB,sBAAM,KAAK,MAAM,MAAM;AAAA,kBACrB,KAAK,MAAM,MAAM;AAAA,kBACjB,aAAa,SAAS;AAAA,kBACtB,SAAS,KAAK,gBAAgB,KAAK,UAAU;AAAA,gBAC/C,CAAC;AACD,sBAAM,KAAK,MAAM,uBAAuB;AAAA,kBACtC;AAAA,gBACF,CAAC;AACD,sBAAM,gBAAgB,CAAC,EAAE,YAAY,QAAQ,YAAY,UAAU,QAAQ,IAAI,eAAe,MAAM,CAAC,EAAE,YAAY,QAAQ,YAAY,UAAU,QAAQ,IAAI,eAAe;AAC5K,sBAAM,qBAAqB;AAAA,cAC7B,KAAK;AACH,oBAAI,MAAM,gBAAgB;AACxB,2BAAS,OAAO;AAChB;AAAA,gBACF;AACA,uBAAO,SAAS,OAAO,QAAQ;AAAA,cACjC,KAAK;AACH,6BAAa,MAAM,kBAAkB;AACrC,sBAAM,kBAAkB,eAAe,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,YAAY,UAAU,SAAS;AAC1H,yBAAS,OAAO;AAChB,yBAAS,OAAO;AAChB,uBAAO,MAAM,eAAe,aAAa,KAAK;AAAA,cAChD,KAAK;AACH,iBAAC,uBAAuB,MAAM,oBAAoB,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,YAAY,MAAM,MAAM,YAAY;AAC9J,yBAAS,OAAO;AAChB;AAAA,cACF,KAAK;AACH,yBAAS,OAAO;AAChB,yBAAS,KAAK,SAAS,OAAO,EAAE,EAAE;AAClC,uBAAO,SAAS,OAAO,UAAU,MAAM,WAAW,eAAe,OAAO,SAAS,EAAE,CAAC,CAAC;AAAA,cACvF,KAAK;AACH,oBAAI,MAAM,eAAe;AACvB,wBAAM,gBAAgB;AACtB,wBAAM,KAAK,MAAM,oBAAoB;AAAA,oBACnC,KAAK,MAAM,MAAM;AAAA,kBACnB,CAAC;AAAA,gBACH;AACA,oBAAI,MAAM,oBAAoB;AAC5B,wBAAM,qBAAqB;AAC3B,wBAAM,MAAM;AAAA,gBACd;AACA,oBAAI,EAAE,QAAQ,CAAC,MAAM,MAAM,UAAU;AACnC,2BAAS,OAAO;AAChB;AAAA,gBACF;AACA,sBAAM,KAAK,MAAM,aAAa;AAC9B,gBAAAA,QAAO,MAAM,WAAW;AACxB,uBAAO,SAAS,OAAO,UAAU,MAAM,KAAK,CAAC;AAAA,cAC/C,KAAK;AACH,oCAAoB,MAAM,MAAM;AAChC,oBAAI,mBAAmB;AACrB,+BAAa,MAAM,kBAAkB;AACrC,wBAAM,qBAAqB,WAAW,WAAW;AAC/C,oBAAAA,QAAO,MAAM,kBAAkB,iBAAiB;AAChD,0BAAM,KAAK;AAAA,kBACb,GAAG,iBAAiB;AAAA,gBACtB;AAAA,cACF,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,SAAS,KAAK;AAAA,YACzB;AAAA,QACJ,GAAG,SAAS,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAAA,MAC9B,CAAC,CAAC;AACF,aAAO,SAAS,IAAI,KAAK,KAAK,KAAK;AACjC,eAAO,MAAM,MAAM,MAAM,SAAS;AAAA,MACpC;AAAA,IACF,EAAE,CAAC;AACH,oBAAgB,uBAAuB,KAAK,GAAG,iBAAiB,SAAS,OAAO,WAAW;AACzF,MAAAA,QAAO,MAAM,cAAc,OAAO,SAAS;AAC3C,YAAM,KAAK,MAAM,YAAY;AAAA,QAC3B,OAAO,eAAe,QAAQ,KAAK;AAAA,QACnC;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,oBAAgB,uBAAuB,KAAK,GAAG,QAAQ,WAAW;AAChE,YAAM,OAAO;AACb,UAAI,MAAM,gBAAgB;AACxB,cAAM,eAAe,YAAY;AAAA,MACnC;AACA,MAAAA,QAAO,MAAM,YAAY;AAAA,IAC3B,CAAC;AACD,oBAAgB,uBAAuB,KAAK,GAAG,SAAS,WAAW;AACjE,mBAAa,MAAM,UAAU;AAC7B,UAAI,wBAAwB,uBAAuB,KAAK,GAAG,QAAQ,sBAAsB;AACzF,UAAI,CAAC;AACH;AACF,YAAM,aAAa,WAAW,MAAM,OAAO,MAAM,aAAa;AAC9D,UAAI,YAAY,OAAO,IAAI,OAAO,IAAI,KAAK,CAAC;AAC5C,UAAI,YAAY,OAAO,CAAC,MAAM;AAC5B;AACF,UAAI,OAAO,MAAM;AACjB,UAAI,eAAe,KAAK,GAAG;AACzB,YAAI,MAAM,aAAa;AACrB,gBAAM,YAAY,GAAG,OAAO,KAAK,iBAAiB,MAAM,QAAQ,CAAC;AAAA,QACnE;AAAA,MACF,OAAO;AACL,YAAI,CAAC,MAAM,eAAe,MAAM,aAAa;AAC3C,gBAAM,YAAY,GAAG,OAAO,KAAK,iBAAiB,MAAM,QAAQ,CAAC;AACjE;AAAA,QACF;AACA,YAAI,KAAK,UAAU,YAAY,KAAK,gBAAgB;AAClD,gBAAM,WAAW;AAAA,QACnB;AAAA,MACF;AAAA,IACF,CAAC;AACD,oBAAgB,uBAAuB,KAAK,GAAG,WAAW,WAAW;AACnE,UAAI,aAAa;AACjB,UAAI,YAAY,MAAM,MAAM,gBAAgB,cAAc,MAAM,WAAW,QAAQ,gBAAgB,SAAS,UAAU,uBAAuB,YAAY,cAAc,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB;AAC/O,UAAI,MAAM,UAAU,CAAC,MAAM,YAAY,WAAW;AAChD,cAAM,OAAO,QAAQ,IAAI;AAAA,MAC3B;AAAA,IACF,CAAC;AACD,oBAAgB,uBAAuB,KAAK,GAAG,cAA8B,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AAC5J,UAAI,OAAO,eAAe,aAAa,GAAG;AAC1C,aAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,eAAO;AACL,kBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,YACvC,KAAK;AACH,kBAAI,EAAE,CAAC,MAAM,UAAU,MAAM,WAAW;AACtC,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,sBAAQ,MAAM,WAAW;AACzB,8BAAgB,MAAM,WAAW;AACjC,kBAAI,EAAE,EAAE,UAAU,QAAQ,UAAU,UAAU,MAAM,WAAW,EAAE,kBAAkB,QAAQ,kBAAkB,UAAU,cAAc,UAAU;AAC7I,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,qBAAO,UAAU,OAAO,QAAQ;AAAA,YAClC,KAAK;AACH,4BAAc,MAAM,MAAM;AAC1B,kBAAI,oBAAoB,MAAM,WAAW,OAAO,WAAW;AAC3D,0BAAY,cAAc,CAAC;AAC3B,kBAAI,EAAE,cAAc,QAAQ,cAAc,SAAS;AACjD,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,qBAAO,UAAU,OAAO,QAAQ;AAAA,YAClC,KAAK;AACH,wBAAU,OAAO;AACjB,qBAAO,MAAM,aAAa,OAAO;AAAA,YACnC,KAAK;AACH,oBAAM,UAAU,MAAM,CAAC,SAAS,CAAC;AACjC,oBAAM,eAAe,aAAa,MAAM,CAAC,CAAC;AAAA,YAC5C,KAAK;AAAA,YACL,KAAK;AACH,qBAAO,UAAU,KAAK;AAAA,UAC1B;AAAA,MACJ,GAAG,QAAQ;AAAA,IACb,CAAC,CAAC,CAAC;AACH,oBAAgB,uBAAuB,KAAK,GAAG,iBAAiB,WAAW;AACzE,UAAI,CAAC,MAAM;AACT;AACF,UAAI,OAAO,MAAM;AACjB,UAAI,cAAc,MAAM,MAAM;AAC9B,UAAI,KAAK,UAAU,KAAK,cAAc,KAAK,eAAe;AACxD,YAAI,YAAY,OAAO,IAAI,OAAO,IAAI,MAAM,KAAK,CAAC;AAClD,YAAI,UAAU,YAAY;AAC1B,YAAI,WAAW,KAAK,YAAY;AAC9B,gBAAM,MAAM,cAAc,YAAY,KAAK;AAAA,QAC7C;AAAA,MACF;AACA,YAAM,YAAY,MAAM,WAAW;AAAA,IACrC,CAAC;AACD,oBAAgB,uBAAuB,KAAK,GAAG,oBAAoB,SAAS,QAAQ;AAClF,UAAI,cAAc,uBAAuB,eAAe;AACxD,UAAI,aAAa,eAAe,OAAO,UAAU,QAAQ,iBAAiB,SAAS,UAAU,wBAAwB,aAAa,gBAAgB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AAC5N,UAAI,YAAY,gBAAgB,OAAO,UAAU,QAAQ,kBAAkB,SAAS,UAAU,wBAAwB,cAAc,gBAAgB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AAC9N,UAAI,WAAW;AACb,cAAM,aAAa;AAAA,MACrB;AACA,UAAI,CAAC,MAAM,MAAM,UAAU,UAAU;AACnC,cAAM,eAAe,eAAe,QAAQ;AAAA,MAC9C;AAAA,IACF,CAAC;AACD,UAAM,QAAQ,UAAU,KAAK;AAC7B,UAAM,QAAQ,MAAM,MAAM,SAAS,SAAS,cAAc,OAAO;AACjE,UAAM,MAAM,QAAQ;AACpB,UAAM,qBAAqB;AAC3B,UAAM,eAAe,IAAI,UAAU,eAAe,eAAe,CAAC,GAAG,MAAM,MAAM,YAAY,GAAG,CAAC,GAAG;AAAA,MAClG,OAAO,MAAM,MAAM;AAAA,MACnB,YAAY,MAAM,MAAM;AAAA,MACxB,SAAS,MAAM,MAAM;AAAA,MACrB,cAAc,MAAM;AAAA,MACpB,YAAY,MAAM;AAAA,MAClB,cAAc;AAAA,IAChB,CAAC,CAAC;AACF,UAAM,iBAAiB,IAAI,cAAc,uBAAuB,KAAK,GAAG,MAAM,MAAM,aAAa,MAAM,QAAQ,QAAQ,MAAM,KAAK;AAClI,UAAM,cAAc,IAAI,WAAW,uBAAuB,KAAK,CAAC;AAChE,UAAM,oBAAoB,IAAI,iBAAiB;AAC/C,UAAM,SAAS,IAAI,kBAAkB,uBAAuB,KAAK,CAAC;AAClE,QAAI,CAAC,MAAM,MAAM,YAAY;AAC3B,YAAM,cAAc,IAAI,WAAW;AAAA,IACrC;AACA,UAAM,MAAM,iBAAiB,QAAQ,MAAM,OAAO;AAClD,UAAM,MAAM,iBAAiB,WAAW,MAAM,UAAU;AACxD,UAAM,MAAM,iBAAiB,cAAc,MAAM,aAAa;AAC9D,UAAM,GAAG,MAAM,iBAAiB,MAAM,gBAAgB;AACtD,WAAO;AAAA,EACT;AACA,eAAa,MAAM,CAAC;AAAA,IAClB,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO,KAAK,MAAM;AAAA,IACpB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,cAAQ,wBAAwB,KAAK,oBAAoB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AAAA,IACrI;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO,CAAC,CAAC,KAAK,cAAc,KAAK;AAAA,IACnC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY;AAC1B,aAAO;AAAA,QACL,OAAO,KAAK,kBAAkB,eAAe;AAAA,QAC7C,UAAU,KAAK,kBAAkB,YAAY;AAAA,MAC/C;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW;AACzB,aAAO,KAAK,OAAO,SAAS;AAAA,IAC9B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa;AAC3B,UAAI;AACJ,UAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI;AAC/E,aAAO,OAAO,KAAK,OAAO,IAAI,KAAK,KAAK,IAAI,eAAe,KAAK,WAAW,QAAQ,iBAAiB,SAAS,SAAS,aAAa,aAAa,OAAO;AAAA,IACzJ;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,kBAAkB;AAChC,aAAO,wBAAwB,KAAK,KAAK;AAAA,IAC3C;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,QAAQ,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,SAAS,KAAK;AAC9F,YAAI,UAAU,SAAS;AACvB,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,2BAAW,OAAO,SAAS,KAAK,OAAO,CAAC,MAAM,SAAS,OAAO,CAAC,IAAI;AACnE,oBAAI,KAAK,gBAAgB;AACvB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,0BAAU,OAAO;AACjB,uBAAO,KAAK,OAAO,QAAQ;AAAA,cAC7B,KAAK;AACH,qBAAK,UAAU,GAAG;AAClB,6BAAa,KAAK,UAAU;AAC5B,qBAAK,aAAa,WAAW,KAAK,OAAO,KAAK,aAAa;AAAA,cAC7D,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,KAAK,KAAK;AACjB,eAAO,MAAM,MAAM,MAAM,SAAS;AAAA,MACpC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,UAAU,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AAC7F,YAAI,SAAS;AACb,YAAI,kBAAkB,YAAY,SAAS;AAC3C,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,mCAAmB,OAAO,SAAS,KAAK,OAAO,CAAC,MAAM,SAAS,OAAO,CAAC,IAAI,KAAK,MAAM;AACtF,6BAAa,OAAO,SAAS,IAAI,OAAO,CAAC,IAAI;AAC7C,oBAAI,KAAK,OAAO;AACd,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,oBAAI,CAAC,kBAAkB;AACrB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,KAAK,OAAO;AAAA,cACrB,KAAK;AACH,2BAAW,WAAW;AACpB,yBAAO,UAAU,OAAO,MAAM,GAAG;AACjC,yBAAO,eAAe,eAAe;AACrC,yBAAO,qBAAqB;AAAA,gBAC9B,CAAC;AACD,0BAAU,OAAO;AACjB;AAAA,cACF,KAAK;AACH,0BAAU,OAAO;AACjB,uBAAO,KAAK,KAAK;AAAA,cACnB,KAAK;AACH,uBAAO,UAAU,OAAO,UAAU,KAAK,MAAM,KAAK,CAAC,UAAU,EAAE,MAAM,WAAW;AAAA,gBAChF,CAAC,CAAC;AAAA,cACJ,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,SAAS;AAChB,eAAO,QAAQ,MAAM,MAAM,SAAS;AAAA,MACtC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa;AAC3B,MAAAA,QAAO,MAAM,aAAa;AAC1B,aAAO,KAAK,OAAO;AAAA,IACrB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,aAAa,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,SAAS,KAAK,UAAU;AAC7G,YAAI,SAAS;AACb,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,oBAAI,KAAK,gBAAgB;AACvB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,oBAAI,EAAE,CAAC,YAAY,CAAC,KAAK,MAAM,SAAS;AACtC,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,KAAK,KAAK,GAAG;AAAA,cACtB,KAAK;AACH,qBAAK,gBAAgB;AACrB,uBAAO,UAAU,OAAO,UAAU,KAAK,MAAM,KAAK,IAAI,EAAE,MAAM,WAAW;AAAA,gBACzE,CAAC,CAAC;AAAA,cACJ,KAAK;AACH,0BAAU,OAAO;AACjB,uBAAO,KAAK,OAAO;AAAA,cACrB,KAAK;AACH,2BAAW,WAAW;AACpB,yBAAO,UAAU,GAAG;AACpB,yBAAO,eAAe,eAAe;AACrC,yBAAO,gBAAgB;AACvB,yBAAO,qBAAqB;AAAA,gBAC9B,CAAC;AAAA,cACH,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,UAAU,KAAK,KAAK;AAC3B,eAAO,WAAW,MAAM,MAAM,SAAS;AAAA,MACzC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,WAAW,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AAC9F,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,oBAAI,KAAK,OAAO;AACd,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,qBAAK,mBAAmB;AACxB,qBAAK,YAAY,MAAM;AACvB,qBAAK,MAAM,oBAAoB,QAAQ,KAAK,OAAO;AACnD,qBAAK,MAAM,oBAAoB,WAAW,KAAK,UAAU;AACzD,qBAAK,MAAM,oBAAoB,cAAc,KAAK,aAAa;AAC/D,0BAAU,OAAO;AACjB,uBAAO,QAAQ,IAAI,CAAC,KAAK,OAAO,GAAG,KAAK,eAAe,QAAQ,CAAC,CAAC;AAAA,cACnE,KAAK;AACH,qBAAK,QAAQ;AACb,qBAAK,iBAAiB;AAAA,cACxB,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,UAAU;AACjB,eAAO,SAAS,MAAM,MAAM,SAAS;AAAA,MACvC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW,OAAO;AAChC,UAAI;AACJ,UAAI,cAAc,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI;AACnF,MAAAA,QAAO,MAAM,KAAK;AAClB,MAAAA,QAAO,MAAM,KAAK;AAClB,MAAAA,QAAO,OAAO,eAAe,KAAK,WAAW,QAAQ,iBAAiB,SAAS,SAAS,aAAa,KAAK;AAC1G,UAAI,KAAK,eAAe;AACtB,aAAK,gBAAgB;AACrB,aAAK,qBAAqB;AAC1B,aAAK,KAAK,MAAM,mBAAmB,KAAK;AAAA,MAC1C;AACA,WAAK,KAAK,MAAM,OAAO,KAAK;AAC5B,UAAI,aAAa;AACf,aAAK,YAAY,MAAM;AACvB,aAAK,KAAK;AAAA,MACZ;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,UAAU,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AAC7F,YAAI,UAAU,SAAS;AACvB,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,2BAAW,OAAO,SAAS,KAAK,OAAO,CAAC,MAAM,SAAS,OAAO,CAAC,IAAI;AACnE,qBAAK,YAAY,MAAM;AACvB,qBAAK,kBAAkB,MAAM;AAC7B,qBAAK,OAAO,MAAM;AAClB,0BAAU,OAAO;AACjB,uBAAO,KAAK,OAAO;AAAA,cACrB,KAAK;AACH,0BAAU,OAAO;AACjB,uBAAO,KAAK,eAAe,MAAM,QAAQ;AAAA,cAC3C,KAAK;AACH,qBAAK,qBAAqB;AAAA,cAC5B,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,SAAS;AAChB,eAAO,QAAQ,MAAM,MAAM,SAAS;AAAA,MACtC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,aAAa,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,SAAS,KAAK,OAAO;AAC1G,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,oBAAI;AACF,uBAAK,MAAM,MAAM;AACnB,sBAAM,KAAK,MAAM;AACjB,oBAAI,KAAK;AACP,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,sBAAM,IAAI,MAAM,uBAAuB;AAAA,cACzC,KAAK;AACH,qBAAK,KAAK,MAAM,YAAY;AAAA,kBAC1B;AAAA,gBACF,CAAC;AACD,gBAAAA,QAAO,MAAM,uBAAuB,KAAK,UAAU,GAAG;AACtD,oBAAI,CAAC,KAAK,UAAU;AAClB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,KAAK,aAAa,OAAO;AAAA,cAClC,KAAK;AACH,qBAAK,WAAW;AAChB,0BAAU,OAAO;AACjB,0BAAU,OAAO;AACjB,uBAAO,KAAK,aAAa,KAAK;AAAA,kBAC5B;AAAA,kBACA;AAAA,gBACF,CAAC;AAAA,cACH,KAAK;AACH,0BAAU,OAAO;AACjB;AAAA,cACF,KAAK;AACH,0BAAU,OAAO;AACjB,0BAAU,KAAK,UAAU,OAAO,EAAE,EAAE;AACpC,uBAAO,UAAU,OAAO,UAAU,KAAK,WAAW,eAAe,QAAQ,UAAU,EAAE,CAAC,CAAC;AAAA,cACzF,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAAA,MAC/B,CAAC,CAAC;AACF,eAAS,UAAU,KAAK,KAAK;AAC3B,eAAO,WAAW,MAAM,MAAM,SAAS;AAAA,MACzC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,UAAU,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AAC7F,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,6BAAa,KAAK,kBAAkB;AACpC,6BAAa,KAAK,UAAU;AAC5B,oBAAI,CAAC,KAAK,cAAc;AACtB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,KAAK,aAAa,OAAO;AAAA,cAClC,KAAK;AACH,qBAAK,WAAW;AAAA,cAClB,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,SAAS;AAChB,eAAO,QAAQ,MAAM,MAAM,SAAS;AAAA,MACtC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,CAAC,GAAG,CAAC;AAAA,IACH,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,WAAW;AACrC,UAAI,CAAC,aAAa,cAAc,WAAW,cAAc,SAAS;AAChE,eAAO,IAAI,YAAY;AAAA,MACzB;AACA,aAAO,OAAO,gBAAgB;AAAA,IAChC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe;AAC7B,aAAO,OAAO;AACd,MAAAC,QAAS,OAAO;AAAA,IAClB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB;AAC9B,aAAO,QAAQ;AACf,MAAAA,QAAS,QAAQ;AAAA,IACnB;AAAA,EACF,CAAC,CAAC;AACF,SAAO;AACT,EAAE,qBAAAC,OAAY;AACd,IAAI;AACF,MAAI,aAAa,QAAQ,KAAK,GAAG;AAC/B,QAAI,aAAa;AAAA,EACnB,OAAO;AACL,QAAI,cAAc;AAAA,EACpB;AACF,SAAS,OAAO;AAChB;;;AIjnBA,IAAI,kBAAkC,WAAW;AAC/C,WAAS,iBAAiB,MAAM,QAAQ;AACtC,QAAI,QAAQ;AACZ,oBAAgB,MAAM,gBAAgB;AACtC,oBAAgB,MAAM,gBAAgB,WAAW;AAC/C,UAAI,eAAe,sBAAsB,gBAAgB;AACzD,UAAI,cAAc,MAAM,OAAO,QAAQ,YAAY,OAAO,eAAe,YAAY,cAAc,YAAY,YAAY;AAC3H,OAAC,gBAAgB,MAAM,aAAa,QAAQ,kBAAkB,SAAS,UAAU,uBAAuB,cAAc,YAAY,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,KAAK,aAAa,MAAM,WAAW;AAC7O,OAAC,iBAAiB,MAAM,aAAa,QAAQ,mBAAmB,SAAS,UAAU,wBAAwB,eAAe,YAAY,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,KAAK,cAAc,eAAe,eAAe,CAAC,GAAG,MAAM,WAAW,GAAG,CAAC,GAAG;AAAA,QAC3R,WAAW,MAAM;AAAA,MACnB,CAAC,CAAC;AACF,WAAK,iBAAiB,KAAK,iBAAiB,MAAM,WAAW;AAC3D,cAAM,SAAS,SAAS;AAAA,MAC1B;AAAA,IACF,CAAC;AACD,oBAAgB,MAAM,YAAY,SAAS,KAAK;AAC9C,UAAI,SAAS,MAAM,QAAQ;AAC3B,UAAI,cAAc,OAAO;AACzB,WAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,SAAS;AAClF;AACF,UAAI,WAAW,OAAO,MAAM;AAC5B,aAAO,QAAQ;AACf,kBAAY,QAAQ,GAAG;AACvB,UAAI,KAAK;AACP,eAAO,OAAO,MAAM;AAAA,MACtB;AACA,UAAI,aAAa,OAAO,KAAK;AAC7B,UAAI,WAAW,QAAQ,UAAU;AAC/B,eAAO,KAAK,aAAa,UAAU,UAAU;AAAA,MAC/C;AACA,UAAI,YAAY,MAAM,QAAQ,YAAY,WAAW,YAAY;AACjE,aAAO,iBAAiB,SAAS;AACjC,aAAO,KAAK,WAAW,WAAW;AAChC,eAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH,CAAC;AACD,oBAAgB,MAAM,uBAAuB,SAAS,KAAK;AACzD,UAAI,eAAe,MAAM,MAAM;AAC/B,UAAI,iBAAiB,KAAK,iBAAiB,GAAG;AAC5C,cAAM,SAAS,GAAG;AAAA,MACpB;AAAA,IACF,CAAC;AACD,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,MAAM;AAAA,EACb;AACA,eAAa,kBAAkB,CAAC;AAAA,IAC9B,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,UAAI,eAAe,KAAK,OAAO,QAAQ,aAAa,OAAO,cAAc,aAAa,aAAa,eAAe,aAAa,cAAc,aAAa,aAAa;AACvK,UAAI,CAAC;AACH;AACF,UAAI,cAAc;AAChB,cAAM,aAAa,gBAAgB,YAAY;AAAA,MACjD;AACA,UAAI,aAAa;AACf,cAAM,aAAa,eAAe,WAAW;AAAA,MAC/C;AACA,UAAI,MAAM,eAAe;AACvB,cAAM,cAAc,UAAU;AAAA,MAChC;AACA,WAAK,YAAY;AAAA,IACnB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,UAAI,QAAQ,KAAK,MAAM;AACvB,YAAM,iBAAiB,aAAa,KAAK,YAAY;AAAA,IACvD;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU;AACxB,UAAI,cAAc;AAClB,OAAC,eAAe,KAAK,WAAW,QAAQ,iBAAiB,SAAS,UAAU,qBAAqB,aAAa,WAAW,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,oBAAoB,aAAa,KAAK,YAAY;AAC/O,WAAK,UAAU;AAAA,IACjB;AAAA,EACF,CAAC,CAAC;AACF,SAAO;AACT,EAAE;;;AC3EF,IAAI,YAA4B,SAAS,aAAa;AACpD,YAAU,YAAY,WAAW;AACjC,MAAI,SAAS,aAAa,UAAU;AACpC,WAAS,aAAa;AACpB,QAAI;AACJ,oBAAgB,MAAM,UAAU;AAChC,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AACA,YAAQ,OAAO,KAAK,MAAM,QAAQ,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC;AACrD,oBAAgB,uBAAuB,KAAK,GAAG,OAAO,IAAI;AAC1D,oBAAgB,uBAAuB,KAAK,GAAG,mBAAmB,IAAI;AACtE,oBAAgB,uBAAuB,KAAK,GAAG,YAAY,WAAW;AACpE,UAAI;AACJ,cAAQ,YAAY,MAAM,SAAS,QAAQ,cAAc,SAAS,SAAS,UAAU,SAAS;AAAA,IAChG,CAAC;AACD,oBAAgB,uBAAuB,KAAK,GAAG,WAAW,WAAW;AACnE,UAAI;AACJ,UAAI,MAAM,KAAK;AACb,cAAM,IAAI,QAAQ;AAClB,cAAM,MAAM;AAAA,MACd;AACA,OAAC,wBAAwB,MAAM,qBAAqB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,QAAQ;AACtI,YAAM,kBAAkB;AAAA,IAC1B,CAAC;AACD,oBAAgB,uBAAuB,KAAK,GAAG,gBAAgB,SAAS,KAAK,UAAU;AACrF,UAAI,MAAM,KAAK;AACb,YAAI,qBAAqB;AACzB,cAAM,OAAO,OAAO,MAAM;AAC1B,cAAM,IAAI,UAAU,KAAK,QAAQ;AACjC,YAAI,CAAC,aAAa,sBAAsB,MAAM,OAAO,YAAY,QAAQ,wBAAwB,WAAW,wBAAwB,oBAAoB,SAAS,QAAQ,0BAA0B,UAAU,sBAAsB,uBAAuB;AACxP,gBAAM,iBAAiB;AAAA,QACzB;AAAA,MACF;AAAA,IACF,CAAC;AACD,oBAAgB,uBAAuB,KAAK,GAAG,oBAAoB,WAAW;AAC5E,UAAI,SAAS,MAAM,OAAO;AAC1B,UAAI,CAAC;AACH;AACF,YAAM,OAAO,KAAK,WAAW,WAAW;AACtC,cAAM,OAAO,MAAM;AAAA,MACrB,CAAC;AAAA,IACH,CAAC;AACD,oBAAgB,uBAAuB,KAAK,GAAG,uBAAuB,SAAS,MAAM;AACnF,UAAI,KAAK,KAAK;AACd,UAAI,MAAM;AACR,cAAM,IAAI,UAAU,EAAE;AAAA,IAC1B,CAAC;AACD,WAAO;AAAA,EACT;AACA,eAAa,YAAY,CAAC;AAAA,IACxB,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO,KAAK;AAAA,IACd;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,cAAQ,aAAa,KAAK,SAAS,QAAQ,eAAe,SAAS,SAAS,WAAW;AAAA,IACzF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI,cAAc;AAClB,UAAI,aAAa,eAAe,KAAK,YAAY,QAAQ,iBAAiB,SAAS,UAAU,uBAAuB,aAAa,YAAY,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB;AACrN,aAAO,CAAC,CAAC,aAAa,cAAc,WAAW,cAAc;AAAA,IAC/D;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,mBAAmB;AACjC,UAAI,SAAS;AACb,UAAI,SAAS,KAAK,OAAO;AACzB,UAAI,CAAC,OAAO;AACV;AACF,UAAI,KAAK;AACP,aAAK,IAAI,QAAQ;AACnB,WAAK,OAAO,YAAY,KAAK;AAC7B,UAAI,UAAU,OAAO,OAAO,CAAC;AAC7B,UAAI,QAAQ,mBAAmB,QAAQ,QAAQ,mBAAmB,QAAQ;AACxE,gBAAQ,iBAAiB;AAAA,MAC3B;AACA,WAAK,MAAM,IAAI,IAAI,eAAe;AAAA,QAChC,YAAY,KAAK;AAAA,QACjB,QAAQ,OAAO;AAAA,QACf,OAAO,KAAK,OAAO;AAAA,MACrB,GAAG,OAAO,CAAC;AACX,UAAI,CAAC,KAAK,YAAY;AACpB,mBAAW,qBAAqB,KAAK,QAAQ;AAAA,UAC3C,KAAK;AAAA,YACH,KAAK,SAAS,MAAM;AAClB,kBAAI,YAAY;AAChB,sBAAQ,aAAa,OAAO,SAAS,QAAQ,eAAe,SAAS,UAAU,mBAAmB,WAAW,WAAW,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB;AAAA,YAC1L;AAAA,YACA,cAAc;AAAA,UAChB;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,KAAK,YAAY;AACnB,aAAK,kBAAkB,IAAI,gBAAgB,eAAe;AAAA,UACxD,OAAO,KAAK,OAAO;AAAA,QACrB,GAAG,OAAO,GAAG,GAAG,IAAI;AACpB,aAAK,OAAO,sBAAsB,WAAW;AAC3C,cAAI;AACJ,mBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,iBAAK,KAAK,IAAI,UAAU,KAAK;AAAA,UAC/B;AACA,kBAAQ,wBAAwB,OAAO,qBAAqB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,oBAAoB,MAAM,uBAAuB,IAAI;AAAA,QAC7L;AAAA,MACF;AACA,UAAI,OAAO,QAAQ;AACjB,YAAI;AACJ,SAAC,gBAAgB,KAAK,YAAY,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,UAAU,WAAW;AACxH,cAAI;AACJ,kBAAQ,cAAc,OAAO,SAAS,QAAQ,gBAAgB,SAAS,SAAS,YAAY,OAAO;AAAA,QACrG,CAAC;AAAA,MACH;AACA,WAAK,GAAG,eAAO,YAAY,KAAK,YAAY;AAC5C,WAAK,GAAG,eAAO,SAAS,KAAK,OAAO;AACpC,WAAK,YAAY;AACjB,WAAK,gBAAgB,MAAM,IAAI;AAC/B,WAAK,gBAAgB,MAAM,UAAU;AACrC,WAAK,gBAAgB,MAAM,qBAAqB;AAChD,WAAK,gBAAgB,MAAM,aAAa;AACxC,WAAK,gBAAgB,MAAM,UAAU;AACrC,WAAK,gBAAgB,MAAM,oBAAoB;AAC/C,WAAK,gBAAgB,MAAM,yBAAyB;AACpD,WAAK,gBAAgB,MAAM,aAAa;AACxC,WAAK,gBAAgB,MAAM,SAAS;AACpC,WAAK,gBAAgB,MAAM,QAAQ;AACnC,WAAK,gBAAgB,MAAM,eAAe;AAC1C,WAAK,gBAAgB,MAAM,GAAG;AAC9B,WAAK,gBAAgB,MAAM,WAAW;AACtC,WAAK,gBAAgB,MAAM,eAAe;AAC1C,WAAK,gBAAgB,MAAM,gBAAgB;AAC3C,WAAK,gBAAgB,MAAM,kBAAkB;AAC7C,WAAK,gBAAgB,MAAM,iBAAiB;AAC5C,WAAK,IAAI,KAAK,OAAO,KAAK,IAAI;AAAA,IAChC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,UAAI,SAAS;AACb,WAAK,IAAI,GAAG,MAAM,OAAO,SAAS,KAAK;AACrC,YAAI,OAAO,QAAQ;AACjB,iBAAO,OAAO,KAAK,eAAO,OAAO,IAAI,OAAO,OAAO,QAAQ,GAAG,CAAC;AAAA,QACjE;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB,WAAW;AACzC,UAAI,SAAS;AACb,WAAK,IAAI,GAAG,WAAW,SAAS,GAAG;AACjC,YAAI,OAAO,QAAQ;AACjB,iBAAO,OAAO,KAAK,cAAc,eAAe,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG;AAAA,YACzE;AAAA,UACF,CAAC,CAAC;AAAA,QACJ;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC,GAAG,CAAC;AAAA,IACH,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,WAAW,OAAO;AAC5C,aAAO,IAAI,YAAY,WAAW,KAAK;AAAA,IACzC;AAAA,EACF,CAAC,CAAC;AACF,SAAO;AACT,EAAE,UAAU;AACZ,gBAAgB,WAAW,OAAO,GAAG;", "names": ["logger", "<PERSON><PERSON>", "EventEmitter"]}