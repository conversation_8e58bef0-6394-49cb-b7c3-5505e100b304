! function (e, t) {
    "object" == typeof exports && "undefined" != typeof module ? t(exports) : "function" == typeof define && define.amd ? define(["exports"], t) : t((e = "undefined" != typeof globalThis ? globalThis : e || self).gh = {})
}(this, function (exports) {
    "use strict";
var showobj = Object.freeze({
    'animation': false,
    'baseLayerPicker': false,
    'fullscreenButtion': false,
    'vrBotton': false,
    'geocoder': false,
    'homeButton': false,
    'infoBox': false,
    'sceneModePicker': false,
    'selectionIndicator': false,
    'timeline': false,
    'navigationHelpButton': false,
    'navigationInstructionsInitiallyVisible': false,
    'scene3DOnly': false,
    'useDefaultRenderLoop': true,
    'showRenderLoopErrors': false,
    'useBrowserRecommendedResolution': true,
    'automaticallyTrackDataSourceClocks': true,
    'orderIndependentTranslucency': true,
    'projectionPicker': false
});
let subdomains = ['0', '1', '2', '3', '4', '5', '6', '7'],
    terrbox = Object.freeze({
        'shouldAnimate': true,
        'skyBox': undefined,
        'skyAtmosphere': undefined,
        'shadows': false,
        'terrainShadows': Cesium.ShadowMode.ENABLED,
        'sceneMode': Cesium.SceneMode.SCENE3D,
        'imageryProvider': new Cesium.UrlTemplateImageryProvider({
            'url': 'https://t{s}.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=9e624bfed46a72f99909f98dbef14c99',
            'subdomains': subdomains,
            'tilingScheme': new Cesium.WebMercatorTilingScheme(),
            'maximumLevel': 18
        })
    }),
    sun = undefined,
    moon = undefined,
    TileCoordinatesImageryLayer = undefined,
    gridImageryLayer = undefined,
    setting = Object.freeze({
        'MAP_3D': Symbol(),
        'MAP_2D': Symbol(),
        'LIGHTING_ENABLE': Symbol(),
        'LIGHTING_DISABLE': Symbol(),
        'GLOBE_ENABLE': Symbol(),
        'GLOBE_DISABLE': Symbol(),
        'SHADOWS_ENABLE': Symbol(),
        'SHADOWS_DISABLE': Symbol(),
        'FOG_ENABLE': Symbol(),
        'FOG_DISABLE': Symbol(),
        'ATMOSPHERE_ENABLE': Symbol(),
        'ATMOSPHERE_DISABLE': Symbol(),
        'GALAXY_ENABLE': Symbol(),
        'GALAXY_DISABLE': Symbol(),
        'GLOBE_TRANSLUCENCY_ENABLE': Symbol(),
        'GLOBE_TRANSLUCENCY_DISABLE': Symbol(),
        'ANIMATE_ENABLE': Symbol(),
        'ANIMATE_DISABLE': Symbol(),
        'TILE_COOR_ENABLE': Symbol(),
        'TILE_COOR_DISABLE': Symbol(),
        'GRID_ENABLE': Symbol(),
        'GRID_DISABLE': Symbol(),
        'T_SHADOWS_ENABLE': 999,
        'T_SHADOWS_DISABLE': 999,
        'UNDERGROUND_ENABLE': Symbol(),
        'UNDERGROUND_DISABLE': Symbol()
    });

function fogHandler(value) {
    let density = value ? value : 0.0002;
    exports._scene.fog.enabled = true;
    if (Object.prototype.toString.call(density) === '[object Number]') {
        exports._scene.fog.density = density;
        return density
    }
    console.log('error: density must be number')
}

function translucencyHandler(translucency) {
    translucency = translucency ? translucency : 0.5;
    exports._globe.translucency.enabled = true;
    if (Object.prototype.toString.call(translucency) === '[object Number]') {
        exports._globe.translucency.frontFaceAlpha = translucency;
        exports._globe.translucency.backFaceAlpha = translucency;
        return translucency
    } else {
        console.log('error: translucency number between 0.0-1.0')
    }

}

function tile_coor_enableHandler() {
    let tileCoordinatesImageryProvider = {
        'color': Cesium.Color.fromCssColorString('rgba(0, 0, 0, 1)')
    };
    if (!TileCoordinatesImageryLayer) {
        TileCoordinatesImageryLayer = exports._viewer.imageryLayers.addImageryProvider(new Cesium.TileCoordinatesImageryProvider(tileCoordinatesImageryProvider))
    } else {
        console.log('tile coordinate already exists')
    }

}

function tile_coor_disableHandler() {
    if (TileCoordinatesImageryLayer) {
        exports._viewer.imageryLayers.remove(TileCoordinatesImageryLayer, true)
        TileCoordinatesImageryLayer = undefined
    } else {
        console.log('error:no xyz coordinate')
    }
}

function grid_enableHandler() {
    let gridImageryProvider = {
        'color': Cesium.Color.fromCssColorString('rgba(0, 0, 0, 1)'),
        'glowColor': Cesium.Color.fromCssColorString('rgba(0, 0, 0, 1)'),
        'glowWidth': 6,
        'backgroundColor': Cesium.Color.fromCssColorString('rgba(0, 0, 0, 0)')
    };
    if (!gridImageryLayer) {
        gridImageryLayer = exports._viewer.imageryLayers.addImageryProvider(new Cesium.GridImageryProvider(gridImageryProvider))
    } else {
        console.log('grid coordinate already exists')
    }

}

function grid_disableHandler() {
    if (gridImageryLayer) {
        exports._viewer.imageryLayers.remove(gridImageryLayer, true);
        gridImageryLayer = undefined
    } else {
        console.log('error: no grid coordinate')
    }
}

function setHandler(prop) {
    propvalueobj = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    switch (prop) {
        case setting.MAP_3D:
            exports._scene.mode = Cesium.SceneMode.SCENE3D;
            return true;
        case setting.MAP_2D:
            exports._scene.mode = Cesium['SceneMode']['SCENE2D'];
            return true;
        case setting.LIGHTING_ENABLE:
            exports._globe.enableLighting = true;
            exports._globe.dynamicAtmosphereLighting = true;
            return true;
        case setting.LIGHTING_DISABLE:
            exports._globe.enableLighting = false;
            return true;
        case setting.GLOBE_ENABLE:
            exports._globe.show = true;
            return true;
        case setting.GLOBE_DISABLE:
            exports._globe.show = false;
            return true;
        case setting.SHADOWS_ENABLE:
            exports._globe.shadows = Cesium.ShadowMode.ENABLED;
            return true;
        case setting.SHADOWS_DISABLE:
            exports._globe.shadows = Cesium.ShadowMode.DISABLED;
            return true;
        case setting.FOG_ENABLE:
            return fogHandler(propvalueobj.density);
        case setting.FOG_DISABLE:
            exports._scene.fog.enabled = false;
            return true;
        case setting.ATMOSPHERE_ENABLE:
            exports._scene.skyAtmosphere.show = true;
            exports._globe.showGroundAtmosphere = true;
            return true;
        case setting.ATMOSPHERE_DISABLE:
            exports._scene.skyAtmosphere.show = false;
            exports._globe.showGroundAtmosphere = false;
            return true;
        case setting.GALAXY_ENABLE:
            exports._scene.sun = sun = new Cesium.Sun();
            exports._scene.moon = moon = new Cesium.Moon();
            return true;
        case setting.GRID_DISABLE:
            exports._scene.sun = sun && sun.destroy();
            exports._scene.moon = moon && moon.destroy();
            return true;
        case setting.GLOBE_TRANSLUCENCY_ENABLE:
            return translucencyHandler(propvalueobj.translucency);
        case setting.GLOBE_TRANSLUCENCY_DISABLE:
            exports._globe.translucency.enabled = false;
            return true;
        case setting.ANIMATE_ENABLE:
            exports._viewer.clock.shouldAnimate = true;
            return true;
        case setting.ANIMATE_DISABLE:
            exports._viewer.clock.shouldAnimate = false;
            return true;
        case setting.TILE_COOR_ENABLE:
            return tile_coor_enableHandler();
        case setting.TILE_COOR_DISABLE:
            return tile_coor_disableHandler();
        case setting.GRID_ENABLE:
            return grid_enableHandler();
        case setting.GRID_DISABLE:
            return grid_disableHandler();
        case setting.UNDERGROUND_ENABLE:
            exports._scene.screenSpaceCameraController.enableCollisionDetection = true;
            return true;
        case setting.UNDERGROUND_DISABLE:
            exports._scene.screenSpaceCameraController.enableCollisionDetection = false;
            return true;
        default:
            console.log('error: wrong setting');
            return false;
    }
}
var n, unit8 = new Uint8Array(16);

function getRandomValuesEnable() {
    if (!n) {
        n = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto) || typeof msCrypto !== 'undefined' && typeof msCrypto.getRandomValues === 'function' && msCrypto.getRandomValues.bind(msCrypto);
        if (!n) throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');
    }
    return n(unit8);
}
var reg = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;

function testuuid(random) {
    return typeof random === 'string' && reg.test(random);
}
var number216 = [];
for (let index = 0; index < 256; ++index) {
    number216.push((index + 256).toString(16).substr(1));
}

function stringifiedUUId(randomnum) {
    var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0,
        random = (number216[randomnum[offset + 0]] + number216[randomnum[offset + 1]] + number216[randomnum[offset + 2]] + number216[randomnum[offset + 3]] + '-' + number216[randomnum[offset + 4]] + number216[randomnum[offset + 5]] + '-' + number216[randomnum[offset + 6]] + number216[randomnum[offset + 7]] + '-' + number216[randomnum[offset + 8]] + number216[randomnum[offset + 9]] + '-' + number216[randomnum[offset + 10]] + number216[randomnum[offset + 11]] + number216[randomnum[offset + 12]] + number216[randomnum[offset + 13]] + number216[randomnum[offset + 14]] + number216[randomnum[offset + 15]]).toLowerCase();
    if (!testuuid(random)) throw TypeError('Stringified UUID is invalid');
    return random;
}

function getRandom() {
    var randomvalue = getRandomValuesEnable();
    randomvalue[6] = randomvalue[6] & 15 | 64;
    randomvalue[8] = randomvalue[8] & 63 | 128;

    return stringifiedUUId(randomvalue);
}

function isNumber(_0x3cba7e, _0x29c950, _0x272abb) {
    var _0x5abc17 = _0xe0e88f;
    if (Object.prototype.toString['call'](_0x3cba7e) !== '[object Number]' || Object.prototype.toString[_0x5abc17(0x34b)](_0x29c950) !== _0x5abc17(0x2e3) || Object['prototype']['toString'][_0x5abc17(0x34b)](_0x272abb) !== _0x5abc17(0x2e3)) return console[_0x5abc17(0x20a)](_0x5abc17(0x2a4)), false;
    else return _0x3cba7e < -0xb4 || _0x3cba7e > 0xb4 || _0x29c950 < -0x5a || _0x29c950 > 0x5a ? (console['log']('error:  -180.0  < lon  < 180.0 and  -90.0  < lat  < 90.0'), false) : true;
}

function checkOptions(prop) {
    if (Object.prototype.toString.call(prop) !== '[object Object]') {
        console.log('error: options must be object')
        return false
    } else {
        return true
    }

}



function getDatasourceid() {
    return getRandom();
}

function loadpoijson(url) {
    return new Promise(function (resolve, reject) {
      let  httprequest = new XMLHttpRequest();
        httprequest.open('get', url);
        httprequest.send(null);
        httprequest.onload = function () {
            if (httprequest.status === 200) {
                try {
                    let res = JSON.parse(httprequest.responseText);
                    resolve(res);
                } catch (err) {
                    console.log('error: file not json')
                    reject(false)
                }
            } else {
                console.log('error: network connect error')
                reject(false);
            }
        };
    });
}
var o_fires = {},
    imagery_feature = {},
    maplayer = Object.freeze({
        'RASTER': Symbol('RASTER'),
        'VECTOR': Symbol('VECTOR'),
        'POI3D': Symbol('POI3D'),
        'LABEL3D': Symbol('LABEL3D'),
        'OBJ3D': Symbol('OBJ3D'),
        'TILES3D': Symbol('TILES3D')
    }),
    layerfordatasourceId = {},
    datasourceForLayers = {};

function addPrimitive(layname) {
    datasourceForLayers[layname] = new Cesium.PrimitiveCollection();
    exports._scene.primitives.add(datasourceForLayers[layname]);
}

function raster(layername) {
    datasourceForLayers[layername] = [];
}

function vector(layername) {
    datasourceForLayers[layername] = [];
}

function poi3d(layername) {
    datasourceForLayers[layername] = [];
}

function createLayer(options) {
   let layername = options.name,
        layertype = options.type;
    if (!layername || !layertype) {
        console.log('error: no name or type given')
        return
    }
    if (datasourceForLayers.hasOwnProperty(layername) || this.layers.hasOwnProperty(layername)) {
        console.log('error: layer name already exists')
        return;
    }
    switch (layertype) {
        case maplayer.OBJ3D:
            addPrimitive(layername);
            break;
        case maplayer.TILES3D:
            addPrimitive(layername);
            break;
        case maplayer.RASTER:
            raster(layername);
            break;
        case maplayer.VECTOR:
            vector(layername);
            break;
        case maplayer.POI3D:
            poi3d(layername);
            break;
        default:
            console.log('error: layer type wrong')
            return false;
    }
    this.layers[layername] = {
        'type': layertype.description,
        'features': []
    }
    return layername;
}

function hideLayer(layername) {
    if (!this.layers[layername]) {
        console.log('error: no layer name')
        return;
    }
    if (Object.prototype.toString.call(datasourceForLayers[layername]) === '[object Array]') {
        for (let index = 0; index < datasourceForLayers[layername]['length']; index++) {
            datasourceForLayers[layername][index].show = false;
        }
    } else datasourceForLayers[layername].show = false;
    return true;
}

function showLayer(layername) {
    if (!this.layers[layername]) {
        console.log('error: no layer name')
        return;
    }
    if (Object.prototype.toString.call(datasourceForLayers[layername]) === '[object Array]') {
        for (let index = 0; index < datasourceForLayers[layername]['length']; index++) {
            datasourceForLayers[layername][index].show = true;
        }
    } else datasourceForLayers[layername].show = true;
    return true;
}

function layerhandler(that) {
    this.create = createLayer.bind(that);
    this.delete = undefined;
    this.show = showLayer.bind(that);
    this.hidden = hideLayer.bind(that);
    this.clear = undefined;
}
var t_map = Object.freeze({
        'T_SATELLITE': Symbol('SATELLITE'),
        'T_STREET': Symbol('STREET'),
        'T_LABLE_NAME': Symbol('LABLE_NAME'),
        'T_LABLE_NATURE': Symbol('LABLE_NATURE'),
        'T_SHADING': Symbol('SHADING'),
        'T_BOUNDARY': Symbol('BOUNDARY')
    }),
    feature = Object.freeze({
        'POINT': 'POINT',
        'LINE': 'LINE',
        'POLYGON': 'POLYGON'
    });

function terrainLoader() {
    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    if (!checkOptions(options)) return false;
    var terrain = options.terrain || 'default',
        waterMask = options.waterMask || false,
        vertexNormals = options.vertexNormals || false;
    if (terrain == 'default') return exports._viewer.terrainProvider = Cesium.createWorldTerrain({
        'requestWaterMask': waterMask,
        'requestVertexNormals': vertexNormals
    }), true;
    return false;
}

function tiandituHandler(options) {
    if (!checkOptions(options)) return false;
    var layer = options.layer || 'tianditu',
        maptype = options.type || t_map.T_SATELLITE,
        key = options.key || '9e624bfed46a72f99909f98dbef14c99';
    if (!maptype || !key || !layer) {
        console.log('error: no name map type or key')
        return;
    }
    if (datasourceForLayers.hasOwnProperty(layer) || this.layers.hasOwnProperty(layer)) {
        console.log('error: layer name already exists')
        return;
    }
    let prefix = undefined;
    switch (maptype) {
        case t_map.T_SATELLITE:
            prefix = 'img_w';
            break;
        case t_map.T_STREET:
            prefix = 'vec_w';
            break;
        case t_map.T_LABLE_NAME:
            prefix = 'cva_w';
            break;
        case t_map.T_LABLE_NATURE:
            prefix = 'cta_w';
            break;
        case t_map.T_SHADING:
            prefix = 'ter_w';
            break;
        case t_map.T_BOUNDARY:
            prefix = 'ibo_w';
            break;
        default:
            console.log('error:map type wrong');
            return false;
    }
    let urlprefix = 'https://t{s}.tianditu.gov.cn/',
        subdomains = ['0', '1', '2', '3', '4', '5', '6', '7'],
        randomsourceId = getDatasourceid(),
        imageryLayerLength = exports._scene.imageryLayers.length,
        imgMap = new Cesium.UrlTemplateImageryProvider({
            'url': urlprefix + 'DataServer?T=' + prefix + '&x={x}&y={y}&l={z}&tk=' + key,
            'subdomains': subdomains,
            'tilingScheme': new Cesium.WebMercatorTilingScheme(),
            'maximumLevel': 18
        });
    exports._scene.imageryLayers.addImageryProvider(imgMap);
    datasourceForLayers[layer] = exports._scene.imageryLayers;
    datasourceForLayers[randomsourceId] = exports._scene.imageryLayers.get(imageryLayerLength);
    layerfordatasourceId[randomsourceId] = layer;
    this.layers[layer] = {
        'type': maptype.description,
        'features': [randomsourceId]
    };
    return randomsourceId;
}

function tiles3dHandler(options) {
    if (!checkOptions(options)) return false;
    var url = options.url,
        layername = options.layer;
    if (!url || !layername) {
        console.log('error: no layer name or url')
        return
    }
    if (!datasourceForLayers.hasOwnProperty(layername) || !this.layers.hasOwnProperty(layername)) {
        console.log('error: no given layer')
        return;
    }
    var height = options.height || 20,
        lon = options.lon,
        lat = options.lat,
        x = options.x,
        y = options.y,
        z = options.z,
        scale = options.scale,
        classify = options.classify,
        randomsourceId = getDatasourceid();
    datasourceForLayers[randomsourceId] = undefined
    if (classify) {
        datasourceForLayers[randomsourceId] = new Cesium.Cesium3DTileset({
            'url': url,
            'classificationType': Cesium.ClassificationType.CESIUM_3D_TILE
        });
        datasourceForLayers[randomsourceId].style = new Cesium.Cesium3DTileStyle({
            'color': 'rgba(255, 255, 255, 0.01)'
        })
    } else {
        datasourceForLayers[randomsourceId] = new Cesium.Cesium3DTileset({
            'url': url,
            'show': true,
            'shadows': Cesium.ShadowMode.ENABLED,
            'maximumMemoryUsage': 128,
            'dynamicScreenSpaceError': true,
            'dynamicScreenSpaceErrorDensity': 0.00278,
            'dynamicScreenSpaceErrorFactor': 4,
            'dynamicScreenSpaceErrorHeightFalloff': 0.25
        });
    }
    datasourceForLayers[layername].add(datasourceForLayers[randomsourceId]);
    layerfordatasourceId[randomsourceId] = layername;
    this.layers[layername].features.push(randomsourceId);
    datasourceForLayers[randomsourceId].readyPromise.then(function (tileset) {
        center = tileset.boundingSphere,
            cartographic = Cesium.Cartographic.fromCartesian(center); //x.y。z笛卡尔转化为经纬度
        surface = Cesium.Cartesian3.fromRadians(cartographic.longitude, cartographic.latitude, 0),
            offset = Cesium.Cartesian3.fromRadians(cartographic.longitude, cartographic.latitude, height),
            translation = Cesium.Cartesian3.subtract(offset, surface, new Cesium.Cartesian3());
        tileset.modelMatrix = Cesium.Matrix4.fromTranslation(translation); //tileset 自身的变形max
        var Matrix4arr = new Array(16).fill(1),
            rotateX_matrix = Cesium.Matrix4.fromArray(Matrix4arr);
        if (lon && lat && isNumber(lon, lat, height)) {
            var enuposition = Cesium.Cartesian3.fromDegrees(lon, lat, height); //传过来的经纬度转化为下全局坐标系x,y，z笛卡尔坐标
            rotateX_matrix = Cesium.Transforms.eastNorthUpToFixedFrame(enuposition); //建立从局部到世界的坐标矩阵
        }
        if (scale && scale !== 1) {
            Cesium.Matrix4.multiply(rotateX_matrix, Cesium.Matrix4.multiplyByUniformScale(scale), rotateX_matrix)
        }
        if (x && x !== 0) {
            //表示绕x周旋转
            var mx = Cesium.Matrix3.fromRotationX(Cesium.Math.toRadians(x)); //绕x轴旋转
            var rotationX = Cesium.Matrix4.fromRotationTranslation(mx);
            Cesium.Matrix4.multiply(rotateX_matrix, rotationX, rotateX_matrix);

        }
        if (y && y !== 0) {
            var my = Cesium.Matrix3.fromRotationY(Cesium.Math.toRadians(y)); //绕x轴旋转
            var rotationY = Cesium.Matrix4.fromRotationTranslation(my);
            Cesium.Matrix4.multiply(rotateX_matrix, rotationY, rotateX_matrix);


        }
        if (z && z != 0) {

            var mz = Cesium.Matrix3.fromRotationZ(Cesium.Math.toRadians(z)); //绕x轴旋转
            var rotationZ = Cesium.Matrix4.fromRotationTranslation(mz);
            Cesium.Matrix4.multiply(rotateX_matrix, rotationZ, rotateX_matrix);

        }
        if (!rotateX_matrix.equals(Cesium.Matrix4.fromArray(Matrix4arr))) {
            tileset.root.transform = rotateX_matrix
        }


    })

    return randomsourceId;
}

function gltfHandler(options) {
    if (!checkOptions(options)) return false;
    var url = options.url;
    console.log(url);
    var model = exports._scene.primitives.add(Cesium.Model.fromGltf({
        'url': url,
        'show': true,
        'scale': 1,
        'modelMatrix': Cesium.Transforms.eastNorthUpToFixedFrame(Cesium.Cartesian3.fromDegrees(110.62898254394531, 40.02804946899414, 6))
    }));

    exports._viewer.zoomTo(model);
    return model;
}

function geojsonHandler(options) {
    if (!checkOptions(options)) return false;
    var url = options.url,
        layername = options.layer;
    if (!url || !layername) {
        console.log('error:no layer name or url')
        return
    }
    if (!datasourceForLayers.hasOwnProperty(layername) || !this.layers.hasOwnProperty(layername)) {
        console.log('error: no given layer')
        return;
    }
    var featuretype = options.type;
    if (!featuretype) {
        console.log('error:no feature type given')
        return
    }
    var clampToGround = options.clampToGround || false,
        stroke = options.stroke || 'rgba(255, 234, 92, 0.8)',
        strokeWidth = options['strokeWidth'] || 5,
        glowPower = options['glowPower'] || 0,
        railways = options.railways || 0,
        fill = options['fill'] || false,
        outlineWidth = options['outlineWidth'] || 0,
        outlineColor = options.outlineColor || 'rgba(255, 234, 0, 0.8)',
        extrudedHeight = options.extrudedHeight || 0,
        height = options.height || 0,
        label = options.label || false,
        labelOffset = options.labelOffset || [0, 0],
        labelColor = options.labelColor || 'rgba(255, 255, 255, 1)',
        labelSize = options.labelSize || 0.5,
        labelBackgroundColor = options.labelBackgroundColor,
        showBackground = true;
    if (labelBackgroundColor == undefined) {
        labelBackgroundColor = 'rgba(0, 0, 0, 0.5)';
        showBackground = false
    }
    var labelNear = options.labelNear || 0,
        labelNearScale = options.labelNearScale || 1,
        labelFar = options.labelFar || 0x9c40,
        labelFarScale = options.labelFarScale || 0.75,
        randomsourceId = getDatasourceid(),
        promise = Cesium.GeoJsonDataSource.load(url, {
            'clampToGround': clampToGround
        });
    promise.then(function (dataSource) {
        let entities = dataSource.entities.values;
        if (featuretype === feature.LINE) {
            for (let index = 0; index < entities.length; index++) {
                var entity = entities[index];
                entity.polyline.width = strokeWidth;
                if (railways) {
                    entity.polyline.material = new Cesium.StripeMaterialProperty({
                        'orientation': Cesium.StripeOrientation.VERTICAL,
                        'repeat': railways
                    });
                } else {
                    if (glowPower) {
                        entity.polyline.material = new Cesium.PolylineGlowMaterialProperty({
                            'color': Cesium.Color['fromCssColorString'](stroke),
                            'glowPower': glowPower,
                            'taperPower': 1
                        })
                    } else {
                        entity.polyline.material = new Cesium.PolylineOutlineMaterialProperty({
                            'color': Cesium.Color.fromCssColorString(stroke),
                            'outlineColor': Cesium.Color.fromCssColorString(outlineColor)
                        });
                    }
                }
            }
        }
        if (featuretype === feature.POLYGON)
            for (let index = 0; index < entities.length; index++) {
                let entity = entities[index];
                if (label) {
                    // 得到每块多边形的坐标集合
                    var polyPositions = entity['polygon'].hierarchy.getValue(Cesium.JulianDate.now()).positions;
                    // 根据坐标集合构造BoundingSphere获取中心点坐标
                    var polyCenter = Cesium.BoundingSphere.fromPoints(polyPositions).center;
                    // 将中心点拉回到地球表面
                    polyCenter = Cesium.Ellipsoid.WGS84.scaleToGeodeticSurface(polyCenter);
                    entity.position = polyCenter;
                    entity.label = {
                        'show': true,
                        'text': entity[label],
                        'fillColor': Cesium.Color['fromCssColorString'](labelColor),
                        'font': 'normal 32px MicroSoft YaHei',
                        'scale': labelSize,
                        'horizontalOrigin': Cesium.HorizontalOrigin.LEFT,
                        'verticalOrigin': Cesium.VerticalOrigin.BOTTOM,
                        'showBackground': showBackground,
                        'backgroundColor': Cesium.Color.fromCssColorString(labelBackgroundColor),
                        'backgroundPadding': new Cesium.Cartesian2(20, 20),
                        'pixelOffset': new Cesium.Cartesian2(labelOffset[0], labelOffset[1]),
                        'distanceDisplayCondition': new Cesium.DistanceDisplayCondition(labelNear, labelFar),
                        'scaleByDistance': new Cesium['NearFarScalar'](labelNear, labelNearScale, labelFar, labelFarScale)
                    };
                }
                entity.polygon.extrudedHeight = extrudedHeight;
                entity.polygon.height = height;
                if (fill) {
                    entity.polygon.fill = true;
                    entity.polygon.material = Cesium.Color.fromCssColorString(fill)
                } else {
                    entity['polygon']['fill'] = false
                }
                if (outlineWidth) {
                    entity.polygon.outline = true;
                    entity.polygon.outlineWidth = outlineWidth;
                    entity.polygon.outlineColor = Cesium.Color['fromCssColorString'](outlineColor)
                }
            }
        datasourceForLayers[layername].push(dataSource);
        exports._viewer.dataSources.add(dataSource);
        datasourceForLayers[randomsourceId] = dataSource;
        layerfordatasourceId[randomsourceId] = layername;
    });
    this.layers[layername]['features'].push(randomsourceId);
    return randomsourceId;
}

function poiFromGeojsonHandler(options) {
    let that = this;
    if (!checkOptions(options)) return false;
    var url = options.url,
        layername = options.layer;
    if (!url || !layername) {
        console.log('error: no layer name or url')
        return
    }
    console.log(options.icon)
    if (!datasourceForLayers.hasOwnProperty(layername) || !this.layers.hasOwnProperty(layername)) {
        console.log('error: no given layer')
        return;
    }
    var id = options.id,
        name = options.name,
        height = options.height || 0,
        icon = options.icon,
        near = options.near || 0,
        nearScale = options.nearScale || 1,
        far = options['far'] || 1000000,
        farScale = options['farScale'] || 0.2,
        labelHeight = options.labelHeight || -100;

    var labelColor = options['labelColor'] || 'rgba(162,172,255,1)',
        textColor = options.textColor || 'rgba(162,172,255,1)',
        labelSize = options.labelSize || 1.4,
        labelBackgroundColor = options.labelBackgroundColor || 'rgba(13,22,51,0.8)';

    var datasourceid = getDatasourceid();
    datasourceForLayers[datasourceid] = new Cesium.CustomDataSource(datasourceid);
    loadpoijson(url).then(function (res) {
        let features = res.features;
        for (let index = 0; index < features['length']; index++) {
            var feature = features[index];
            if (feature.type !== 'Feature') continue;
            var geometry = feature.geometry;
            if (geometry.type !== 'Point') continue;
            var coordinates = geometry.coordinates,
                entityname = '@' + layername + '/' + feature.properties[id];
            datasourceForLayers[entityname] = datasourceForLayers[datasourceid].entities.add({
                'id': feature.properties[id],
                'lon': coordinates[0],
                'lat': coordinates[1],
                'name': feature.properties[name],
                'type': feature.properties.type,
                'info': feature.properties.info,
                'position': Cesium['Cartesian3']['fromDegrees'](coordinates[0], coordinates[1], height),
                'billboard': {
                    'image': icon,
                    'scaleByDistance': new Cesium.NearFarScalar(near, nearScale, far, farScale),
                    'pixelOffsetScaleByDistance': new Cesium.NearFarScalar(near, 0, far, 0),
                    'disableDepthTestDistance': Number.POSITIVE_INFINITY,
                    'distanceDisplayCondition': new Cesium.DistanceDisplayCondition(near, far)
                },
                'label': {
                    'text': feature.properties[name],
                    'font': '14px SectionHeader',
                    'style': Cesium.LabelStyle.FILL,
                    'scale': labelSize,
                    'textColor': Cesium.Color.fromCssColorString(textColor),
                    'fillColor': Cesium.Color['fromCssColorString'](labelColor),
                    'showBackground': true,
                    'backgroundColor': Cesium.Color.fromCssColorString(labelBackgroundColor),
                    'backgroundPadding': new Cesium.Cartesian2(7, 5),
                    'verticalOrigin': Cesium.VerticalOrigin.TOP,
                    'horizontalOrigin': Cesium.HorizontalOrigin['CENTER'],
                    'scaleByDistance': new Cesium.NearFarScalar(1000, 1, 200, 1.5),
                    'pixelOffset': new Cesium.Cartesian2(0, labelHeight),
                    'distanceDisplayCondition': new Cesium.DistanceDisplayCondition(near, far)
                }
            });
            that.layers[layername].features.push(feature.properties[entityname]);
        }
        exports._viewer.dataSources.add(datasourceForLayers[datasourceid]);
        datasourceForLayers[layername].push(datasourceForLayers[datasourceid]);
        layerfordatasourceId[datasourceid] = layername;
    })
    return datasourceid;
}

function imageHandler(options) {
    if (!checkOptions(options)) return false;
    var url = options.url,
        layername = options.layer;
    if (!url) {
        console.log('error: no url')
        return;
    }
    if (!datasourceForLayers['hasOwnProperty'](layername) || !this['layers'].hasOwnProperty(layername)) {
        console.log('error: no given layer')
        return;
    }
    var west = options.west,
        south = options.south,
        east = options.east,
        north = options.north;
    if (!west || !south || !east || !north) {
        console.log('error: no rectangle')
        return
    }
    if (isNumber(west, south) && isNumber(east, north)) {
        if (west < east && south < north) {
            var randomsourceId = getDatasourceid(),
                length = datasourceForLayers[layername].length;
            datasourceForLayers[layername].addImageryProvider(new Cesium['SingleTileImageryProvider']({
                'url': url,
                'rectangle': Cesium.Rectangle.fromDegrees(west, south, east, north)
            }));
            datasourceForLayers[randomsourceId] = datasourceForLayers[layername].get(length);
            layerfordatasourceId[randomsourceId] = layername;
            this.layers[layername]['features'].push(randomsourceId);
            return randomsourceId;
        } else {
            console.log('error: values west < east and sourth < north')
            return;
        }
    } else return false;
}

function tmsHandler(options) {
    if (!checkOptions(options)) return false;
    var url = options.url,
        layername = options['layer'];
    if (!url) {
        console.log('error: no url')
        return;
    }
    if (!datasourceForLayers.hasOwnProperty(layername) || !this.layers.hasOwnProperty(layername)) {
        console.log('error: no given layer')
        return;
    }
    var randomsourceId = getDatasourceid(),
        imageryLayersLength = exports._scene.imageryLayers.length;
    exports._scene.imageryLayers.addImageryProvider(new Cesium.TileMapServiceImageryProvider({
        'url': url
    }));
    datasourceForLayers[layername].push(exports._scene.imageryLayers.get(imageryLayersLength));
    datasourceForLayers[randomsourceId] = exports._scene.imageryLayers.get(imageryLayersLength);
    layerfordatasourceId[randomsourceId] = layername;
    this.layers[layername]['features']['push'](randomsourceId);
    return randomsourceId;
}
var unloadHandler = function unloaddatasource(datasourceId) {
    if (datasourceForLayers[datasourceId]) {
        if (layerfordatasourceId[datasourceId]) {
            if (datasourceForLayers[datasourceId] instanceof Cesium.GeoJsonDataSource || datasourceForLayers[datasourceId] instanceof Cesium.CustomDataSource) {
                datasourceForLayers[datasourceId].entities.removeAll()
            } else {
                datasourceForLayers[layerfordatasourceId[datasourceId]].remove(datasourceForLayers[datasourceId])
            }
            this.layers[layerfordatasourceId[datasourceId]].features = this.layers[layerfordatasourceId[datasourceId]].features.filter(function (item) {
                return item != datasourceId;
            });
            delete datasourceForLayers[datasourceId];
            delete layerfordatasourceId[datasourceId]
        } else {
            delete layerfordatasourceId[datasourceId];
            console.log('warn: no this data')
        }
    } else {
        console.log('error: wrong id')
    }
};

function loaderhandler(that) {
    this.terrain = terrainLoader.bind(that);
    this.tianditu = tiandituHandler.bind(that);
    this.tiles3d = tiles3dHandler.bind(that);
    this.geojson = geojsonHandler.bind(that),
        this.poiFromGeojson = poiFromGeojsonHandler.bind(that);
    this.image = imageHandler.bind(that);
    this.tms = tmsHandler.bind(that);
    this.gltf = gltfHandler.bind(that);
    this.unload = unloadHandler.bind(that);
}
var operation = Object.freeze({
    'LEFT_CLICK': Symbol('GET_PAO'),
    'LEFT_DOUBLE_CLICK': Symbol('LEFT_DOUBLE_CLICK'),
    'RIGHT_CLICK': Symbol('RIGHT_CLICK'),
    'MIDDLE_CLICK': Symbol('MIDDLE_CLICK'),
    'MOUSE_MOVE': Symbol('MOUSE_MOVE'),
    'WHEEL': Symbol('WHEEL')
});

function getEventType(eventtype) {
    switch (eventtype) {
        case operation.LEFT_CLICK:
            return Cesium.ScreenSpaceEventType.LEFT_CLICK;
        case operation.LEFT_DOUBLE_CLICK:
            return Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK;
        case operation.RIGHT_CLICK:
            return Cesium.ScreenSpaceEventType.RIGHT_CLICK;
        case operation.MIDDLE_CLICK:
            return Cesium.ScreenSpaceEventType.MIDDLE_CLICK;
        case operation.WHEEL:
            return Cesium.ScreenSpaceEventType.WHEEL;
        default:
            console.log('error: operation type wrong');
            return false;
    }
}

function clearHandler(options) {
    if (!checkOptions(options)) return false;
    var type = options.type,
        eventtype = getEventType(type);
    if (eventtype === false) return eventtype;
    if (screenSpaceEventHandler.getInputAction(eventtype))
        screenSpaceEventHandler.removeInputAction(eventtype)
    return true;
}

function get_position(options, next) {
    if (Object.prototype.toString.call(next) !== '[object Function]') {
        console.log('error: next must be a function')
        return;
    }
    var that = this;
    if (!checkOptions(options)) return false;
    var type = options.type;
    var eventtype = getEventType(type);
    if (eventtype === false) return;
    screenSpaceEventHandler.setInputAction(function (movement) {
        let longandlat = {},
            ellipsoid = exports._globe.ellipsoid,
            click_position = exports._viewer.camera.pickEllipsoid(movement.position, ellipsoid);
        if (click_position) {
            var carto_position = ellipsoid.cartesianToCartographic(click_position);
            longandlat.latitude = Cesium.Math.toDegrees(carto_position.latitude).toFixed(6);
            longandlat.longitude = Cesium.Math.toDegrees(carto_position.longitude).toFixed(6);
            longandlat.elevation = carto_position.height.toFixed(4);
            that.mapinfo.pao = longandlat;
        }
        next(longandlat);
    }, eventtype);
}

function get_pao() {
    position = carema.position
    cartographic = exports._globe.ellipsoid.cartesianToCartographic(position); //世界坐标转化为经纬度
    return {
        'lon': Cesium.Math.toDegrees(cartographic.longitude),
        'lat': Cesium.Math.toDegrees(cartographic.cartographic),
        'height': cartographic.height,
        'heading': Cesium.Math.toDegrees(carema.heading),
        'pitch': Cesium.Math.toDegrees(carema.pitch),
        'roll': Cesium.Math.toDegrees(carema.roll)
    };
}
var pickentity = {
    'tiles3d': {
        'feature': undefined,
        'color': undefined
    },
    'billboard': {
        'feature': undefined,
        'scale': undefined
    }
};

function changePickEntity() {
    try {
        if (pickentity.tiles3d.feature != undefined) {
            pickentity.tiles3d.feature.color = pickentity.tiles3d.color
        }
        if (pickentity.billboard.feature != undefined) {
            pickentity.billboard.feature.id.billboard.scale = pickentity.billboard.scale
        }

    } catch (err) {
        pickentity = {
            'tiles3d': {
                'feature': undefined,
                'color': undefined
            },
            'billboard': {
                'feature': undefined,
                'scale': undefined
            }
        };
    }
}

function get_info(options, next) {
    if (!checkOptions(options)) return false;
    if (Object.prototype.toString.call(next) !== '[object Function]') {
        console.log('error: next must be a function')
        return;
    }
    var eventtype = options.type,
        cesiumevent = getEventType(eventtype);
    if (cesiumevent === false) {
        console.log('error: no action or event type')
        return;
    }
   screenSpaceEventHandler.setInputAction(function (click) {
       let pickinfo = undefined,
            pickedfeature = exports._scene.pick(click.position);
        if (Cesium.defined(pickedfeature)) {
            if (Cesium.defined(pickedfeature.id) && Cesium.defined(pickedfeature.id.billboard)) {
                if (pickentity.billboard.feature != undefined) {
                    if (pickedfeature != pickentity['billboard'].feature) {
                        changePickEntity();
                        pickentity.billboard.feature = pickedfeature;
                        pickentity.billboard.scale = pickedfeature.id.billboard.scale;
                        pickedfeature.id.billboard['scale'] = 1.1
                    }
                } else {
                    pickentity['billboard'].feature = pickedfeature;
                    pickentity.billboard.scale = pickedfeature.id['billboard'].scale;
                    pickedfeature.id['billboard']['scale'] = 1.1
                }
                pickinfo = {
                    'id': pickedfeature.id.id,
                    'name': pickedfeature.id.name,
                    'type': pickedfeature.id.type,
                    'info': pickedfeature.id.info
                };
                next(pickinfo);
            } else {
                if (pickedfeature instanceof Cesium['Cesium3DTileFeature']) {
                    if (pickentity.tiles3d.feature != undefined) {
                        if (pickedfeature != pickentity['tiles3d'].feature) {
                            changePickEntity();
                            pickentity.tiles3d.feature = pickedfeature;
                            pickentity.tiles3d.color = pickedfeature.color;
                            pickedfeature.color = Cesium.Color['fromAlpha'](Cesium.Color.DODGERBLUE, 0.5)
                        }
                    } else {
                        pickentity.tiles3d.feature = pickedfeature;
                        pickentity.tiles3d.color = pickedfeature.color;
                        pickedfeature.color = Cesium.Color.fromAlpha(Cesium.Color.DODGERBLUE, 0.5)
                    }
                    pickinfo = {};
                    var propertyNames = pickedfeature.getPropertyNames();
                    for (let index = 0; index < propertyNames.length; ++index) {
                        pickinfo[propertyNames[index]] = pickedfeature['getProperty'](propertyNames[index]);
                    }
                    next(pickinfo);
                } else changePickEntity(),
                    next(pickinfo);
            }
        } else {
            changePickEntity()
        }
        next(pickinfo);
    }, cesiumevent)
}

function zoomHandler(entityname) {
    hprdata = arguments['length'] > 1 && arguments[1] !== undefined ? arguments[1] : {},
        height = hprdata.height || undefined,
        heading = hprdata.heading || undefined,
        pitch = hprdata.pitch || undefined;
    height === undefined && (height = 750);
    heading === undefined ? heading = Cesium.Math.toRadians(0) : heading = Cesium.Math.toRadians(heading);
    pitch === undefined ? pitch = Cesium.Math.toRadians(-45) : pitch = Cesium.Math.toRadians(pitch);
    exports._viewer.flyTo(datasourceForLayers[entityname], {
        'offset': new Cesium.HeadingPitchRange(heading, pitch, height)
    });
}

function setViewHandler(options) {
    if (!checkOptions(options)) return false;
    var prologue = options.prologue || false,
        direct = options.direct || false,
        lon = options.lon,
        lat = options.lat,
        height = options.height,
        heading = options.heading || 0,
        pitch = options.pitch || 0,
        roll = options.roll || 0,
        flyDuration = options.flyDuration || 3,
        rotateDuration = options.rotateDuration || 7.2;
    rotateDuration = rotateDuration * 1000;
    var _0x4e160c = 3600 / rotateDuration,
        _0x3f0d33 = 113,
        rotatetimer = 0;
    if (prologue) {
        carema.setView({
            'destination': new Cesium.Cartesian3.fromDegrees(113, 30, 18000000) //设置视角
        });
        var timer = setInterval(function () {
            _0x3f0d33 = _0x3f0d33 + _0x4e160c;
            if (_0x3f0d33 >= 180)
                _0x3f0d33 = -180;
            carema.setView({
                'destination': Cesium.Cartesian3.fromDegrees(_0x3f0d33, 30, 18000000)
            });
            rotatetimer = rotatetimer + 10;
            if (rotatetimer >= rotateDuration) {

                clearInterval(timer);
                lon != undefined && lat != undefined && height != undefined && (direct ? carema.flyTo({
                    'destination': Cesium.Cartesian3.fromDegrees(lon, lat, 50000),
                    'duration': flyDuration * 0.4,
                    'complete': () => {
                        carema.flyTo({
                            'destination': Cesium.Cartesian3.fromDegrees(lon, lat, height),
                            'orientation': {
                                'heading': Cesium.Math.toRadians(heading),
                                'pitch': Cesium.Math.toRadians(pitch),
                                'roll': Cesium.Math.toRadians(roll)
                            },
                            'duration': flyDuration * 0.6
                        });
                    }
                }) : carema.flyTo({
                    'destination': Cesium.Cartesian3['fromDegrees'](lon, lat, height),
                    'orientation': {
                        'heading': Cesium.Math.toRadians(heading),
                        'pitch': Cesium.Math.toRadians(pitch),
                        'roll': Cesium.Math['toRadians'](roll)
                    },
                    'duration': flyDuration
                }))
            }
        }, 10);
        return true;
    } else return carema['flyTo']({
        'destination': Cesium.Cartesian3.fromDegrees(lon, lat, height),
        'orientation': {
            'heading': Cesium.Math.toRadians(heading),
            'pitch': Cesium.Math.toRadians(pitch),
            'roll': Cesium.Math.toRadians(roll)
        },
        'duration': flyDuration
    }), true;
}

function operatorhandler(that) {
    this.get_position = get_position.bind(that);
    this.get_pao = get_pao.bind(that);
    this.get_info = get_info.bind(that);
    this.clear = clearHandler.bind(that);
    this.set_view = setViewHandler.bind(that);
    this.zoom_to = zoomHandler.bind(that);
}


window.CESIUM_BASE_URL = '/';
exports._viewer = undefined;
exports._scene = undefined;
exports._globe = undefined;
var carema = undefined,
    screenSpaceEventHandler = undefined,
    options = Object.assign({}, showobj, terrbox);
Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJmNWEyYzg0OC02NjQ3LTRkNDUtOWJmYy0yMzVhOWMyODUyOTYiLCJpZCI6MTMyNjMsImlhdCI6MTYyNjE0NDMxMH0.4Pl52S18EdnkNKbE8ucpI9-2qinrgSTGD6OXcoBt1Mk';

function map() {
    this.layers = {};
    this.mapinfo = {};
    let loader = new loaderhandler(this),
        operator = new operatorhandler(this),
        layer = new layerhandler(this);
    this._loader = loader;
    this._operator = operator;
    this._layer = layer;
  
}

Object.defineProperties(map.prototype, {
    loader: {
        get: function() {
            return this._loader;
        }
    },
    operator: {
        get: function () {
            return this._operator;
        }
    },
   
    layer: {
        get: function() {
            return this._layer;
        }
    }
})
map.prototype.bind = function (container) {
    console.log('container',container)
    if (container && document.getElementById(container)) {
        console.log('1')
        exports._viewer = new Cesium.Viewer(container, options)
        exports._viewer._cesiumWidget._creditContainer.style.display = 'none'
        exports._scene = exports._viewer.scene;
        exports._scene.postProcessStages.fxaa.enabled = true;
        exports._globe = exports._scene.globe;
        exports._globe.depthTestAgainstTerrain = true;
        console.log('5')
        carema = exports._viewer.camera;
        screenSpaceEventHandler = new Cesium.ScreenSpaceEventHandler(exports._scene.canvas)
    } else {
        console.log('error: no element named' + container)
    }
}


map.prototype.set = setHandler;
var type = {
    'SETTING': setting,
    'MAP': t_map,
    'FEATURE': feature,
    'LAYER': maplayer,
    'OPERATION': operation
};
exports.Map = map;
exports.TYPE = type;
exports.imagery_feature = imagery_feature;
exports.o_fires = o_fires;
Object.defineProperty(exports, "__esModule", {
    value: !0
})
})