const defaultColor = Cesium.Color.RED;
const defaultSpeed = 3;

function WaveCircleMaterialProperty(options) {
  options = Cesium.defaultValue(options, Cesium.defaultValue.EMPTY_OBJECT);

  this._definitionChanged = new Cesium.Event();
  this._color = undefined;
  this._colorSubscription = undefined;
  this._speed = undefined;
  this._speedSubscription = undefined;

  this.color = options.color;
  this.speed = options.speed;
}

Object.defineProperties(WaveCircleMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return Cesium.Property.isConstant(this._color) && Cesium.Property.isConstant(this._speed);
    },
  },
  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    },
  },
  color: Cesium.createPropertyDescriptor("color"),
  speed: Cesium.createPropertyDescriptor("speed"),
});

WaveCircleMaterialProperty.prototype.getType = function (time) {
  return "WaveCircle";
};

WaveCircleMaterialProperty.prototype.getValue = function (time, result) {
  if (!Cesium.defined(result)) {
    result = {};
  }
  result.color = Cesium.Property.getValueOrClonedDefault(this._color, time, defaultColor, result.color);
  result.speed = Cesium.Property.getValueOrClonedDefault(this._speed, time, defaultSpeed, result.speed);
  return result;
};

WaveCircleMaterialProperty.prototype.equals = function (other) {
  return (
    this === other || //
    (other instanceof WaveCircleMaterialProperty && //
      Cesium.Property.equals(this._color, other._color) && //
      Cesium.Property.equals(this._speed, other._speed))
  );
};

Cesium.WaveCircleMaterialProperty = WaveCircleMaterialProperty;

const type = "WaveCircle";

const source = `
uniform vec4 color;
uniform float speed;
uniform float count;
uniform float gradient;

czm_material czm_getMaterial(czm_materialInput materialInput)
{
  czm_material material = czm_getDefaultMaterial(materialInput);
  material.diffuse = 1.5 * color.rgb;
  vec2 st = materialInput.st;
  float dis = distance(st, vec2(0.5, 0.5));
  float per = fract(czm_frameNumber * speed / 1000.0);
  if(count == 1.0){
    if(dis > per * 0.5){
      discard;
    }else {
      material.alpha = color.a  * dis / per / 2.0;
    }
  } else {
    vec3 str = materialInput.str;
    if(abs(str.z)  > 0.001){
      discard;
    }
    if(dis > 0.5){
      discard;
    } else {
      float perDis = 0.5 / count;
      float disNum;
      float bl = 0.0;
      for(int i = 0; i <= 999; i++){
        if(float(i) <= count){
          disNum = perDis * float(i) - dis + per / count;
          if(disNum > 0.0){
            if(disNum < perDis){
              bl = 1.0 - disNum / perDis;
            }
            else if(disNum - perDis < perDis){
              bl = 1.0 - abs(1.0 - disNum / perDis);
            }
            material.alpha = pow(bl,(1.0 + 10.0 * (1.0 - gradient)));
          }
        }
      }
    }
  }
  return material;
}
`;

Cesium.Material._materialCache.addMaterial(type, {
  fabric: {
    type,
    uniforms: {
      color: defaultColor,
      speed: defaultSpeed,
      count: 4,
      gradient: 0.1
    },
    source,
  },
  translucent: function (material) {
    return true;
  },
});
