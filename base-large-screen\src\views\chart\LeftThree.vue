<template>
  <div class="box">
    <PartTitle title="饼图" :icon="icon" :position="1.5" />
    <EchartsContainer :option="chartOption" style="width:470px;height:250px" />
  </div>
</template>
<script setup>
import { onMounted, shallowRef, inject } from "vue";
import { storeToRefs } from 'pinia'
import PartTitle from "../../components/common/PartTitle.vue"
import EchartsContainer from "../../components/common/EchartsContainer.vue"
import icon from "../../assets/images/store.svg"
import { useChartStore } from "../../stores/modules/chart";

const { chartData } = useChartStore()

const chartOption = shallowRef({})

const pageTl = inject('pageTl')
const isEnter = inject('isEnter')

function initTl() {
  if (pageTl) {
    pageTl.add(setOption, 1.5)
  } else {
    setOption()
  }
}

function setOption() {
  if (isEnter && !isEnter.value) {
    return
  }
  const data = []
  if (chartData.leftThree && chartData.leftThree.length) {
    chartData.leftThree.forEach(item => {
      data.push({
        name: item.key,
        value: item.value
      })
    })
  }
  chartOption.value = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      top: '5%',
      left: 'center'
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 40,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data
      }
    ]
  }
}

onMounted(() => {
  initTl()
})

</script>
<style scoped>
.box {
  width: 500px;
  height: 290px;
  display: flex;
  flex-direction: column;
  background-color: #f5f5d5;
}
</style>
