export default {
  url: "/api/grain",
  method: "get",
  response: () => {
    return {
      orderRank: [
        {
          type: "面粉",
          rankData: [
            {
              name: "五得利面粉",
              sales: 1025,
            },
            {
              name: "金龙鱼面粉",
              sales: 987,
            },
            {
              name: "民天面粉",
              sales: 876,
            },
          ],
        },
        {
          type: "食用油",
          rankData: [
            {
              name: "鲁花花生油",
              sales: 1045,
            },
            {
              name: "金龙鱼花生油",
              sales: 927,
            },
            {
              name: "民天花生油",
              sales: 676,
            },
          ],
        },
        {
          type: "大米",
          rankData: [
            {
              name: "五得利大米",
              sales: 1025,
            },
            {
              name: "金龙鱼大米",
              sales: 987,
            },
            {
              name: "民天大米",
              sales: 876,
            },
          ],
        },
      ],
      "orderDatas|10": [
        {
          number: "DH-2022022201",
          name: "五得利面粉",
          count: "@integer(100, 500)袋",
          province: "@province",
          date: "2022.06.10",
        },
      ],
      inventory: {
        product: [
          {
            name: "大豆油",
            value: "1500吨",
            icon: "./mock/soybean_oil_icon.png",
          },
          {
            name: "花生油",
            value: "1400吨",
            icon: "./mock/peanut_oil_icon.png",
          },
          {
            name: "棕榈油",
            value: "1200吨",
            icon: "./mock/palm_oil_icon.png",
          },
        ],
        material: [
          {
            name: "大豆",
            value: "1500吨",
            icon: "./mock/soybean_icon.png",
          },
          {
            name: "花生",
            value: "1400吨",
            icon: "./mock/peanut_icon.png",
          },
          {
            name: "棕榈果",
            value: "1200吨",
            icon: "./mock/palm_icon.png",
          },
        ],
      },
      "produce|10": [
        {
          name: "日榨大豆生产线",
          state: "生产",
          capacity: "@integer(6000, 8000)吨",
        },
        {
          name: "日榨炼油脂生产线",
          state: "闲置",
          capacity: "@integer(6000, 8000)吨",
        },
      ],
      document: [
        {
          name: "营业执照",
          effective: "@integer(10, 365)",
          icon: "./mock/document1_icon.png",
        },
        {
          name: "安全生产经营许可证",
          effective: "@integer(10, 365)",
          icon: "./mock/document2_icon.png",
        },
        {
          name: "健康证",
          effective: "@integer(10, 365)",
          icon: "./mock/document3_icon.png",
        },
        {
          name: "某资质",
          effective: "@integer(10, 365)",
          icon: "./mock/document4_icon.png",
        },
        {
          name: "某资质",
          effective: "@integer(10, 365)",
          icon: "./mock/document1_icon.png",
        },
        {
          name: "某资质",
          effective: "@integer(10, 365)",
          icon: "./mock/document2_icon.png",
        },
        {
          name: "某资质",
          effective: "@integer(10, 365)",
          icon: "./mock/document3_icon.png",
        },
        {
          name: "某资质",
          effective: "@integer(10, 365)",
          icon: "./mock/document4_icon.png",
        },
      ],
      environment: [
        { day: "星期一", value: "@integer(0, 500)" },
        { day: "星期二", value: "@integer(0, 500)" },
        { day: "星期三", value: "@integer(0, 500)" },
        { day: "星期四", value: "@integer(0, 500)" },
        { day: "星期五", value: "@integer(0, 500)" },
        { day: "星期六", value: "@integer(0, 500)" },
        { day: "星期天", value: "@integer(0, 500)" },
      ],
      logistics: {
        car: {
          online: "@integer(100, 200)",
          offline: "@integer(1, 20)",
        },
        container: {
          online: "@integer(300, 500)",
          offline: "@integer(1, 20)",
        },
        carWarn: {
          normal: "@integer(100, 200)",
          abnormal: "@integer(10, 50)",
        },
      },
      staff: [
        { key: "劳务人员", value: "@integer(50, 200)" },
        { key: "派遣人员", value: "@integer(50, 200)" },
        { key: "合同员工", value: "@integer(50, 200)" },
        { key: "55岁以上人员", value: "@integer(50, 200)" },
      ],
      car: {
        use: "@integer(800, 1000)",
        unused: "@integer(10, 100)",
      },
      mapLabels: [
        {
          province: "四川",
          count: "@integer(600, 3000)",
        },
        {
          province: "山东",
          count: "@integer(600, 3000)",
        },
        {
          province: "湖南",
          count: "@integer(600, 3000)",
        },
        {
          province: "广西",
          count: "@integer(600, 3000)",
        },
      ],
    };
  },
};
