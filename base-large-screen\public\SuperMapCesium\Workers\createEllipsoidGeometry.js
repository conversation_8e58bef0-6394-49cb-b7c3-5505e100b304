define(["./when-b60132fc","./EllipsoidGeometry-77f654e8","./arrayFill-4513d7ad","./Check-7b2a090c","./buildModuleUrl-8958744c","./Cartographic-3309dd0d","./Math-119be1a3","./Cartesian2-47311507","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4","./VertexFormat-6446fca0"],(function(e,t,a,r,i,n,o,d,c,b,f,u,l,s,m,y,p,G,C){"use strict";return function(a,r){return e.defined(r)&&(a=t.EllipsoidGeometry.unpack(a,r)),t.EllipsoidGeometry.createGeometry(a)}}));
