//This file is automatically rebuilt by the Cesium build process.
export default "/**\n\
 * A built-in GLSL floating-point constant for <code>pi/6</code>.\n\
 *\n\
 * @alias czm_piOverSix\n\
 * @glslConstant\n\
 *\n\
 * @see CesiumMath.PI_OVER_SIX\n\
 *\n\
 * @example\n\
 * // GLSL declaration\n\
 * const float czm_piOverSix = ...;\n\
 *\n\
 * // Example\n\
 * float pi = 6.0 * czm_piOverSix;\n\
 */\n\
const float czm_piOverSix = 0.5235987755982988;\n\
";
