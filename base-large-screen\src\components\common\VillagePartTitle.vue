<template>
  <div class="title-box">
    <AnimationText class="title-text" :text="props.title" :position="props.position" />
  </div>
</template>
<script setup>
import { onMounted } from "vue";
import AnimationText from "./AnimationText.vue"

const props = defineProps({
  title: {
    type: String
  },
  position: {
    type: Number
  }
})

onMounted(() => {

})
</script>
<style scoped>
.title-box {
  display: flex;
  align-items: center;
  width: 465px;
  height: 59px;
  background-image: url('../../assets/images/village_title_bg.png');
  background-size: 100% 100%;
  padding-left: 64px;
}


.title-text {
  font-size: 24px;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
}

:deep(.aki__char) {
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-image: linear-gradient(5deg, rgba(133, 255, 177, 0.96) 0%, rgba(214, 255, 229, 0.96) 20.2392578125%, rgba(255, 255, 255, 0.96) 87.2314453125%, rgba(133, 255, 177, 0.96) 100%);
}
</style>
