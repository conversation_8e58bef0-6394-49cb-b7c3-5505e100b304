<template>
  <div class="box-shadow">
    <div class="box">
      <VillagePartTitle title="特色旅游" :position="1.5" />
      <div class="news-box">
        <img class="poster" :src="pageData.tourism && pageData.tourism.poster" alt="icon" align="left">
        <p class="name">{{ pageData.tourism && pageData.tourism.name }}</p>
        <p class="content">{{ pageData.tourism && pageData.tourism.introduce }}</p>
      </div>
    </div>
  </div>
</template>
<script setup>
import { onMounted, shallowRef, inject, computed } from "vue";
import VillagePartTitle from "../../components/common/VillagePartTitle.vue"
import { useVillageStore } from "../../stores/modules/village";

const { pageData = {} } = useVillageStore()

const completeRate = computed(() => {

})

onMounted(() => {

})

</script>
<style scoped lang="scss">
.box-shadow {
  width: 465px;
  height: 203px;
  background: linear-gradient(0deg, #f0fff5c3, rgba(240, 255, 245, 0));
  box-shadow: 0px 4px 3px 0px rgba(0, 255, 93, 0.3), 0px 3px 2px 0px rgba(2, 13, 6, 0.77);
}

.box {
  width: 465px;
  height: 203px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(270deg, #0A170F, #0A170F);
  box-shadow: 0px -11px 21px 0px rgba(81, 207, 127, 0.17);
  border-radius: 0px 0px 14px 14px;
}

.news-box {
  width: 403px;
  height: 123px;
  margin-top: 10px;
  overflow: hidden;

  .poster {
    width: 153px;
    height: 84px;
    margin-right: 20px;
    margin-bottom: 6px;
    object-fit: cover;
  }

  .name {
    font-size: 16px;
    line-height: 16px;
    font-family: zcoolwenyiti;
    font-weight: 400;
    color: #FFFEFE;
    margin-bottom: 10px;
  }

  .content {
    font-size: 12px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    color: #E5FFEF;
    line-height: 16px;
    margin-bottom: 0px;
  }
}
</style>
