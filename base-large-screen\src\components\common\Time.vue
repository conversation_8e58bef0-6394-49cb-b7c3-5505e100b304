<template>
  <div class="time-box">
    <span class="date">{{ date }}</span>
    <span class="hms">{{ hms }}</span>
    <span class="week">{{ week }}</span>
  </div>
</template>
<script setup>
import { onMounted, computed, ref, onUnmounted } from "vue";

let intervalkey

const weekMap = ['日', '一', '二', '三', '四', '五', '六']

const nowDate = ref(new Date())

const hms = computed(() => {
  const h = getStandard(nowDate.value.getHours())
  const m = getStandard(nowDate.value.getMinutes())
  const s = getStandard(nowDate.value.getSeconds())
  return `${h}:${m}:${s}`
})
const date = computed(() => {
  const year = nowDate.value.getFullYear()
  const month = getStandard(nowDate.value.getMonth() + 1)
  const day = getStandard(nowDate.value.getDate())
  return `${year}-${month}-${day}`
})
const week = computed(() => `星期${weekMap[nowDate.value.getDay()]}`)

function getStandard(n) {
  return n < 10 ? `0${n}` : n
}

function startTime() {
  intervalkey = setInterval(() => {
    nowDate.value = new Date()
  }, 1000)
}
onMounted(() => {
  startTime()
})
onUnmounted(() => {
  clearInterval(intervalkey)
})
</script>
<style scoped>
.time-box {
  display: flex;
  align-items: flex-end;
}

.hms {
  font-size: 16px;
  margin: 0px 5px;
}

.date {
  font-size: 16px;
  margin: 0px 5px;
}

.week {
  font-size: 16px;
  margin: 0px 5px;
}
</style>
