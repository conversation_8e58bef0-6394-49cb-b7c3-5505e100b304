define(["./when-b60132fc","./Cartographic-3309dd0d","./Check-7b2a090c","./EllipsoidOutlineGeometry-6312b9fc","./Math-119be1a3","./arrayFill-4513d7ad","./buildModuleUrl-8958744c","./Cartesian2-47311507","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4"],(function(e,i,t,a,r,n,o,s,d,l,u,c,p,m,y,b,G,f){"use strict";function k(t){var r=e.defaultValue(t.radius,1),n={radii:new i.Cartesian3(r,r,r),stackPartitions:t.stackPartitions,slicePartitions:t.slicePartitions,subdivisions:t.subdivisions};this._ellipsoidGeometry=new a.EllipsoidOutlineGeometry(n),this._workerName="createSphereOutlineGeometry"}k.packedLength=a.EllipsoidOutlineGeometry.packedLength,k.pack=function(e,i,t){return a.EllipsoidOutlineGeometry.pack(e._ellipsoidGeometry,i,t)};var v=new a.EllipsoidOutlineGeometry,E={radius:void 0,radii:new i.Cartesian3,stackPartitions:void 0,slicePartitions:void 0,subdivisions:void 0};return k.unpack=function(t,r,n){var o=a.EllipsoidOutlineGeometry.unpack(t,r,v);return E.stackPartitions=o._stackPartitions,E.slicePartitions=o._slicePartitions,E.subdivisions=o._subdivisions,e.defined(n)?(i.Cartesian3.clone(o._radii,E.radii),n._ellipsoidGeometry=new a.EllipsoidOutlineGeometry(E),n):(E.radius=o._radii.x,new k(E))},k.createGeometry=function(e){return a.EllipsoidOutlineGeometry.createGeometry(e._ellipsoidGeometry)},function(i,t){return e.defined(t)&&(i=k.unpack(i,t)),k.createGeometry(i)}}));
