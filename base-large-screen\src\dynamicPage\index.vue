<template>
  <LayoutBox :parts="parts" :key="compKey" :focus="false" :direct-display="layer === 'firstLayer'">
  </LayoutBox>
</template>
<script setup>
import { onMounted, ref, shallowRef, inject, onUnmounted, watch, computed } from "vue";
import { useRoute } from "vue-router";
import { Components } from "./utils"

const route = useRoute();
const layer = inject('layer')

const parts = computed(() => {
  const list = []
  if (layer && route.meta.layers && route.meta.layers[layer]) {
    for (const comp of route.meta.layers[layer]) {
      const { key, style = {}, animation, focus, title } = comp
      if (!Components[key]) {
        continue
      }
      const { positionStyle, customStyle } = splitStyle(style)
      const part = {
        component: Components[key],
        positionStyle,
        customStyle,
        animation,
        focus,
        title
      }
      list.push(part)
    }
  }
  return list
})

const compKey = computed(() => {
  if (layer && route.meta.layers && route.meta.layers[layer]) {
    return JSON.stringify(route.meta.layers[layer].map((c) => c.key))
  }
})

const PositionKeys = ['top', 'bottom', 'left', 'right']

function splitStyle(style) {
  const positionStyle = {}
  const customStyle = {}
  for (const key in style) {
    if (Object.hasOwnProperty.call(style, key)) {
      if (PositionKeys.includes(key)) {
        positionStyle[key] = style[key];
      } else {
        customStyle[key] = style[key];
      }
    }
  }
  return { positionStyle, customStyle }
}
</script>
<style scoped></style>
