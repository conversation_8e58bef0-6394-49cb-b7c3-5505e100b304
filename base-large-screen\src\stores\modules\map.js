import { ref, shallowRef } from "vue";
import { defineStore } from "pinia";
import { useSystemStore } from "../system";
import { MAP_CONFIG } from "../../config";

export const useMapStore = defineStore("map", () => {
  const { locationData } = useSystemStore();
  const mapType = ref(MAP_CONFIG.defaultType);
  const mapView = ref({ center: locationData.center, zoom: 10, height: 395915 });
  const markData = shallowRef([]);
  const polygonData = shallowRef([]);
  const buildingData = shallowRef([]);
  const integrationData = shallowRef([]);
  const placeList = ref([]);

  function setMapType(type) {
    mapType.value = type;
  }

  function setPlaceList(data) {
    placeList.value = data;
  }

  function setMapView(data) {
    mapView.value = data;
  }
  function setDefaultView() {
    mapView.value = { center: locationData.center, zoom: 10, height: 395915 };
  }

  function setMarkData(data) {
    markData.value = data;
  }

  function setPolygonData(data) {
    polygonData.value = data;
  }

  function setBuildingData(data) {
    buildingData.value = data;
  }

  function setIntegrationData(data) {
    integrationData.value = data;
  }

  return {
    mapType,
    placeList,
    mapView,
    markData,
    polygonData,
    buildingData,
    integrationData,
    setMapType,
    setPlaceList,
    setMapView,
    setDefaultView,
    setMarkData,
    setPolygonData,
    setBuildingData,
    setIntegrationData,
  };
});
