//This file is automatically rebuilt by the Cesium build process.
export default "/**\n\
 * Struct for representing the output of a custom vertex shader.\n\
 * \n\
 * @name czm_modelVertexOutput\n\
 * @glslStruct\n\
 *\n\
 * @see {@link CustomShader}\n\
 * @see {@link ModelExperimental}\n\
 *\n\
 * @property {vec3} positionMC The position of the vertex in model coordinates\n\
 * @property {float} pointSize A custom value for gl_PointSize. This is only used for point primitives. \n\
 */\n\
struct czm_modelVertexOutput {\n\
  vec3 positionMC;\n\
  float pointSize;\n\
};\n\
";
