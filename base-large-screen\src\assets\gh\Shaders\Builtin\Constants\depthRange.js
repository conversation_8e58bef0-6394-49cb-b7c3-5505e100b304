//This file is automatically rebuilt by the Cesium build process.
export default "/**\n\
 * A built-in GLSL vec2 constant for defining the depth range.\n\
 * This is a workaround to a bug where IE11 does not implement gl_DepthRange.\n\
 *\n\
 * @alias czm_depthRange\n\
 * @glslConstant\n\
 *\n\
 * @example\n\
 * // GLSL declaration\n\
 * float depthRangeNear = czm_depthRange.near;\n\
 * float depthRangeFar = czm_depthRange.far;\n\
 *\n\
 */\n\
const czm_depthRangeStruct czm_depthRange = czm_depthRangeStruct(0.0, 1.0);\n\
";
