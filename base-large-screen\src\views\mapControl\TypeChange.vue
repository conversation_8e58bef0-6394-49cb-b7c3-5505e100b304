<template>
  <div>
    <el-radio-group :model-value="mapStore.mapType" size="large" @change="mapTypeChange">
      <el-radio-button label="raster">影像地图</el-radio-button>
      <el-radio-button label="vector">电子地图</el-radio-button>
    </el-radio-group>
  </div>
</template>
<script setup>
import { useMapStore } from "../../stores/modules/map";

const mapStore = useMapStore();

function mapTypeChange(value) {
  mapStore.setMapType(value)
}
</script>
