import axios from "axios";
import { ElMessageBox } from "element-plus";

//会缓存的get请求url
const CacheGetApi = [];

let token = "";

function syncToken(query) {
  const cookieToken = document.cookie.replace(/(?:(?:^|.*;\s*)Admin-Token\s*\=\s*([^;]*).*$)|^.*$/, "$1");
  if (cookieToken) {
    token = cookieToken;
  } else if (query && query.token) {
    token = query.token;
  }
}

const request = axios.create({
  baseURL: "/",
  withCredentials: true,
});

request.interceptors.request.use(
  (config) => {
    if (!token) {
      syncToken();
    }
    if (token) {
      config.headers["Authorization"] = `Bearer  ${token}`;
    }
    return config;
  },
  (error) => {
    console.log(error);
    Promise.reject(error);
  }
);

request.interceptors.response.use(
  (response) => {
    let hitApi;
    if (response.config.method === "get") {
      hitApi = getCacheHitApi(response.config.url);
    }
    if (response.data && response.data.type) {
      return response.data;
    }
    if (response.status === 200 && response.data) {
      if (hitApi) {
        setCacheResponse(hitApi, response.data);
      }
      return response.data;
    }
    //失败时尝试匹配缓存
    if (hitApi) {
      return getCacheResponse(hitApi, response.data);
    }
    console.log("request fail");
    console.log(response);
    return Promise.reject(response.data);
  },
  (error) => {
    console.log("request error");
    console.log(error);
    if (error.response && error.response.status === 401 && token) {
      token = null;
      ElMessageBox.confirm("请检查登录状态，稍后再试", "接口请求未授权", {
        type: "warning",
      });
    } else if (error.config.method === "get") {
      const hitApi = getCacheHitApi(error.config.url);
      if (hitApi) {
        return getCacheResponse(hitApi, error);
      }
    }
    return Promise.reject(error);
  }
);

function getCacheHitApi(url) {
  const hitApi = CacheGetApi.find((api) => {
    return url.startsWith(api);
  });
  return hitApi;
}

function setCacheResponse(api, data) {
  try {
    const itemData = JSON.stringify(data);
    const itemkey = `API-CACHE-${api}`;
    window.localStorage.setItem(itemkey, itemData);
  } catch (error) {
    console.log(error);
  }
}

function getCacheResponse(api, errorData) {
  try {
    const itemkey = `API-CACHE-${api}`;
    let itemData = window.localStorage.getItem(itemkey);
    if (itemData) {
      return JSON.parse(itemData);
    } else {
      return Promise.reject(errorData);
    }
  } catch (error) {
    console.log(error);
    return Promise.reject(errorData);
  }
}

export { request, syncToken };
