<template>
  <div class="box-shadow">
    <div class="box">
      <VillagePartTitle title="人居环境" :position="0.5" />
      <div class="top">
        <img class="icon" src="../../assets/images/environment_icon.png" alt="icon">
        <div class="right">
          <div class="title">人居环境案件处理</div>
          <div class="line">
            <div class="key">完成数：</div>
            <GrowNumber class="value" :value="pageData.environmentCase && pageData.environmentCase.complete"
              :position="0.5" />
            <div class="key" style="margin-left: 34px;">完成率：</div>
            <span class="value">
              <GrowNumber class="value" :value="+(completeRate * 100).toFixed(1)" :position="0.5" :decimal="1" />%
            </span>
          </div>
          <div class="rate">
            <div class="block"></div>
            <div class="rate-bg"></div>
            <div class="rate-show" :style="{ width: `${completeRate * 216}px` }"></div>
            <div class="block"></div>
            <div class="key" style="margin-left: 5px;">总数：</div>
            <GrowNumber class="value" :value="pageData.environmentCase && pageData.environmentCase.total"
              :position="0.5" />
          </div>
        </div>
      </div>
      <div class="center">环境整治活动</div>
      <swiper-container class="swiper" :autoplay="true" :autoplay-delay="3000" :speed="1000" loop="true"
        slidesPerView='auto'>
        <swiper-slide class="item" v-for="(item, index) in pageData.environmentActivity">
          <img class="poster" :src="item.poster" alt="icon">
          <div class="name">{{ item.name }}</div>
        </swiper-slide>
      </swiper-container>
    </div>
  </div>
</template>
<script setup>
import { onMounted, shallowRef, inject, computed } from "vue";
import VillagePartTitle from "../../components/common/VillagePartTitle.vue"
import GrowNumber from "../../components/common/GrowNumber.vue"
import { useVillageStore } from "../../stores/modules/village";

const { pageData = {} } = useVillageStore()

const completeRate = computed(() => {
  if (pageData.environmentCase && pageData.environmentCase.total) {
    return (pageData.environmentCase.complete / pageData.environmentCase.total).toFixed(3)
  }
  return 0
})

onMounted(() => {

})

</script>
<style scoped lang="scss">
.box-shadow {
  width: 465px;
  height: 295px;
  background: linear-gradient(0deg, #f0fff5c3, rgba(240, 255, 245, 0));
  box-shadow: 0px 4px 3px 0px rgba(0, 255, 93, 0.3), 0px 3px 2px 0px rgba(2, 13, 6, 0.77);
}

.box {
  width: 465px;
  height: 295px;
  display: flex;
  flex-direction: column;
  background: linear-gradient(270deg, #0A170F, #0A170F);
  box-shadow: 0px -11px 21px 0px rgba(81, 207, 127, 0.17);
  border-radius: 0px 0px 14px 14px;

  .top {
    height: 88px;
    width: 100%;
    display: flex;
    align-items: center;

    .key {
      font-size: 14px;
      font-family: AlibabaPuHuiTi;
      font-weight: 400;
      color: #FFFFFF;
    }

    .value {
      font-size: 18px;
      font-family: OPPOSans;
      font-weight: 800;
      color: #85FFB1;
    }

    .icon {
      width: 89px;
      height: 71px;
      flex-shrink: 0;
      margin-left: 28px;
      margin-right: 14px;
    }

    .right {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
      height: 72px;
      width: 100%;
      line-height: 1.1;

      .title {
        font-size: 18px;
        font-family: YouSheBiaoTiHei;
        font-weight: 400;
        font-style: italic;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-image: linear-gradient(0deg, #79C796 0%, #FFFFFF 100%);
      }

      .line {
        display: flex;
        align-items: center;
      }

      .rate {
        width: 100%;
        height: 16px;
        display: flex;
        align-items: center;

        .block {
          width: 5px;
          height: 14px;
          background: #73E69D;
          flex-shrink: 0px;
        }

        .rate-bg {
          width: 216px;
          height: 14px;
          background: #375742;
          opacity: 0.5;
          margin-left: 3px;
          margin-right: 3px;
        }

        .rate-show {
          height: 14px;
          background: linear-gradient(90deg, #375742 1%, #78F0A4 100%);
          position: absolute;
          left: 8px;
        }
      }
    }
  }

  .center {
    display: flex;
    align-items: center;
    width: 172px;
    height: 30px;
    margin-left: 16px;
    padding-left: 19px;
    padding-bottom: 17px;
    font-size: 18px;
    font-family: YouSheBiaoTiHei;
    font-weight: 400;
    color: #FFFFFF;
    -webkit-text-stroke: 0.5px #49C175;
    background-image: url('../../assets/images/little_title_bg.png');
    background-size: 100% 100%;
  }

  .swiper {
    width: 449px;
    height: 103px;
    margin-left: 16px;
  }

  .item {
    width: 151px;
    height: 103px;
    background: linear-gradient(-30deg, #1B2E22, #324F3D, #1F3627);
    margin-right: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 6px;

    .poster {
      width: 140px;
      height: 77px;
    }

    .name {
      font-size: 12px;
      font-weight: 400;
      color: #FFFFFF;
      opacity: 0.75;
    }
  }
}
</style>
