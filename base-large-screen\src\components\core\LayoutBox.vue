<template>
  <Suspense @resolve="resolve">
    <component v-if="props.directDisplay && props.parts[0]" ref="partRef" :is="props.parts[0].component"
      :style="partStyle(props.parts[0], 0)" :animation="props.parts[0].animation">
    </component>
    <div v-else class="layout-box" :style="boxStyle" style="pointer-events: none;">
      <template v-if="props.parts && props.parts.length">
        <component v-for="(item, index) in props.parts" ref="partRefs" :animation="item.animation"
          :position="getPosition(item.animation)" :is="item.component" :style="partStyle(item, index)" :title="item.title"
          @click.right="enterFocus(index, item.focus, $event)">
        </component>
        <div v-if="focusIndex !== null" class="focus-shadow" @click.self="exitFocus"></div>
      </template>
      <slot v-else></slot>
    </div>
  </Suspense>
</template>

<script setup>
import { ref, computed, onMounted, provide, nextTick, onUnmounted, watch } from "vue";
import { onBeforeRouteLeave, onBeforeRouteUpdate } from 'vue-router'
import gsap from "gsap";
import { APP_CONFIG, ANIMATIONS } from '../../config'
import { usePageScale } from '../../composables/pageScale'

const props = defineProps({
  parts: {
    type: Object,
    default: [],
  },
  focus: {
    type: Boolean,
    default: APP_CONFIG.focus
  },
  focusScale: {
    type: Number,
    default: APP_CONFIG.focusScale
  },
  focusColor: {
    type: String
  },
  ultraWide: {
    type: Object
  },
  directDisplay: {
    type: Boolean,
    default: false
  },
})
//进出场动画
const partRef = ref(null)
const partRefs = ref([])

const pageTl = gsap.timeline({ paused: true });

const isEnter = ref(true)

provide('pageTl', pageTl)
provide('isEnter', isEnter)

const resolve = async () => {
  await nextTick()
  enterTl()
}

function enterTl() {
  isEnter.value = true
  if (partRef.value && partRef.value.$el && partRef.value.$el instanceof HTMLElement && partRef.value.$attrs && partRef.value.$attrs.animation) {
    const { type, startTime } = partRef.value.$attrs.animation
    if (type && ANIMATIONS[type]) {
      const { from = {}, to = {} } = ANIMATIONS[type]
      const tween = gsap.fromTo(partRef.value.$el, from, to)
      pageTl.add(tween, +startTime)
    }
  }
  if (partRefs.value.length) {
    partRefs.value.forEach(p => {
      if (p.$el && p.$el instanceof HTMLElement && p.$attrs && p.$attrs.animation) {
        const { type, startTime } = p.$attrs.animation
        if (type && ANIMATIONS[type]) {
          const { from = {}, to = {} } = ANIMATIONS[type]
          const tween = gsap.fromTo(p.$el, from, to)
          pageTl.add(tween, +startTime)
        }
      }
    })
  }
  pageTl.resume();
  pageTl.then(() => {
    console.log('enterTl')
  })
}

function leaveTl() {
  isEnter.value = false
  return new Promise((resolve, reject) => {
    pageTl.timeScale(3)
    pageTl.reverse();
    pageTl.then(() => {
      console.log('leaveTl')
      resolve()
    })
  })
}

onBeforeRouteLeave((to, from) => {
  return leaveTl()
})
onBeforeRouteUpdate(() => {
  return leaveTl()
})
//样式自适应
const { layout, scale, adaptScale, aspectRatio, pageSize } = usePageScale()

const boxStyle = computed(() => {
  if (layout.value === 'adapt') {
    return {
      width: `${pageSize.height * aspectRatio.value}px`,
      height: `${pageSize.height}px`,
      transform: `scale(${adaptScale.value})`,
    }
  }
  return {
    width: `${pageSize.width}px`,
    height: `${pageSize.height}px`,
    transform: `scale(${scale.value})`,
  }
})
// 模块样式
function partStyle(item, index) {
  const { positionStyle = {}, ultraWidePosition = {}, customStyle = {} } = item
  if (index === focusIndex.value) {
    return focusStyle(positionStyle, customStyle)
  }
  if (layout.value === 'adapt' && props.ultraWide && props.ultraWide.aspectRatio && aspectRatio.value > props.ultraWide.aspectRatio) {
    return {
      position: 'absolute',
      ...customStyle,
      ...ultraWidePosition,
      scale: props.ultraWide.contentScale,
      transformOrigin: `${ultraWidePosition.left ? 'left' : 'right'} top`
    }
  }
  const style = {
    position: 'absolute',
    ...positionStyle,
    ...customStyle,
  }
  return style
}
//聚焦功能
const focusIndex = ref(null)
let isFocus = false

function focusStyle(positionStyle, customStyle) {
  const baseStyle = {
    ...customStyle,
    position: 'absolute',
    top: "50%",
    translate: "0 -50%",
    scale: props.focusScale,
    zIndex: 1,
    transitionDuration: "0.5s"
  }
  if (props.focusColor) {
    baseStyle.backgroundColor = props.focusColor
  }
  if (positionStyle.left) {
    return {
      ...baseStyle,
      left: "50%",
      translate: "-50% -50%",
    }
  }
  if (positionStyle.right) {
    return {
      ...baseStyle,
      right: "50%",
      translate: "50% -50%",
    }
  }
  return baseStyle
}

function exitFocus() {
  console.log('exitFocus')
  focusIndex.value = null
  isFocus = false
}
function enterFocus(index, focus, event) {
  console.log("enterFocus")
  if (isFocus) {
    return
  }
  if (focus || props.focus) {
    event.preventDefault()
    isFocus = true
    focusIndex.value = index
  }
}
const getPosition = (animation) => {
  if (animation && animation.type && ANIMATIONS[animation.type]) {
    const { to = {} } = ANIMATIONS[animation.type]
    return +(animation.startTime || 0) + (to.duration || 0.5)
  }
  return 0
}
</script>

<style>
.layout-box {
  position: relative;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.focus-shadow {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.11);
}

/* 
.layout-box::before {
  content: "";
  position: absolute;
  height: 100%;
  top: 0px;
  right: -100%;
  width: 100%;
  background-color: #ffffff;
}

.layout-box::after {
  content: "";
  position: absolute;
  height: 100%;
  top: 0px;
  left: -100%;
  width: 100%;
  background-color: #ffffff;
} */
</style>
