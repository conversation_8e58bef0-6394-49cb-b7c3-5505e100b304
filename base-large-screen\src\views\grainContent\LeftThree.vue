<template>
  <div class="box">
    <GrainPartTitle title="生产信息" :position="1.5" />
    <div class="line">
      <div class="title">生产线</div>
      <div class="title">产线状态</div>
      <div class="title">产能</div>
    </div>
    <swiper-container class="swiper" direction="vertical" :autoplay="true" :autoplay-delay="3000" :speed="1000"
      loop="true" slidesPerView='auto'>
      <swiper-slide class="item" v-for="(item, index) in pageData.produce" :style="slideStyle(index)">
        <div class="content" style="width:152px;text-align: left;">{{ item.name }}</div>
        <div class="content" style="width:100px;text-align: center;" :style="stateStyle(item.state)">{{ item.state }}
        </div>
        <div class="content" style="width:100px;text-align: right;">{{ item.capacity }}</div>
      </swiper-slide>
    </swiper-container>
  </div>
</template>
<script setup>
import GrainPartTitle from "../../components/common/GrainPartTitle.vue"
import { useGrainStore } from "../../stores/modules/grain";

const { pageData } = useGrainStore()


function slideStyle(index) {
  if (index % 2 === 0) {
    return {
      backgroundColor: "rgba(74,132,240,0.22)"
    }
  } else {
    return {
      backgroundColor: "rgba(74,132,240,0.12)"
    }
  }
}

function stateStyle(state) {
  if (state === "生产") {
    return {
      color: "#6BE4AD"
    }
  } else {
    return {
      color: "#E4BD6B"
    }
  }
}
</script>
<style scoped lang="scss">
.box {
  height: 252px;
  width: 450px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.line {
  width: 406px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 38px;

  .title {
    font-size: 15px;
    font-family: Alibaba PuHuiTi;
    font-weight: 400;
    color: #E6F0FE;
  }
}

.swiper {
  width: 406px;
  height: 170px;

  .item {
    width: 406px;
    height: 34px;
    padding: 0px 39px 0px 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .content {
    font-size: 14px;
    font-family: Alibaba PuHuiTi;
    font-weight: 500;
    color: #BDCBDD;
    line-height: 35px;
    text-align: right;
  }
}
</style>