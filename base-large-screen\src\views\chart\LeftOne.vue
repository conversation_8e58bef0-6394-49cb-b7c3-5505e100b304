<template>
  <div class="box" @click="openDialog">
    <PartTitle title="折线图" :icon="icon" :position="0.5" />
    <EchartsContainer :option="chartOption" style="width:470px;height:250px" />
    <UDialog v-if="dialogOpen" @close="dialogClose">
      <div class="dialog">
        弹窗内容
      </div>
    </UDialog>
  </div>
</template>
<script setup>
import { onMounted, ref, shallowRef, inject } from "vue";
import { storeToRefs } from 'pinia'
import PartTitle from "../../components/common/PartTitle.vue"
import EchartsContainer from "../../components/common/EchartsContainer.vue"
import icon from "../../assets/images/store.svg"
import { useChartStore } from "../../stores/modules/chart";

const { chartData } = useChartStore()

const chartOption = shallowRef({})

const pageTl = inject('pageTl')
const isEnter = inject('isEnter')

const dialogOpen = ref(false)

function openDialog() {
  dialogOpen.value = true
}

function dialogClose() {
  dialogOpen.value = false
}

function initTl() {
  if (pageTl) {
    pageTl.add(setOption, 0.5)
  } else {
    setOption()
  }
}

function setOption() {
  if (isEnter && !isEnter.value) {
    return
  }
  const series = []
  if (chartData.leftOne && chartData.leftOne.length) {
    chartData.leftOne.forEach(item => {
      series.push({
        name: item.key,
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: item.value
      })
    })
  }
  chartOption.value = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    grid: {
      left: '8',
      right: '8',
      top: '30',
      bottom: '8',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    series
  }
}

onMounted(() => {
  initTl()
})

</script>
<style scoped>
.box {
  width: 500px;
  height: 290px;
  display: flex;
  flex-direction: column;
  background-color: #f5f5d5;
}

.dialog {
  width: 400px;
  height: 300px;
  background-color: rgb(91, 200, 255);
}
</style>
