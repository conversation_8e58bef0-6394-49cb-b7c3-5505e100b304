<template>
  <div class="box">
    <PartTitle title="雷达图" :icon="icon" :position="0.5" />
    <EchartsContainer :option="chartOption" style="width:470px;height:250px" />
  </div>
</template>
<script setup>
import { onMounted, shallowRef, inject } from "vue";
import { storeToRefs } from 'pinia'
import PartTitle from "../../components/common/PartTitle.vue"
import EchartsContainer from "../../components/common/EchartsContainer.vue"
import icon from "../../assets/images/store.svg"
import { useChartStore } from "../../stores/modules/chart";

const { chartData } = useChartStore()

const chartOption = shallowRef({})

const pageTl = inject('pageTl')
const isEnter = inject('isEnter')

function initTl() {
  if (pageTl) {
    pageTl.add(setOption, 0.5)
  } else {
    setOption()
  }
}

function setOption() {
  if (isEnter && !isEnter.value) {
    return
  }
  const data = []
  if (chartData.rightOne && chartData.rightOne.length) {
    chartData.rightOne.forEach(item => {
      data.push({
        name: item.key,
        value: item.value
      })
    })
  }
  chartOption.value = {
    radar: {
      indicator: [
        { name: '维度一', max: 6500 },
        { name: '维度二', max: 16000 },
        { name: '维度三', max: 30000 },
        { name: '维度四', max: 38000 },
        { name: '维度五', max: 52000 },
        { name: '维度六', max: 25000 }
      ]
    },
    grid: {
      left: '8',
      right: '8',
      top: '1',
      bottom: '1'
    },
    series: [
      {
        name: 'Budget vs spending',
        type: 'radar',
        data
      }
    ]
  }
}

onMounted(() => {
  initTl()
})

</script>
<style scoped>
.box {
  width: 500px;
  height: 290px;
  display: flex;
  flex-direction: column;
  background-color: #f5f5d5;
}
</style>
