<template>
  <Teleport to="body" v-if="open">
    <div class="modal" @click="closeModal">
      <LayoutBox>
        <slot></slot>
      </LayoutBox>
    </div>
  </Teleport>
</template>
<script setup>
import { ref } from 'vue'

const props = defineProps({
  maskClosable: {
    type: Boolean,
    default: true
  }
})

const open = ref(true)

const emit = defineEmits(['close'])

function closeModal() {
  if (props.maskClosable) {
    open.value = false
    emit('close')
  }
}

</script>

<style scoped>
.modal {
  position: absolute;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.2);
}
</style>
