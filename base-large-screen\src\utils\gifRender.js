import { Icon } from "ol/style";

const gifCache = {};

let mapObj;

export async function getGifBillboard(src, styleParams) {
  if (!gifler) {
    console.log("gifler error");
    return;
  }
  await recordGifBind(src);
  if (gifCache[src] && gifCache[src].canvas) {
    return new Cesium.BillboardGraphics({
      image: new Cesium.CallbackProperty(() => {
        return gifCache[src].canvas.toDataURL();
      }, false),
      ...styleParams,
    });
  }
}

export async function getGifIcon(src, styleParams, map) {
  if (!gifler) {
    console.log("gifler error");
    return;
  }
  mapObj = map;
  await recordGifBind(src);
  if (gifCache[src] && gifCache[src].canvas) {
    return new Icon({
      ...styleParams,
      img: gifCache[src].canvas,
      imgSize: [gifCache[src].frame.width, gifCache[src].frame.height],
    });
  }
}

function recordGifBind(src) {
  return new Promise((resolve, reject) => {
    if (gifCache[src]) {
      if (gifCache[src].timeoutKey) {
        clearTimeout(gifCache[src].timeoutKey);
      }
      gifCache[src].useCount++;
      resolve();
    } else {
      createGif(src, resolve);
    }
  });
}

function createGif(src, resolve) {
  gifCache[src] = {
    useCount: 1,
  };
  const gif = gifler(src);
  gif
    .frames(
      document.createElement("canvas"),
      (ctx, frame) => {
        if (!gifCache[src].frame) {
          gifCache[src].canvas = ctx.canvas;
          gifCache[src].frame = frame;
          resolve();
        }
        ctx.clearRect(0, 0, frame.width, frame.height);
        ctx.drawImage(frame.buffer, frame.x, frame.y);
        mapRender();
      },
      true
    )
    .then((animator) => {
      gifCache[src].animator = animator;
    });
}

export function unbindGif(src) {
  if (!gifCache[src]) {
    return;
  }
  gifCache[src].useCount--;
  if (gifCache[src].useCount < 1) {
    delayDeleteGifCache(src);
  }
}

function delayDeleteGifCache(src) {
  if (gifCache[src].timeoutKey) {
    clearTimeout(gifCache[src].timeoutKey);
  }
  gifCache[src].timeoutKey = setTimeout(() => {
    if (gifCache[src].animator) {
      gifCache[src].animator.stop();
    }
    gifCache[src] = null;
  }, 1000 * 10);
}

export function destroyCache() {
  for (const key in gifCache) {
    if (Object.hasOwnProperty.call(gifCache, key)) {
      if (gifCache[key] && gifCache[key].animator) {
        gifCache[key].animator.stop();
      }
      gifCache[key] = null;
    }
  }
  mapObj = null;
}

function mapRender() {
  if (mapObj) {
    mapObj.render();
  }
}

//判断是gif
export function isGif(src) {
  if (src && /\.gif$/i.test(src)) {
    return true;
  }
  return false;
}
