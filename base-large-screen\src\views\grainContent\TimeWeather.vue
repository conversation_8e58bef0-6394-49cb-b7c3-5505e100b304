<template>
  <div class="box">
    <Time class="time" />
    <div class="line"></div>
    <Weather class="weather" />
  </div>
</template>
<script setup>
import Time from "../../components/common/Time.vue"
import Weather from "../../components/common/Weather.vue"
</script>
<style scoped>
.box {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.line {
  height: 20px;
  width: 1px;
  background-color: #FFFFFF;
  margin: 0px 5px;
}

.time {
  font-size: 16px;
  font-weight: 400;
  color: #FFFFFF;
  opacity: 0.7;
}

.weather {
  font-size: 16px;
  font-weight: 400;
  color: #FFFFFF;
  opacity: 0.7;
}

:deep(.week) {
  display: none;
}

:deep(.icon) {
  filter: drop-shadow(30px 0px #FFFFFF);
}
</style>