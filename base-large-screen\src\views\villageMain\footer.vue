<template>
  <div class="footer">
    <div class="botton-box">
      <img class="button" src="../../assets/images/footer_button_1.png" alt="button" @click="link" />
      <img class="button" src="../../assets/images/footer_button_2.png" alt="button" />
      <img class="button" src="../../assets/images/footer_button_3.png" alt="button" @click="link" />
      <img class="button" src="../../assets/images/footer_button_4.png" alt="button" @click="link" />
      <img class="button" src="../../assets/images/footer_button_5.png" alt="button" @click="link" />
      <img class="button" src="../../assets/images/footer_button_6.png" alt="button" @click="link" />
    </div>
  </div>
</template>
<script setup>
import { useRouter, useRoute } from "vue-router";

const router = useRouter();

function link() {
  router.push({
    name: "Grain"
  })
}
</script>
<style scoped>
.footer {
  left: 0px;
  width: 100%;
  height: 117px;
  background-image: url('../../assets/images/village_footer.png');
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
}

.botton-box {
  position: absolute;
  width: 55%;
  height: 50px;
  bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.button {
  width: 181px;
  height: 50px;
  cursor: pointer
}
</style>
