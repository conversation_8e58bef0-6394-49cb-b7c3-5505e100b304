import { request } from "@/api/request";

// 封装接口，返回直接数据，推荐配置默认数据
// 入参是路由query，可拼接接口请求参数
export function getDynamic1(query) {
  const params = { type: query.type };
  return new Promise((resolve, reject) => {
    request
      .get("/api/dynamic1", {
        params,
      })
      .then((res) => {
        //对获取的数据进行判断或处理，resolve最终数据
        if (res) {
          resolve(res);
        } else {
          resolve({});
        }
      })
      .catch((err) => {
        //失败也执行resolve
        resolve({});
      });
  });
}
export function getDynamic2() {
  return new Promise((resolve, reject) => {
    request
      .get("/api/dynamic2")
      .then((res) => {
        //对获取的数据进行判断或处理，resolve最终数据
        if (res) {
          resolve(res);
        } else {
          resolve({});
        }
      })
      .catch((err) => {
        //失败也执行resolve
        resolve({});
      });
  });
}
