<template>
  <div ref="dom" class="text">{{ props.text }}</div>
</template>
<script setup>
import { onMounted, inject, ref } from "vue";
import gsap from "gsap";
import { SplitText } from "../../utils/splitText"
import { ANIMATIONS } from '../../config'

const dom = ref(null)

const props = defineProps({
  text: {
    type: String,
  },
  position: {
    type: Number
  },
  type: {
    type: String,
    default: "ShrinkShow"
  }
})

const pageTl = inject('pageTl')

function initTween() {
  const { from, to } = ANIMATIONS[props.type] || ANIMATIONS["ShrinkShow"]

  const splittedText = new SplitText(dom.value)

  if (pageTl && typeof props.position !== 'undefined') {
    const numTween = gsap.fromTo(splittedText.chars, from, to);
    pageTl.add(numTween, props.position)
  } else {
    const delay = typeof props.position === 'number' ? props.position : 0
    gsap.fromTo(splittedText.chars, from, { ...to, delay });
  }

}
onMounted(() => {
  initTween()
})
</script>
<style scoped>
.num {
  color: #000;
}
</style>
