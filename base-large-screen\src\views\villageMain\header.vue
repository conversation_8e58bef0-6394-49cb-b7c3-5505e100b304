<template>
  <div class="header">
    <div class="left-bg"></div>
    <div class="center-bg"></div>
    <div class="right-bg"></div>
    <Time class="time" />
    <Weather class="weather" />
  </div>
</template>
<script setup>
import Time from "../../components/common/Time.vue"
import Weather from "../../components/common/Weather.vue"

</script>
<style scoped>
.header {
  left: 0px;
  width: 100%;
  height: 96px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.left-bg {
  position: absolute;
  left: 0px;
  top: 0px;
  width: calc(50% - 262px);
  height: 76px;
  background-image: url('../../assets/images/village_top_side.png');
  background-size: 100% 100%;
}

.right-bg {
  position: absolute;
  right: 0px;
  top: 0px;
  width: calc(50% - 262px);
  height: 76px;
  background-image: url('../../assets/images/village_top_side.png');
  background-size: 100% 100%;
  transform: rotateY(180deg);
}

.center-bg {
  width: 1144px;
  height: 96px;
  background-image: url('../../assets/images/village_top.png');
  background-size: 100% 100%;
}

.time {
  position: absolute;
  left: 212px;
  top: 11px;
  font-size: 16px;
  font-weight: 400;
  color: #FFFFFF;
  opacity: 0.7;
}

.weather {
  position: absolute;
  right: 22px;
  top: 6px;
  font-size: 16px;
  font-weight: 400;
  color: #FFFFFF;
  opacity: 0.7;
}

:deep(.week) {
  display: none;
}

:deep(.icon) {
  filter: drop-shadow(30px 0px #FFFFFF);
}
</style>
