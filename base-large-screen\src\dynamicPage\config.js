import * as shandongConfig from "@/shandongViews/config";

const Categorys = [
  {
    name: "山东组件",
    config: shandongConfig,
    storePrefix: "shandong",
  },
];

const ApiConfig = {};

const CompsConfig = {};

const Modules = {};

for (const category of Categorys) {
  const { name, config, storePrefix } = category;
  if (!storePrefix) {
    continue;
  }
  if (config.ApiConfig) {
    for (const key in config.ApiConfig) {
      if (config.ApiConfig[key]) {
        ApiConfig[`${storePrefix}_${key}`] = config.ApiConfig[key];
      }
    }
  }
  if (config.CompsConfig) {
    for (const key in config.CompsConfig) {
      if (config.CompsConfig[key]) {
        CompsConfig[key] = { ...config.CompsConfig[key], storePrefix, category: name };
      }
    }
  }
  if (config.Modules) {
    Object.assign(Modules, config.Modules);
  }
}

export { ApiConfig, CompsConfig, Modules, Categorys };
