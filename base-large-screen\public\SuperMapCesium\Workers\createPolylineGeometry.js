define(["./when-b60132fc","./Cartesian2-47311507","./ArcType-29cf2197","./arrayRemoveDuplicates-d2f048c5","./buildModuleUrl-8958744c","./Cartographic-3309dd0d","./Color-2a095a27","./ComponentDatatype-c140a87d","./Check-7b2a090c","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./IndexDatatype-8a5eead4","./Math-119be1a3","./PolylinePipeline-3454449c","./FeatureDetection-806b12f0","./VertexFormat-6446fca0","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian4-3ca25aab","./EllipsoidGeodesic-0f19ac62","./EllipsoidRhumbLine-ed1a6bf4","./IntersectionTests-a793ed08","./Plane-a3d8b3d2"],(function(e,t,r,a,o,i,n,l,s,p,d,c,u,y,m,f,h,v,C,_,A,g,E,w){"use strict";var b=[];function P(e,t,r,a,o){var i,l=b;l.length=o;var s=r.red,p=r.green,d=r.blue,c=r.alpha,u=a.red,y=a.green,m=a.blue,f=a.alpha;if(n.Color.equals(r,a)){for(i=0;i<o;i++)l[i]=n.Color.clone(r);return l}var h=(u-s)/o,v=(y-p)/o,C=(m-d)/o,_=(f-c)/o;for(i=0;i<o;i++)l[i]=new n.Color(s+i*h,p+i*v,d+i*C,c+i*_);return l}function T(a){var l,s,p=(a=e.defaultValue(a,e.defaultValue.EMPTY_OBJECT)).positions,d=a.colors,c=e.defaultValue(a.width,1),y=e.defaultValue(a.hMax,-1),m=e.defaultValue(a.colorsPerVertex,!1);this._positions=p,this._colors=d,this._width=c,this._hMax=y,this._colorsPerVertex=m,this._dist=a.dist,this._period=a.period,this._vertexFormat=f.VertexFormat.clone(e.defaultValue(a.vertexFormat,f.VertexFormat.DEFAULT)),this._followSurface=e.defaultValue(a.followSurface,!0),e.defined(a.followSurface)&&(l="PolylineGeometry.followSurface",s="PolylineGeometry.followSurface is deprecated and will be removed in Cesium 1.55. Use PolylineGeometry.arcType instead.",o.oneTimeWarning(l,s),a.arcType=a.followSurface?r.ArcType.GEODESIC:r.ArcType.NONE),this._arcType=e.defaultValue(a.arcType,r.ArcType.GEODESIC),this._followSurface=this._arcType!==r.ArcType.NONE,this._granularity=e.defaultValue(a.granularity,u.CesiumMath.RADIANS_PER_DEGREE),this._ellipsoid=t.Ellipsoid.clone(e.defaultValue(a.ellipsoid,t.Ellipsoid.WGS84)),this._workerName="createPolylineGeometry";var h=1+p.length*i.Cartesian3.packedLength;h+=e.defined(d)?1+d.length*n.Color.packedLength:1,this.packedLength=h+t.Ellipsoid.packedLength+f.VertexFormat.packedLength+4+2}T.pack=function(r,a,o){var l;o=e.defaultValue(o,0);var s=r._positions,p=s.length;for(a[o++]=p,l=0;l<p;++l,o+=i.Cartesian3.packedLength)i.Cartesian3.pack(s[l],a,o);var d=r._colors;for(p=e.defined(d)?d.length:0,a[o++]=p,l=0;l<p;++l,o+=n.Color.packedLength)n.Color.pack(d[l],a,o);return t.Ellipsoid.pack(r._ellipsoid,a,o),o+=t.Ellipsoid.packedLength,f.VertexFormat.pack(r._vertexFormat,a,o),o+=f.VertexFormat.packedLength,a[o++]=r._width,a[o++]=r._colorsPerVertex?1:0,a[o++]=r._arcType,a[o++]=r._granularity,a[o++]=r._hMax,a[o++]=r._dist,a[o]=r._period,a};var x=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),D=new f.VertexFormat,k={positions:void 0,colors:void 0,ellipsoid:x,vertexFormat:D,width:void 0,colorsPerVertex:void 0,arcType:void 0,granularity:void 0};T.unpack=function(r,a,o){var l;a=e.defaultValue(a,0);var s=r[a++],p=new Array(s);for(l=0;l<s;++l,a+=i.Cartesian3.packedLength)p[l]=i.Cartesian3.unpack(r,a);var d=(s=r[a++])>0?new Array(s):void 0;for(l=0;l<s;++l,a+=n.Color.packedLength)d[l]=n.Color.unpack(r,a);var c=t.Ellipsoid.unpack(r,a,x);a+=t.Ellipsoid.packedLength;var u=f.VertexFormat.unpack(r,a,D);a+=f.VertexFormat.packedLength;var y=r[a++],m=1===r[a++],h=r[a++],v=r[a++],C=r[a++],_=1==r[a++],A=r[a];return e.defined(o)?(o._positions=p,o._colors=d,o._ellipsoid=t.Ellipsoid.clone(c,o._ellipsoid),o._vertexFormat=f.VertexFormat.clone(u,o._vertexFormat),o._width=y,o._colorsPerVertex=m,o._arcType=h,o._granularity=v,o._hMax=C,o._dist=_,o._period=A,o):(k.positions=p,k.colors=d,k.width=y,k.colorsPerVertex=m,k.arcType=h,k.granularity=v,k.hMax=C,k.dist=_,k.period=A,new T(k))};var G=new i.Cartesian3,V=new i.Cartesian3,F=new i.Cartesian3,L=new i.Cartesian3;return T.createGeometry=function(t){var s,f,h,v=t._width,C=t._hMax,_=t._vertexFormat,A=t._colors,g=t._colorsPerVertex,E=t._arcType,w=t._granularity,T=t._ellipsoid,x=t._dist,D=t._period,k=a.arrayRemoveDuplicates(t._positions,i.Cartesian3.equalsEpsilon),S=k.length;if(!(S<2||v<=0)){if(E===r.ArcType.GEODESIC||E===r.ArcType.RHUMB){var O,M;E===r.ArcType.GEODESIC?(O=u.CesiumMath.chordLength(w,T.maximumRadius),M=y.PolylinePipeline.numberOfPoints):(O=w,M=y.PolylinePipeline.numberOfPointsRhumbLine);var I=y.PolylinePipeline.extractHeights(k,T);if(e.defined(A)){var R=1;for(s=0;s<S-1;++s)R+=M(k[s],k[s+1],O);var B=new Array(R),N=0;for(s=0;s<S-1;++s){var U=k[s],W=k[s+1],H=A[s],Y=M(U,W,O);if(g&&s<R){var q=P(0,0,H,A[s+1],Y),z=q.length;for(f=0;f<z;++f)B[N++]=q[f]}else for(f=0;f<Y;++f)B[N++]=n.Color.clone(H)}B[N]=n.Color.clone(A[A.length-1]),A=B,b.length=0}k=E===r.ArcType.GEODESIC?y.PolylinePipeline.generateCartesianArc({positions:k,minDistance:O,ellipsoid:T,height:I,hMax:C}):y.PolylinePipeline.generateCartesianRhumbArc({positions:k,granularity:O,ellipsoid:T,height:I})}var J,j=4*(S=k.length)-4,K=new Float64Array(3*j),Q=new Float64Array(3*j),X=new Float64Array(3*j),Z=new Float32Array(2*j),$=_.st?new Float32Array(2*j):void 0,ee=e.defined(A)?new Uint8Array(4*j):void 0,te=x?new Float32Array(3*j):void 0,re=0,ae=0,oe=0,ie=0,ne=0,le=0;for(f=0;f<S;++f){var se,pe;0===f?(J=G,i.Cartesian3.subtract(k[0],k[1],J),i.Cartesian3.add(k[0],J,J)):J=k[f-1],i.Cartesian3.clone(J,F),i.Cartesian3.clone(k[f],V),f===S-1?(J=G,i.Cartesian3.subtract(k[S-1],k[S-2],J),i.Cartesian3.add(k[S-1],J,J)):J=k[f+1],i.Cartesian3.clone(J,L),e.defined(ee)&&(se=0===f||g?A[f]:A[f-1],f!==S-1&&(pe=A[f]));var de=f===S-1?2:4;for(h=0===f?2:0;h<de;++h){i.Cartesian3.pack(V,K,re),i.Cartesian3.pack(F,Q,re),i.Cartesian3.pack(L,X,re),re+=3;var ce=h-2<0?-1:1,ue=h%2*2-1,ye=ue*f/S;if(Z[ae++]=C>0?ye:ue,Z[ae++]=ce*v,_.st&&($[oe++]=f/(S-1),$[oe++]=Math.max(Z[ae-2],0)),e.defined(ee)){var me=h<2?se:pe;ee[ie++]=n.Color.floatToByte(me.red),ee[ie++]=n.Color.floatToByte(me.green),ee[ie++]=n.Color.floatToByte(me.blue),ee[ie++]=n.Color.floatToByte(me.alpha)}x&&(te[3*ne]=le,ne++)}le+=i.Cartesian3.distance(J,k[f])}if(x){var fe=le,he=Math.random()*(D>0?D:fe);for(f=0;f<j;f++)te[3*f+1]=fe,te[3*f+2]=he}var ve=new d.GeometryAttributes;ve.position=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:K}),ve.prevPosition=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:Q}),ve.nextPosition=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:X}),ve.expandAndWidth=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:Z}),_.st&&(ve.st=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:$})),e.defined(ee)&&(ve.color=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:4,values:ee,normalize:!0})),x&&(ve.dist=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:te}));var Ce=c.IndexDatatype.createTypedArray(j,6*S-6),_e=0,Ae=0,ge=S-1;for(f=0;f<ge;++f)Ce[Ae++]=_e,Ce[Ae++]=_e+2,Ce[Ae++]=_e+1,Ce[Ae++]=_e+1,Ce[Ae++]=_e+2,Ce[Ae++]=_e+3,_e+=4;return new p.Geometry({attributes:ve,indices:Ce,primitiveType:m.PrimitiveType.TRIANGLES,boundingSphere:o.BoundingSphere.fromPoints(k),geometryType:p.GeometryType.POLYLINES})}},function(r,a){return e.defined(a)&&(r=T.unpack(r,a)),r._ellipsoid=t.Ellipsoid.clone(r._ellipsoid),T.createGeometry(r)}}));
