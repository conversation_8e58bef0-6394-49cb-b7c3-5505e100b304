define(["./AttributeCompression-90851096","./EllipsoidTangentPlane-ce9a1fbb","./buildModuleUrl-8958744c","./Cartesian2-47311507","./Cartographic-3309dd0d","./when-b60132fc","./TerrainEncoding-895d4619","./IndexDatatype-8a5eead4","./Math-119be1a3","./FeatureDetection-806b12f0","./OrientedBoundingBox-08964f84","./Check-7b2a090c","./GeometryAttribute-06a41648","./WebMercatorProjection-01b1b5e7","./createTaskProcessorWorker","./Cartesian4-3ca25aab","./IntersectionTests-a793ed08","./Plane-a3d8b3d2","./Event-16a2dfbf","./RuntimeError-4a5c8994","./ComponentDatatype-c140a87d","./WebGLConstants-4ae0db90","./PolygonPipeline-d328cdf1","./earcut-2.2.1-20c8012f","./EllipsoidRhumbLine-ed1a6bf4"],(function(e,t,r,i,n,o,a,s,d,u,c,h,l,I,m,g,f,v,T,p,E,y,w,N,x){"use strict";function M(){h.DeveloperError.throwInstantiationError()}Object.defineProperties(M.prototype,{errorEvent:{get:h.DeveloperError.throwInstantiationError},credit:{get:h.DeveloperError.throwInstantiationError},tilingScheme:{get:h.DeveloperError.throwInstantiationError},ready:{get:h.DeveloperError.throwInstantiationError},readyPromise:{get:h.DeveloperError.throwInstantiationError},hasWaterMask:{get:h.DeveloperError.throwInstantiationError},hasVertexNormals:{get:h.DeveloperError.throwInstantiationError},availability:{get:h.DeveloperError.throwInstantiationError}});var b=[];M.getRegularGridIndices=function(e,t){var r=b[e];o.defined(r)||(b[e]=r=[]);var i=r[t];return o.defined(i)||P(e,t,i=e*t<d.CesiumMath.SIXTY_FOUR_KILOBYTES?r[t]=new Uint16Array((e-1)*(t-1)*6+3*(e+t-2)):r[t]=new Uint32Array((e-1)*(t-1)*6+3*(e+t-2)),0),i},M.getRegularGridIndicesForReproject=function(e,t){var r=b[e];o.defined(r)||(b[e]=r=[]);var i=r[t];return o.defined(i)||P(e,t,i=e*t<d.CesiumMath.SIXTY_FOUR_KILOBYTES?r[t]=new Uint16Array((e-1)*(t-1)*6):r[t]=new Uint32Array((e-1)*(t-1)*6),0),i};var C=[];M.getRegularGridIndicesAndEdgeIndices=function(e,t){var r=C[e];o.defined(r)||(C[e]=r=[]);var i=r[t];if(!o.defined(i)){var n=M.getRegularGridIndices(e,t),a=S(e,t),s=a.westIndicesSouthToNorth,d=a.southIndicesEastToWest,u=a.eastIndicesNorthToSouth,c=a.northIndicesWestToEast;i=r[t]={indices:n,westIndicesSouthToNorth:s,southIndicesEastToWest:d,eastIndicesNorthToSouth:u,northIndicesWestToEast:c}}return i};var A=[];function S(e,t){var r,i=new Array(t),n=new Array(e),o=new Array(t),a=new Array(e);for(r=0;r<e;++r)a[r]=r,n[r]=e*t-1-r;for(r=0;r<t;++r)o[r]=(r+1)*e-1,i[r]=(t-r-1)*e;return{westIndicesSouthToNorth:i,southIndicesEastToWest:n,eastIndicesNorthToSouth:o,northIndicesWestToEast:a}}function P(e,t,r,i){for(var n=0,o=0;o<t-1;++o){for(var a=0;a<e-1;++a){var s=n,d=s+e,u=d+1,c=s+1;r[i++]=s,r[i++]=d,r[i++]=c,r[i++]=c,r[i++]=d,r[i++]=u,++n}++n}var h=(t-1)/2,l=(e-1)/2;n=0;for(a=0;a<l;a++)r[i++]=n,r[i++]=n+1,r[i++]=n+2,n+=2;n=e*(t-1);for(a=0;a<l;a++)r[i++]=n+1,r[i++]=n,r[i++]=n+2,n+=2;n=0;for(a=0;a<h;a++)r[i++]=n+e,r[i++]=n,r[i++]=n+2*e,n+=2*e;n=e-1;for(a=0;a<h;a++)r[i++]=n,r[i++]=n+e,r[i++]=n+2*e,n+=2*e}function W(e,t,r,i,n){for(var a=o.defined(n),s=e[0],d=e.length,u=1;u<d;++u){var c=e[u];!a||n[s+"_"+c]?(r[i++]=s,r[i++]=c,r[i++]=t,r[i++]=t,r[i++]=c,r[i++]=t+1,s=c,++t):(s=c,++t)}return i}M.getRegularGridAndSkirtIndicesAndEdgeIndices=function(e,t){var r=A[e];o.defined(r)||(A[e]=r=[]);var i=r[t];if(!o.defined(i)){var n=e*t,a=(e-1)*(t-1)*6,d=2*e+2*t,u=n+d,c=3*(e+t-2),h=a+6*Math.max(0,d-4)+c,l=S(e,t),I=l.westIndicesSouthToNorth,m=l.southIndicesEastToWest,g=l.eastIndicesNorthToSouth,f=l.northIndicesWestToEast,v=s.IndexDatatype.createTypedArray(u,h);P(e,t,v,0),M.addSkirtIndices(I,m,g,f,n,v,a+c),i=r[t]={indices:v,westIndicesSouthToNorth:I,southIndicesEastToWest:m,eastIndicesNorthToSouth:g,northIndicesWestToEast:f,indexCountWithoutSkirts:a}}return i},M.addSkirtIndices=function(e,t,r,i,n,o,a,s){var d=n;a=W(e,d,o,a,s),a=W(t,d+=e.length,o,a,s),a=W(r,d+=t.length,o,a,s),W(i,d+=r.length,o,a,s)},M.heightmapTerrainQuality=.25,M.getEstimatedLevelZeroGeometricErrorForAHeightmap=function(e,t,r){return 2*e.maximumRadius*Math.PI*M.heightmapTerrainQuality/(t*r)},M.prototype.requestTileGeometry=h.DeveloperError.throwInstantiationError,M.prototype.getLevelMaximumGeometricError=h.DeveloperError.throwInstantiationError,M.prototype.getTileDataAvailable=h.DeveloperError.throwInstantiationError,M.prototype.loadTileDataAvailability=h.DeveloperError.throwInstantiationError;var B=32767,_=new n.Cartesian3,F=new n.Cartesian3,D=new n.Cartesian3,k=new n.Cartographic,H=new i.Cartesian2,O=new n.Cartesian3,V=new u.Matrix4,G=new u.Matrix4;function Y(e,t,r,i,o,a,s,c,h){var l=Number.POSITIVE_INFINITY,I=o.north,m=o.south,g=o.east,f=o.west;g<f&&(g+=d.CesiumMath.TWO_PI);for(var v=e.length,T=0;T<v;++T){var p=e[T],E=r[p],y=i[p];k.longitude=d.CesiumMath.lerp(f,g,y.x),k.latitude=d.CesiumMath.lerp(m,I,y.y),k.height=E-t;var w=a.cartographicToCartesian(k,_);u.Matrix4.multiplyByPoint(s,w,w),n.Cartesian3.minimumByComponent(w,c,c),n.Cartesian3.maximumByComponent(w,h,h),l=Math.min(l,k.height)}return l}function R(t,r,i,a,s,c,h,m,g,f,v,T,p,E,y){var w=o.defined(h),N=g.north,x=g.south,M=g.east,b=g.west;M<b&&(M+=d.CesiumMath.TWO_PI);for(var C=i.length,A=0;A<C;++A){var S=i[A],P=s[S],W=c[S];k.longitude=d.CesiumMath.lerp(b,M,W.x)+E,k.latitude=d.CesiumMath.lerp(x,N,W.y)+y,k.height=P-f;var B,F=m.cartographicToCartesian(k,_);if(w){var D=2*S;if(H.x=h[D],H.y=h[D+1],1!==v){var Y=e.AttributeCompression.octDecode(H.x,H.y,O),R=l.Transforms.eastNorthUpToFixedFrame(_,m,G),U=u.Matrix4.inverseTransformation(R,V);u.Matrix4.multiplyByPointAsVector(U,Y,Y),Y.z*=v,n.Cartesian3.normalize(Y,Y),u.Matrix4.multiplyByPointAsVector(R,Y,Y),n.Cartesian3.normalize(Y,Y),e.AttributeCompression.octEncode(Y,H)}}a.hasWebMercatorT&&(B=(I.WebMercatorProjection.geodeticLatitudeToMercatorAngle(k.latitude)-T)*p),r=a.encode(t,r,F,W,k.height,H,B)}}function U(e,t){var r;return"function"==typeof e.slice&&"function"!=typeof(r=e.slice()).sort&&(r=void 0),o.defined(r)||(r=Array.prototype.slice.call(e)),r.sort(t),r}return m((function(h,m){var g,f,v=h.quantizedVertices,T=v.length/3,p=h.octEncodedNormals,E=h.westIndices.length+h.eastIndices.length+h.southIndices.length+h.northIndices.length,y=h.includeWebMercatorT,w=i.Rectangle.clone(h.rectangle),N=w.west,x=w.south,b=w.east,C=w.north,A=i.Ellipsoid.clone(h.ellipsoid),S=h.exaggeration,P=h.minimumHeight*S,W=h.maximumHeight*S,z=o.defined(h.validMinimumHeight)?h.validMinimumHeight*S:P*S,L=o.defined(h.validMaximumHeight)?h.validMaximumHeight*S:W*S,j=h.relativeToCenter,q=l.Transforms.eastNorthUpToFixedFrame(j,A),K=u.Matrix4.inverseTransformation(q,new u.Matrix4);y&&(g=I.WebMercatorProjection.geodeticLatitudeToMercatorAngle(x),f=1/(I.WebMercatorProjection.geodeticLatitudeToMercatorAngle(C)-g));var Q=v.subarray(0,T),X=v.subarray(T,2*T),Z=v.subarray(2*T,3*T),J=o.defined(p),$=new Array(T),ee=new Array(T),te=new Array(T),re=y?new Array(T):[],ie=F;ie.x=Number.POSITIVE_INFINITY,ie.y=Number.POSITIVE_INFINITY,ie.z=Number.POSITIVE_INFINITY;var ne=D;ne.x=Number.NEGATIVE_INFINITY,ne.y=Number.NEGATIVE_INFINITY,ne.z=Number.NEGATIVE_INFINITY;for(var oe=Number.POSITIVE_INFINITY,ae=Number.NEGATIVE_INFINITY,se=Number.POSITIVE_INFINITY,de=Number.NEGATIVE_INFINITY,ue=0;ue<T;++ue){var ce=Q[ue],he=X[ue],le=ce/B,Ie=he/B,me=d.CesiumMath.lerp(P,W,Z[ue]/B);k.longitude=d.CesiumMath.lerp(N,b,le),k.latitude=d.CesiumMath.lerp(x,C,Ie),k.height=me,oe=Math.min(k.longitude,oe),ae=Math.max(k.longitude,ae),se=Math.min(k.latitude,se),de=Math.max(k.latitude,de);var ge=A.cartographicToCartesian(k);$[ue]=new i.Cartesian2(le,Ie),ee[ue]=me,te[ue]=ge,y&&(re[ue]=(I.WebMercatorProjection.geodeticLatitudeToMercatorAngle(k.latitude)-g)*f),u.Matrix4.multiplyByPoint(K,ge,_),n.Cartesian3.minimumByComponent(_,ie,ie),n.Cartesian3.maximumByComponent(_,ne,ne)}var fe,ve,Te=U(h.westIndices,(function(e,t){return $[e].y-$[t].y})),pe=U(h.eastIndices,(function(e,t){return $[t].y-$[e].y})),Ee=U(h.southIndices,(function(e,t){return $[t].x-$[e].x})),ye=U(h.northIndices,(function(e,t){return $[e].x-$[t].x}));ve=r.BoundingSphere.fromPoints(te),fe=c.OrientedBoundingBox.fromRectangle(w,P,W,A);var we,Ne=c.OrientedBoundingBox.fromRectangle(w,z,L,A);(1!==S||P<0)&&(we=new a.EllipsoidalOccluder(A).computeHorizonCullingPointPossiblyUnderEllipsoid(j,te,P));var xe=P;xe=Math.min(xe,Y(h.westIndices,h.westSkirtHeight,ee,$,w,A,K,ie,ne)),xe=Math.min(xe,Y(h.southIndices,h.southSkirtHeight,ee,$,w,A,K,ie,ne)),xe=Math.min(xe,Y(h.eastIndices,h.eastSkirtHeight,ee,$,w,A,K,ie,ne)),xe=Math.min(xe,Y(h.northIndices,h.northSkirtHeight,ee,$,w,A,K,ie,ne));for(var Me=new t.AxisAlignedBoundingBox(ie,ne,j),be=new a.TerrainEncoding(Me,xe,W,q,J,y),Ce=be.getStride(),Ae=new Float32Array(T*Ce+E*Ce),Se=0,Pe=0;Pe<T;++Pe){if(J){var We=2*Pe;if(H.x=p[We],H.y=p[We+1],1!==S){var Be=e.AttributeCompression.octDecode(H.x,H.y,O),_e=l.Transforms.eastNorthUpToFixedFrame(te[Pe],A,G),Fe=u.Matrix4.inverseTransformation(_e,V);u.Matrix4.multiplyByPointAsVector(Fe,Be,Be),Be.z*=S,n.Cartesian3.normalize(Be,Be),u.Matrix4.multiplyByPointAsVector(_e,Be,Be),n.Cartesian3.normalize(Be,Be),e.AttributeCompression.octEncode(Be,H)}}Se=be.encode(Ae,Se,te[Pe],$[Pe],ee[Pe],H,re[Pe])}var De=Math.max(0,2*(E-4)),ke=h.indices.length+3*De,He=s.IndexDatatype.createTypedArray(T+E,ke);He.set(h.indices,0);var Oe=1e-4,Ve=(ae-oe)*Oe,Ge=(de-se)*Oe,Ye=-Ve,Re=Ve,Ue=Ge,ze=-Ge,Le=T*Ce;R(Ae,Le,Te,be,ee,$,p,A,w,h.westSkirtHeight,S,g,f,Ye,0),R(Ae,Le+=h.westIndices.length*Ce,Ee,be,ee,$,p,A,w,h.southSkirtHeight,S,g,f,0,ze),R(Ae,Le+=h.southIndices.length*Ce,pe,be,ee,$,p,A,w,h.eastSkirtHeight,S,g,f,Re,0),R(Ae,Le+=h.eastIndices.length*Ce,ye,be,ee,$,p,A,w,h.northSkirtHeight,S,g,f,0,Ue);var je=function(e,t,r,i){if(i<12)return;for(var n={},o=e.length,a=0;a<o;a+=3){var s=e[a],d=e[a+1],u=e[a+2];(t[s]===B&&t[d]===B||0===t[s]&&0===t[d]||r[s]===B&&r[d]===B||0===r[s]&&0===r[d])&&(n[s+"_"+d]=1,n[d+"_"+s]=1),(t[d]===B&&t[u]===B||0===t[d]&&0===t[u]||r[d]===B&&r[u]===B||0===r[d]&&0===r[u])&&(n[d+"_"+u]=1,n[u+"_"+d]=1),(t[u]===B&&t[s]===B||0===t[u]&&0===t[s]||r[u]===B&&r[s]===B||0===r[u]&&0===r[s])&&(n[u+"_"+s]=1,n[s+"_"+u]=1)}return n}(h.indices,Q,X,h.level);return M.addSkirtIndices(Te,Ee,pe,ye,T,He,h.indices.length,je),m.push(Ae.buffer,He.buffer),{vertices:Ae.buffer,indices:He.buffer,westIndicesSouthToNorth:Te,southIndicesEastToWest:Ee,eastIndicesNorthToSouth:pe,northIndicesWestToEast:ye,vertexStride:Ce,center:j,minimumHeight:P,maximumHeight:W,boundingSphere:ve,orientedBoundingBox:fe,validOrientedBoundingBox:Ne,occludeePointInScaledSpace:we,encoding:be,indexCountWithoutSkirts:h.indices.length}}))}));
