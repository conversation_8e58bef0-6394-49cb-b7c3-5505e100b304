<template>
  <div class="box">
    <PartTitle title="仪表盘" :icon="icon" :position="1" />
    <EchartsContainer :option="chartOption" style="width:470px;height:280px;margin-top: -30px;" />
  </div>
</template>
<script setup>
import { onMounted, shallowRef, inject } from "vue";
import { storeToRefs } from 'pinia'
import PartTitle from "../../components/common/PartTitle.vue"
import EchartsContainer from "../../components/common/EchartsContainer.vue"
import icon from "../../assets/images/store.svg"
import { useChartStore } from "../../stores/modules/chart";

const { chartData } = useChartStore()

const chartOption = shallowRef({})

const pageTl = inject('pageTl')
const isEnter = inject('isEnter')

function initTl() {
  if (pageTl) {
    pageTl.add(setOption, 1)
  } else {
    setOption()
  }
}

function setOption() {
  if (isEnter && !isEnter.value) {
    return
  }
  const gaugeData = [
    {
      value: 20,
      name: 'Perfect',
      title: {
        offsetCenter: ['0%', '-35%']
      },
      detail: {
        valueAnimation: true,
        offsetCenter: ['0%', '-20%']
      }
    },
    {
      value: 40,
      name: 'Good',
      title: {
        offsetCenter: ['0%', '-5%']
      },
      detail: {
        valueAnimation: true,
        offsetCenter: ['0%', '10%']
      }
    },
    {
      value: 60,
      name: 'Commonly',
      title: {
        offsetCenter: ['0%', '25%']
      },
      detail: {
        valueAnimation: true,
        offsetCenter: ['0%', '40%']
      }
    }
  ];
  if (chartData.rightTwo && chartData.rightTwo.length) {
    chartData.rightTwo.forEach((item, index) => {
      gaugeData[index].value = item.value
      gaugeData[index].name = item.key
    })
  }
  chartOption.value = {
    grid: {
      left: '8',
      right: '8',
      top: '1',
      bottom: '1'
    },
    series: [
      {
        type: 'gauge',
        startAngle: 90,
        endAngle: -270,
        pointer: {
          show: false
        },
        progress: {
          show: true,
          overlap: false,
          roundCap: true,
          clip: false,
          itemStyle: {
            borderWidth: 1,
            borderColor: '#464646'
          }
        },
        axisLine: {
          lineStyle: {
            width: 40
          }
        },
        splitLine: {
          show: false,
          distance: 0,
          length: 10
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false,
          distance: 50
        },
        data: gaugeData,
        title: {
          fontSize: 9
        },
        detail: {
          width: 40,
          height: 6,
          fontSize: 13,
          color: 'inherit',
          borderColor: 'inherit',
          borderRadius: 6,
          borderWidth: 1,
          formatter: '{value}%'
        }
      }
    ]
  }
}

onMounted(() => {
  initTl()
})

</script>
<style scoped>
.box {
  width: 500px;
  height: 290px;
  display: flex;
  flex-direction: column;
  background-color: #f5f5d5;
}
</style>
