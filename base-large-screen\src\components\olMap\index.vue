<template>
  <div ref="mapDom" class="map-box">
    <div ref="positionDom" class="position-box"></div>
    <div ref="dialogDom" id="olMap-overlay-container" :style="dialogDomStyle"></div>
  </div>
</template>
<script setup>
import { onMounted, onUnmounted, onBeforeUnmount, ref, watch, computed } from "vue";
import { onBeforeRouteLeave } from 'vue-router'
import Map from 'ol/Map';
import View from 'ol/View';
import TileLayer from 'ol/layer/Tile';
import VectorLayer from 'ol/layer/Vector';
import WMTS from 'ol/source/WMTS';
import VectorSource from 'ol/source/Vector';
import XYZSource from 'ol/source/XYZ';
import { get as getProjection, fromLonLat, transform } from 'ol/proj';
import WMTSTileGrid from 'ol/tilegrid/WMTS';
import { getTopLeft, getWidth } from 'ol/extent';
import MousePosition from 'ol/control/MousePosition';
import { createStringXY } from 'ol/coordinate';
import Feature from 'ol/Feature';
import GeomPoint from 'ol/geom/Point';
import GeomPolygon from 'ol/geom/Polygon';
import GeomCircle from 'ol/geom/Circle';
import { Style, Text, Icon, Stroke, Fill } from 'ol/style';
import Overlay from 'ol/Overlay'
import Projection from 'ol/proj/Projection';
import ImageLayer from 'ol/layer/Image';
import { getCenter } from 'ol/extent';
import Static from 'ol/source/ImageStatic';
import { ElMessage } from 'element-plus'
import { MAP_CONFIG } from "../../config"
import { useMapStore } from "../../stores/modules/map";
import { useMapDialogStore } from "../../stores/modules/mapDialog"
import { useSystemStore } from "../../stores/system";
import { ImageStyle, TextStyle, StrokeStyle, FillStyle } from "./style"
import { getGifIcon, unbindGif, destroyCache, isGif } from "../../utils/gifRender"
import { usePageScale } from '@/composables/pageScale'


const props = defineProps({
  base: {
    type: String,
    default: MAP_CONFIG.provider
  },
  imageExtent: {
    type: Array
  },
  image: {
    type: String
  },
  viewExtent: {
    type: Array
  },
})

const mapStore = useMapStore();
const dialogStore = useMapDialogStore();
const systemStore = useSystemStore();

const mapDom = ref(null)
const positionDom = ref(null)
const dialogDom = ref(null)

let isLeave = false

let mapObj
let mapOverlay

function initMap() {
  mapObj = new Map({ target: mapDom.value, controls: [] });
  // window.mapObj = mapObj
  if (props.base === 'TDT') {
    initTDTMap(mapStore.mapType)
  } else if (props.base === 'SDTDT') {
    initSDTDTMap(mapStore.mapType)
  } else if (props.base === "IMAGE") {
    initImageMap()
  }
  //鼠标在标签上显示手形
  mapObj.on('pointermove', (e) => {
    const pixel = mapObj.getEventPixel(e.originalEvent);
    const hit = mapObj.hasFeatureAtPixel(pixel, {
      layerFilter: (layer) => {
        return !!layer.get('markType');
      }
    });
    mapObj.getTarget().style.cursor = hit ? 'pointer' : '';
  });
}
function initMapOverlay() {
  mapOverlay = new Overlay({
    element: dialogDom.value, // 将自己写的 html 内容添加到覆盖层
    // position: evt.coordinate,
    positioning: 'top-left',// 覆盖层位置
    autoPan: true,// 是否自动平移，当点击时对话框超出屏幕边距，会自动平移地图使其可见
    offset: [0, -20],// 覆盖层偏移起点的位置
  })
  mapObj.addOverlay(mapOverlay)
}
//控制弹窗显隐
function setOverlayPosition(name) {
  if (name && dialogStore.dialogData.position) {
    mapOverlay.setPosition(dialogStore.dialogData.position)
  } else {
    mapOverlay.setPosition()
  }
}
function initEventListener() {
  mapObj.on('singleclick', (evt) => {
    dialogStore.close()
    // 获取点击的标注
    const obj = mapObj.forEachFeatureAtPixel(evt.pixel, (feature, layer) => {
      return { feature, layer };
    })
    if (!obj || !obj.feature) {
      return
    }
    if (!mapOverlay) {
      initMapOverlay()
    }
    const markType = obj.layer.get('markType')
    if (markType) {
      const dialogData = {
        id: obj.feature.getId(),
        name: markType,
        position: evt.coordinate,
      }
      dialogStore.openRequest(dialogData, 'ol')
    }
  })
}
//使用xyz方式，加载离线地图
function initOfflineMap() {
  const projection = "EPSG:3857"
  const viewParams = {
    center: getInitCenter(),
    projection,
    minZoom: 10,
    maxZoom: 18,
    zoom: 18
  }
  mapObj.setView(createView(viewParams))

  const control = createPositionControl(projection)
  mapObj.addControl(control)

  const layer = new TileLayer({
    source: new XYZSource({
      url: '/mapTiles/{z}/{x}/{y}.png'
    })
  })

  mapObj.setLayers([layer])
}
//图片底图
function initImageMap() {
  if (!props.image || !props.imageExtent) {
    return
  }
  const imageProjection = new Projection({
    code: 'map-image',
    units: 'pixels',
    extent: props.imageExtent,
  });
  const view = new View({
    projection: imageProjection,
    center: getCenter(props.imageExtent),
    zoom: 2,
    maxZoom: 5,
    extent: props.viewExtent
  })
  mapObj.setView(view)

  const layer = new ImageLayer({
    source: new Static({
      url: props.image,
      projection: imageProjection,
      imageExtent: props.imageExtent,
    }),
  })
  mapObj.setLayers([layer])

  const control = createPositionControl(imageProjection)
  mapObj.addControl(control)

}
//初始化天地图
function initTDTMap(type) {
  const projection = MAP_CONFIG.projection
  const viewParams = {
    center: getInitCenter(),
    projection,
    minZoom: 1,
    maxZoom: 18,
    zoom: mapStore.mapView.zoom,
    resolution: mapStore.mapView.resolution,
    extent: mapStore.mapView.extent
  }
  mapObj.setView(createView(viewParams))

  const control = createPositionControl(projection)
  mapObj.addControl(control)

  if (type === 'vector') {
    mapObj.setLayers(createTDTVector())
  } else if (type === 'raster') {
    mapObj.setLayers(createTDTRaster())
  }
}
//增加地图图层
function addTDTLayers(type) {
  if (type === 'vector') {
    const layers = createTDTVector()
    mapObj.addLayer(layers[0])
    mapObj.addLayer(layers[1])
  } else if (type === 'raster') {
    const layers = createTDTRaster()
    mapObj.addLayer(layers[0])
    mapObj.addLayer(layers[1])
  }
}
//天地图影像地图
function createTDTRaster() {
  const projection = MAP_CONFIG.projection
  const urlMark = projection === 'EPSG:4326' ? 'c' : 'w'
  const properties = {
    mapType: 'raster'
  }
  const layerAParams = {
    url: `${document.location.protocol}//t${Math.floor(Math.random() * 8)}.tianditu.gov.cn/img_${urlMark}/wmts?tk=${systemStore.mapKey}`,
    projection,
    layer: 'img',
    matrixSet: urlMark,
    format: 'tiles',
    style: 'default',
    maxZoom: 18,
    properties
  }
  const layerBParams = {
    url: `${document.location.protocol}//t${Math.floor(Math.random() * 8)}.tianditu.gov.cn/cia_${urlMark}/wmts?tk=${systemStore.mapKey}`,
    projection,
    layer: 'cia',
    matrixSet: urlMark,
    format: 'tiles',
    style: 'default',
    maxZoom: 18,
    properties
  }
  return [createWMTSLayer(layerAParams), createWMTSLayer(layerBParams)]
}
//天地图电子地图
function createTDTVector() {
  const projection = MAP_CONFIG.projection
  const urlMark = projection === 'EPSG:4326' ? 'c' : 'w'
  const properties = {
    mapType: 'vector'
  }
  const layerAParams = {
    url: `${document.location.protocol}//t${Math.floor(Math.random() * 8)}.tianditu.gov.cn/vec_${urlMark}/wmts?tk=${systemStore.mapKey}`,
    projection,
    layer: 'vec',
    matrixSet: urlMark,
    format: 'tiles',
    style: 'default',
    maxZoom: 18,
    properties
  }
  const layerBParams = {
    url: `${document.location.protocol}//t${Math.floor(Math.random() * 8)}.tianditu.gov.cn/cva_${urlMark}/wmts?tk=${systemStore.mapKey}`,
    projection,
    layer: 'cva',
    matrixSet: urlMark,
    format: 'tiles',
    style: 'default',
    maxZoom: 18,
    properties
  }
  return [createWMTSLayer(layerAParams), createWMTSLayer(layerBParams)]
}
//初始化山东天地图
function initSDTDTMap(type) {
  const projection = 'EPSG:4326'
  const viewParams = {
    center: getInitCenter(),
    projection,
    minZoom: 7,
    maxZoom: 18,
    zoom: mapStore.mapView.zoom,
    resolution: mapStore.mapView.resolution,
    extent: mapStore.mapView.extent
  }
  mapObj.setView(createView(viewParams))

  const control = createPositionControl(projection)
  mapObj.addControl(control)

  if (type === 'vector') {
    mapObj.setLayers(createSDTDTVector())
  } else if (type === 'raster') {
    mapObj.setLayers(createSDTDTRaster())
  }
}
//增加地图图层
function addSDTDTLayers(type) {
  if (type === 'vector') {
    const layers = createSDTDTVector()
    mapObj.addLayer(layers[0])
  } else if (type === 'raster') {
    const layers = createSDTDTRaster()
    mapObj.addLayer(layers[0])
    mapObj.addLayer(layers[1])
  }
}
//山东天地图影像地图
function createSDTDTRaster() {
  const projection = 'EPSG:4326'
  const properties = {
    mapType: 'raster'
  }
  const layerAParams = {
    url: `${document.location.protocol}//service.sdmap.gov.cn/tileservice/sdrasterpubmap?tk=${systemStore.mapKey}`,
    projection,
    layer: 'SDRasterPubMap',
    matrixSet: 'raster',
    format: 'image/jpeg',
    style: 'default',
    maxZoom: 18,
    properties
  }
  const layerBParams = {
    url: `${document.location.protocol}//service.sdmap.gov.cn/tileservice/sdrasterpubmapdj?tk=${systemStore.mapKey}`,
    projection,
    layer: 'SDRasterPubMapDJ',
    matrixSet: 'rasterdj',
    format: 'image/png',
    style: 'default',
    maxZoom: 18,
    properties
  }
  return [createWMTSLayer(layerAParams), createWMTSLayer(layerBParams)]
}
//山东天地图电子地图
function createSDTDTVector() {
  const projection = 'EPSG:4326'
  const properties = {
    mapType: 'vector'
  }
  const layerAParams = {
    url: `${document.location.protocol}//service.sdmap.gov.cn/tileservice/sdpubmap?tk=${systemStore.mapKey}`,
    projection,
    layer: 'SDPubMap',
    matrixSet: 'vector',
    format: 'image/png',
    style: 'default',
    maxZoom: 18,
    properties
  }
  return [createWMTSLayer(layerAParams)]
}
//创建view
function createView(params) {
  const { center, projection, minZoom, maxZoom, zoom, resolution, extent } = params
  return new View({
    center,//设置中心点
    projection: getProjection(projection),
    zoom,
    resolution,
    maxZoom,
    minZoom,
    enableRotation: false,
    extent: getExtent(extent)
  })
}
//获取地图限制范围
function getExtent(coordinates) {
  if (coordinates && coordinates.length > 1) {
    return [...getFitCoordinate(coordinates[0]), ...getFitCoordinate(coordinates[1])]
  }
}
//创建鼠标位置控制器
function createPositionControl(projection) {
  const mousePositionControl = new MousePosition({
    coordinateFormat: createStringXY(4),
    projection,
    target: positionDom.value,
  });
  return mousePositionControl
}
//创建wmts图层
function createWMTSLayer(params) {
  const { url, layer, matrixSet, projection, format = 'image/png', style = 'default', maxZoom = 18, opacity = 1, attributions = '', properties = {} } = params
  const projectionObj = getProjection(projection);
  const projectionExtent = projectionObj.getExtent();
  const size = getWidth(projectionExtent) / 256;
  const resolutions = new Array(maxZoom + 1);
  const matrixIds = new Array(maxZoom + 1);
  for (let z = 0; z <= maxZoom; z++) {
    // generate resolutions and matrixIds arrays for this WMTS
    resolutions[z] = size / Math.pow(2, z);
    matrixIds[z] = z;
  }
  return new TileLayer({
    opacity,
    source: new WMTS({
      attributions,
      url,
      layer,
      matrixSet,
      format,
      projection: projectionObj,
      tileGrid: new WMTSTileGrid({
        origin: getTopLeft(projectionExtent),
        resolutions,
        matrixIds,
      }),
      style,
      wrapX: true,
    }),
    properties
  })
}

//更新内容层状态
async function updateContentLayer(layerList, layerTypeKey, rewrite) {
  const layerMap = {}
  const allLayers = mapObj.getAllLayers()
  for (const layer of allLayers) {
    const layerTypeValue = layer.get(layerTypeKey)
    if (layerTypeValue) {
      layerMap[layerTypeValue] = layer
    }
  }
  for (const layerData of layerList) {
    const layerTypeValue = layerData[layerTypeKey]
    if (!layerTypeValue || !layerData.datas) {
      continue
    }
    if (layerData.visible) {
      if (layerMap[layerTypeValue]) {
        updateLayerVisible(layerMap[layerTypeValue], true)
        if (rewrite) {
          await rewriteContent(layerData, layerMap[layerTypeValue], layerTypeKey)
        } else {
          await updateContent(layerData, layerMap[layerTypeValue], layerTypeKey)
        }
      } else {
        await createLayer(layerData, layerTypeKey)
      }
    } else {
      if (layerMap[layerTypeValue]) {
        updateLayerVisible(layerMap[layerTypeValue], false)
      }
    }
  }
}
//重写层内容
async function rewriteContent(layerData, layer, layerTypeKey) {
  const source = layer.getSource()
  source.clear()
  const newFeatures = []
  for (const featureData of layerData.datas) {
    const cFeatures = await createFeatures(layerData, featureData, layerTypeKey)
    newFeatures.push(...cFeatures)
  }
  source.addFeatures(newFeatures)
}
//根据数据创建所有素材
async function createFeatures(layerData, featureData, layerTypeKey) {
  const features = []
  const { coordinate, coordinates, holesCoordinates, text, image, radius, status } = featureData
  const id = getFeatureID(featureData)
  if (layerTypeKey === 'markType' && coordinate && coordinate.length > 1) {
    if (text || image) {
      const geomPoint = createGeomPoint(coordinate)
      const style = await createMarkStyle(layerData, featureData)
      features.push(createFeature(geomPoint, style, status, id, image))
    }
    if (radius) {
      const geomCircle = createGeomCircle(coordinate, radius)
      const style = createCircleStyle(layerData, featureData)
      features.push(createFeature(geomCircle, style, status, `${id}-accessory`))
    }
  } else if (layerTypeKey === 'polygonType' && coordinates && coordinates.length > 1) {
    let geomPolygon
    if (holesCoordinates && holesCoordinates.length) {
      geomPolygon = createGeomPolygonHole(coordinates, holesCoordinates)
    } else {
      geomPolygon = createGeomPolygon(coordinates)
    }
    const style = createPolygonStyle(layerData, featureData)
    features.push(createFeature(geomPolygon, style, status, id))
    if (text || image) {
      const centerCoordinate = coordinate || geomPolygon.getInteriorPoint().getCoordinates();
      const geomPoint = createGeomPoint(centerCoordinate)
      const style = await createMarkStyle(layerData, featureData)
      features.push(createFeature(geomPoint, style, status, `${id}-accessory`, image))
    }
  }
  return features
}
//创建素材
function createFeature(geometry, style, status, id, image) {
  const feature = new Feature({
    geometry,// 标签位置
    status,
    image
  })
  feature.setStyle(style)
  feature.setId(id)
  return feature
}
//创建圆
function createGeomCircle(coordinate, radius) {
  const fitCoordinate = getFitCoordinate(coordinate)
  const fitRadius = getFitRadius(radius)
  return new GeomCircle(fitCoordinate, fitRadius)
}
//圆样式
function createCircleStyle(layerData, featureData) {
  const styleType = featureData.styleType || layerData.styleType || "default"
  return new Style({
    stroke: getStrokeStyle(styleType),
    fill: getFillStyle(styleType)
  })
}
//创建点
function createGeomPoint(coordinate) {
  const fitCoordinate = getFitCoordinate(coordinate)
  return new GeomPoint(fitCoordinate)
}
//点样式
async function createMarkStyle(layerData, featureData) {
  const { text, image } = featureData
  const styleType = featureData.styleType || layerData.styleType || "default"
  const imageStyle = await getImageStyle(styleType, image)
  return new Style({
    image: imageStyle,
    text: getTextStyle(styleType, text)
  })
}
//创建多边形
function createGeomPolygon(coordinates) {
  const fitCoordinates = coordinates.map(c => getFitCoordinate(c))
  return new GeomPolygon([fitCoordinates])
}
//创建挖洞多边形
function createGeomPolygonHole(coordinates, holesCoordinates) {
  const fitCoordinates = coordinates.map(c => getFitCoordinate(c))
  const fitHolesCoordinates = holesCoordinates.map((hole) => hole.map(c => getFitCoordinate(c)))
  return new GeomPolygon([fitCoordinates, ...fitHolesCoordinates])
}
//多边形样式
function createPolygonStyle(layerData, featureData) {
  const styleType = featureData.styleType || layerData.styleType || "default"
  return new Style({
    stroke: getStrokeStyle(styleType),
    fill: getFillStyle(styleType)
  })
}
//更新层内容
async function updateContent(layerData, layer, layerTypeKey) {
  const source = layer.getSource()
  for (const featureData of layerData.datas) {
    const featureID = getFeatureID(featureData)
    const feature = source.getFeatureById(featureID)
    //状态不同时，删除原有材质，新建材质
    if (feature && featureData.status !== feature.get('status')) {
      source.removeFeature(feature)
      const accessoryFeature = source.getFeatureById(`${featureID}-accessory`)
      if (accessoryFeature) {
        source.removeFeature(accessoryFeature)
      }
      const newFeatures = await createFeatures(layerData, featureData, layerTypeKey)
      source.addFeatures(newFeatures)
    }
  }
}
//创建层
async function createLayer(layerData, layerTypeKey) {
  const features = []
  for (const featureData of layerData.datas) {
    const cFeatures = await createFeatures(layerData, featureData, layerTypeKey)
    features.push(...cFeatures)
  }
  const vectorSource = new VectorSource({
    features,
  });
  let zIndex = 1
  if (layerTypeKey === 'markType') {
    zIndex = 2
  }
  if (layerData.zIndex) {
    zIndex = layerData.zIndex
  }
  const properties = {}
  properties[layerTypeKey] = layerData[layerTypeKey]
  const vectorLayer = new VectorLayer({
    source: vectorSource,
    zIndex,
    properties
  });
  mapObj.addLayer(vectorLayer)
  vectorSource.on('removefeature', (e) => {
    onRemovefeature(e.feature);
  });
}
//图标样式
async function getImageStyle(type, src) {
  if (!src) {
    return
  }
  const styleParams = ImageStyle[type] || ImageStyle.default
  if (isGif(src)) {
    const gifIcon = await getGifIcon(src, styleParams, mapObj)
    return gifIcon
  }
  return new Icon({
    ...styleParams,
    src,
  })
}
//文字样式
function getTextStyle(type, text) {
  if (!text) {
    return
  }
  const styleParams = TextStyle[type] || TextStyle.default
  return new Text({
    ...styleParams,
    text,
  })
}
//线条样式
function getStrokeStyle(type) {
  const styleParams = StrokeStyle[type] || StrokeStyle.default
  return new Stroke({
    ...styleParams,
  })
}
//填充样式
function getFillStyle(type) {
  const styleParams = FillStyle[type] || FillStyle.default
  return new Fill({
    ...styleParams,
  })
}
//layer显隐
function updateLayerVisible(layer, visible) {
  layer.setVisible(visible)
}
//标识ID
function getFeatureID(item) {
  return item.id || item.ID || item.Id
}
//获取适配的坐标
function getFitCoordinate(coordinate) {
  if (coordinate && coordinate.length === 2 && props.base === 'TDT' && MAP_CONFIG.projection === 'EPSG:3857') {
    return fromLonLat(coordinate)
  }
  return coordinate
}
//获取适配的半径值
function getFitRadius(radius) {
  if (props.base === 'SDTDT' || MAP_CONFIG.projection === 'EPSG:4326') {
    const fitCoordinate = transform([radius, 0], 'EPSG:3857', 'EPSG:4326')
    return fitCoordinate[0]
  }
  return radius
}
//获取初始化中心点坐标
function getInitCenter() {
  //默认中心
  let center = mapStore.mapView.center
  return getFitCoordinate(center)
}
//删除素材回调
function onRemovefeature(feature) {
  const image = feature.get('image')
  if (isGif(image)) {
    unbindGif(image)
  }
}

//弹窗大屏适配
const { layout, scale, adaptScale } = usePageScale()
const dialogDomStyle = computed(() => {
  if (layout.value === 'adapt') {
    return {
      transformOrigin: 'top left',
      transform: `scale(${adaptScale.value})`,
    }
  }
  return {
    transformOrigin: 'top left',
    transform: `scale(${scale.value})`,
  }
})
//切换地图视角
watch(() => mapStore.mapView, (newView, oldView) => {
  if (isLeave) {
    return
  }
  console.log('watch view')
  const view = mapObj.getView()
  view.animate({
    center: getFitCoordinate(newView.center), // 中心点
    zoom: newView.zoom, // 缩放级别
    duration: 1000 // 缩放持续时间，默认不需要设置
  })
})
//切换地图类型
watch(() => mapStore.mapType, (newType, oldType) => {
  if (isLeave) {
    return
  }
  console.log('watch mapType')
  let isLayerExist = false
  const layers = mapObj.getAllLayers()
  for (const layer of layers) {
    const mapType = layer.get('mapType')
    if (mapType) {
      if (mapType === newType) {
        updateLayerVisible(layer, true)
        isLayerExist = true
      } else {
        updateLayerVisible(layer, false)
      }
    }
  }
  if (!isLayerExist) {
    if (props.base === 'TDT') {
      addTDTLayers(newType)
    } else if (props.base === 'SDTDT') {
      addSDTDTLayers(newType)
    }
  }
})
//更新标识
watch(() => mapStore.markData, (newData, oldData) => {
  if (isLeave) {
    return
  }
  console.log('watch markData')
  updateContentLayer(newData, "markType", true)
})
//更新多边形
watch(() => mapStore.polygonData, (newData, oldData) => {
  if (isLeave) {
    return
  }
  console.log('watch polygonData')
  updateContentLayer(newData, "polygonType", true)
})
//更新弹窗状态
watch(() => dialogStore.activateName, (newName, oldName) => {
  if (isLeave) {
    return
  }
  setOverlayPosition(newName)
})
//按键监听
function onKeyEvent(e) {
  //ctrl+m
  if (e.ctrlKey && e.keyCode === 77) {
    copyPosition()
  }
}
//复制鼠标位置
function copyPosition() {
  try {
    const content = positionDom.value.children[0].textContent
    navigator.clipboard.writeText(content).then(() => {
      ElMessage({
        message: `鼠标位置：${content} 复制成功`,
        type: 'success',
      })
    })
  } catch (error) {
    console.log(error)
  }
}

onMounted(() => {
  document.addEventListener('keydown', onKeyEvent)
  initMap()
  updateContentLayer(mapStore.markData, "markType")
  updateContentLayer(mapStore.polygonData, "polygonType")
  initEventListener()
})

onBeforeRouteLeave(() => {
  isLeave = true
})
onBeforeUnmount(() => {
  destroyCache()
})
onUnmounted(() => {
  document.removeEventListener('keydown', onKeyEvent)
})
</script>
<style scoped>
.map-box {
  width: 100%;
  height: 100%;
}

.position-box {
  position: absolute;
  visibility: hidden;
}
</style>
