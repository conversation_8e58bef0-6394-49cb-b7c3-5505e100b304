<template>
  <div class="box">
    <PartTitle title="柱状图" :icon="icon" :position="1" />
    <EchartsContainer :option="chartOption" style="width:470px;height:250px" />
  </div>
</template>
<script setup>
import { onMounted, shallowRef, inject } from "vue";
import { storeToRefs } from 'pinia'
import * as echarts from 'echarts';
import PartTitle from "../../components/common/PartTitle.vue"
import EchartsContainer from "../../components/common/EchartsContainer.vue"
import icon from "../../assets/images/store.svg"
import { useChartStore } from "../../stores/modules/chart";

const { chartData } = useChartStore()

const chartOption = shallowRef({})

const pageTl = inject('pageTl')
const isEnter = inject('isEnter')

function initTl() {
  if (pageTl) {
    pageTl.add(setOption, 1)
  } else {
    setOption()
  }
}

function setOption() {
  if (isEnter && !isEnter.value) {
    return
  }
  const dataAxis = []
  const data = []
  if (chartData.leftTwo && chartData.leftTwo.length) {
    chartData.leftTwo.forEach(item => {
      dataAxis.push(item.key)
      data.push(item.value)
    })
  }
  chartOption.value = {
    xAxis: {
      data: dataAxis,
      axisLabel: {
        inside: true,
        color: '#fff',
        rotate: 90
      },
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
      z: 10
    },
    yAxis: {
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#999',
      }
    },
    grid: {
      left: '8',
      right: '8',
      top: '30',
      bottom: '8',
      containLabel: true
    },
    series: [
      {
        type: 'bar',
        showBackground: true,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2378f7' },
              { offset: 0.7, color: '#2378f7' },
              { offset: 1, color: '#83bff6' }
            ])
          }
        },
        data: data
      }
    ]
  }
}

onMounted(() => {
  initTl()
})

</script>
<style scoped>
.box {
  width: 500px;
  height: 290px;
  display: flex;
  flex-direction: column;
  background-color: #f5f5d5;
}
</style>
