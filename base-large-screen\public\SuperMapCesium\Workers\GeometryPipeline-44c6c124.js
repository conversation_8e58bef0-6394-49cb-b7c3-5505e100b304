define(["exports","./AttributeCompression-90851096","./Cartesian2-47311507","./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./Math-119be1a3","./buildModuleUrl-8958744c","./Cartesian4-3ca25aab","./ComponentDatatype-c140a87d","./EncodedCartesian3-f1396b05","./GeometryAttribute-06a41648","./IndexDatatype-8a5eead4","./IntersectionTests-a793ed08","./FeatureDetection-806b12f0","./Plane-a3d8b3d2"],(function(e,t,r,a,i,n,s,o,u,p,d,l,v,y,f,c){"use strict";var m=new a.Cartesian3,C=new a.Cartesian3,h=new a.Cartesian3;var b={calculateACMR:function(e){var t=(e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT)).indices,r=e.maximumIndex,a=n.defaultValue(e.cacheSize,24),i=t.length;if(!n.defined(r)){r=0;for(var s=0,o=t[s];s<i;)o>r&&(r=o),o=t[++s]}for(var u=[],p=0;p<r+1;p++)u[p]=0;for(var d=a+1,l=0;l<i;++l)d-u[t[l]]>a&&(u[t[l]]=d,++d);return(d-a+1)/(i/3)}};b.tipsify=function(e){var t,r=(e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT)).indices,a=e.maximumIndex,i=n.defaultValue(e.cacheSize,24);function s(e,r,a,i,n,s,o){for(var u,p=-1,d=-1,l=0;l<a.length;){var v=a[l];i[v].numLiveTriangles&&(u=0,n-i[v].timeStamp+2*i[v].numLiveTriangles<=r&&(u=n-i[v].timeStamp),(u>d||-1===d)&&(d=u,p=v)),++l}return-1===p?function(e,r,a,i){for(;r.length>=1;){var n=r[r.length-1];if(r.splice(r.length-1,1),e[n].numLiveTriangles>0)return n}for(;t<i;){if(e[t].numLiveTriangles>0)return++t-1;++t}return-1}(i,s,0,o):p}var o=r.length,u=0,p=0,d=r[p],l=o;if(n.defined(a))u=a+1;else{for(;p<l;)d>u&&(u=d),d=r[++p];if(-1===u)return 0;++u}var v,y=[];for(v=0;v<u;v++)y[v]={numLiveTriangles:0,timeStamp:0,vertexTriangles:[]};p=0;for(var f=0;p<l;)y[r[p]].vertexTriangles.push(f),++y[r[p]].numLiveTriangles,y[r[p+1]].vertexTriangles.push(f),++y[r[p+1]].numLiveTriangles,y[r[p+2]].vertexTriangles.push(f),++y[r[p+2]].numLiveTriangles,++f,p+=3;var c=0,m=i+1;t=1;var C,h,b,g,A=[],T=[],x=0,P=[],w=o/3,I=[];for(v=0;v<w;v++)I[v]=!1;for(;-1!==c;){A=[],g=(h=y[c]).vertexTriangles.length;for(var S=0;S<g;++S)if(!I[f=h.vertexTriangles[S]]){I[f]=!0,p=f+f+f;for(var O=0;O<3;++O)b=r[p],A.push(b),T.push(b),P[x]=b,++x,--(C=y[b]).numLiveTriangles,m-C.timeStamp>i&&(C.timeStamp=m,++m),++p}c=s(0,i,A,y,m,T,u)}return P};var g={};function A(e,t,r,a,i){e[t++]=r,e[t++]=a,e[t++]=a,e[t++]=i,e[t++]=i,e[t]=r}function T(e){var t={};for(var r in e)if(e.hasOwnProperty(r)&&n.defined(e[r])&&n.defined(e[r].values)){var a=e[r];t[r]=new l.GeometryAttribute({componentDatatype:a.componentDatatype,componentsPerAttribute:a.componentsPerAttribute,normalize:a.normalize,values:[]})}return t}function x(e,t,r){for(var a in t)if(t.hasOwnProperty(a)&&n.defined(t[a])&&n.defined(t[a].values))for(var i=t[a],s=0;s<i.componentsPerAttribute;++s)e[a].values.push(i.values[r*i.componentsPerAttribute+s])}g.toWireframe=function(e){var t=e.indices;if(n.defined(t)){switch(e.primitiveType){case f.PrimitiveType.TRIANGLES:e.indices=function(e){for(var t=e.length,r=t/3*6,a=v.IndexDatatype.createTypedArray(t,r),i=0,n=0;n<t;n+=3,i+=6)A(a,i,e[n],e[n+1],e[n+2]);return a}(t);break;case f.PrimitiveType.TRIANGLE_STRIP:e.indices=function(e){var t=e.length;if(t>=3){var r=6*(t-2),a=v.IndexDatatype.createTypedArray(t,r);A(a,0,e[0],e[1],e[2]);for(var i=6,n=3;n<t;++n,i+=6)A(a,i,e[n-1],e[n],e[n-2]);return a}return new Uint16Array}(t);break;case f.PrimitiveType.TRIANGLE_FAN:e.indices=function(e){if(e.length>0){for(var t=e.length-1,r=6*(t-1),a=v.IndexDatatype.createTypedArray(t,r),i=e[0],n=0,s=1;s<t;++s,n+=6)A(a,n,i,e[s],e[s+1]);return a}return new Uint16Array}(t)}e.primitiveType=f.PrimitiveType.LINES}return e},g.createLineSegmentsForVectors=function(e,t,r){t=n.defaultValue(t,"normal"),r=n.defaultValue(r,1e4);for(var a,i=e.attributes.position.values,s=e.attributes[t].values,u=i.length,d=new Float64Array(2*u),v=0,y=0;y<u;y+=3)d[v++]=i[y],d[v++]=i[y+1],d[v++]=i[y+2],d[v++]=i[y]+s[y]*r,d[v++]=i[y+1]+s[y+1]*r,d[v++]=i[y+2]+s[y+2]*r;var c=e.boundingSphere;return n.defined(c)&&(a=new o.BoundingSphere(c.center,c.radius+r)),new l.Geometry({attributes:{position:new l.GeometryAttribute({componentDatatype:p.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:d})},primitiveType:f.PrimitiveType.LINES,boundingSphere:a})},g.createAttributeLocations=function(e){var t,r=["position","positionHigh","positionLow","position3DHigh","position3DLow","position2DHigh","position2DLow","pickColor","normal","st","tangent","bitangent","extrudeDirection","compressedAttributes"],a=e.attributes,i={},s=0,o=r.length;for(t=0;t<o;++t){var u=r[t];n.defined(a[u])&&(i[u]=s++)}for(var p in a)a.hasOwnProperty(p)&&!n.defined(i[p])&&(i[p]=s++);return i},g.reorderForPreVertexCache=function(e){var t=l.Geometry.computeNumberOfVertices(e),r=e.indices;if(n.defined(r)){for(var a=new Int32Array(t),i=0;i<t;i++)a[i]=-1;for(var s,o=r,u=o.length,d=v.IndexDatatype.createTypedArray(t,u),y=0,f=0,c=0;y<u;)-1!==(s=a[o[y]])?d[f]=s:(a[s=o[y]]=c,d[f]=c,++c),++y,++f;e.indices=d;var m=e.attributes;for(var C in m)if(m.hasOwnProperty(C)&&n.defined(m[C])&&n.defined(m[C].values)){for(var h=m[C],b=h.values,g=0,A=h.componentsPerAttribute,T=p.ComponentDatatype.createTypedArray(h.componentDatatype,c*A);g<t;){var x=a[g];if(-1!==x)for(var P=0;P<A;P++)T[A*x+P]=b[A*g+P];++g}h.values=T}}return e},g.reorderForPostVertexCache=function(e,t){var r=e.indices;if(e.primitiveType===f.PrimitiveType.TRIANGLES&&n.defined(r)){for(var a=r.length,i=0,s=0;s<a;s++)r[s]>i&&(i=r[s]);e.indices=b.tipsify({indices:r,maximumIndex:i,cacheSize:t})}return e},g.fitToUnsignedShortIndices=function(e){var t=[],r=l.Geometry.computeNumberOfVertices(e);if(n.defined(e.indices)&&r>=s.CesiumMath.SIXTY_FOUR_KILOBYTES){var a,i=[],o=[],u=0,p=T(e.attributes),d=e.indices,v=d.length;e.primitiveType===f.PrimitiveType.TRIANGLES?a=3:e.primitiveType===f.PrimitiveType.LINES?a=2:e.primitiveType===f.PrimitiveType.POINTS&&(a=1);for(var y=0;y<v;y+=a){for(var c=0;c<a;++c){var m=d[y+c],C=i[m];n.defined(C)||(C=u++,i[m]=C,x(p,e.attributes,m)),o.push(C)}u+a>=s.CesiumMath.SIXTY_FOUR_KILOBYTES&&(t.push(new l.Geometry({attributes:p,indices:o,primitiveType:e.primitiveType,boundingSphere:e.boundingSphere,boundingSphereCV:e.boundingSphereCV})),i=[],o=[],u=0,p=T(e.attributes))}0!==o.length&&t.push(new l.Geometry({attributes:p,indices:o,primitiveType:e.primitiveType,boundingSphere:e.boundingSphere,boundingSphereCV:e.boundingSphereCV}))}else t.push(e);return t};var P=new a.Cartesian3,w=new a.Cartographic;g.projectTo2D=function(e,t,r,i,s){for(var u=e.attributes[t],d=(s=n.defined(s)?s:new o.GeographicProjection).ellipsoid,v=u.values,y=new Float64Array(v.length),f=0,c=0;c<v.length;c+=3){var m=a.Cartesian3.fromArray(v,c,P),C=d.cartesianToCartographic(m,w),h=s.project(C,P);y[f++]=h.x,y[f++]=h.y,y[f++]=h.z}return e.attributes[r]=u,e.attributes[i]=new l.GeometryAttribute({componentDatatype:p.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:y}),delete e.attributes[t],e};var I={high:0,low:0};g.encodeAttribute=function(e,t,r,a){for(var i=e.attributes[t],n=i.values,s=n.length,o=new Float32Array(s),u=new Float32Array(s),v=0;v<s;++v)d.EncodedCartesian3.encode(n[v],I),o[v]=I.high,u[v]=I.low;var y=i.componentsPerAttribute;return e.attributes[r]=new l.GeometryAttribute({componentDatatype:p.ComponentDatatype.FLOAT,componentsPerAttribute:y,values:o}),e.attributes[a]=new l.GeometryAttribute({componentDatatype:p.ComponentDatatype.FLOAT,componentsPerAttribute:y,values:u}),delete e.attributes[t],e};var S=new a.Cartesian3;function O(e,t){if(n.defined(t))for(var r=t.values,i=r.length,s=0;s<i;s+=3)a.Cartesian3.unpack(r,s,S),f.Matrix4.multiplyByPoint(e,S,S),a.Cartesian3.pack(S,r,s)}function E(e,t){if(n.defined(t))for(var r=t.values,i=r.length,s=0;s<i;s+=3)a.Cartesian3.unpack(r,s,S),f.Matrix3.multiplyByVector(e,S,S),S=a.Cartesian3.normalize(S,S),a.Cartesian3.pack(S,r,s)}var N=new f.Matrix4,L=new f.Matrix3;g.transformToWorldCoordinates=function(e){var t=e.modelMatrix;if(f.Matrix4.equals(t,f.Matrix4.IDENTITY))return e;var r=e.geometry.attributes;O(t,r.position),O(t,r.prevPosition),O(t,r.nextPosition),(n.defined(r.normal)||n.defined(r.tangent)||n.defined(r.bitangent))&&(f.Matrix4.inverse(t,N),f.Matrix4.transpose(N,N),f.Matrix4.getRotation(N,L),E(L,r.normal),E(L,r.tangent),E(L,r.bitangent));var a=e.geometry.boundingSphere;return n.defined(a)&&(e.geometry.boundingSphere=o.BoundingSphere.transform(a,t,a)),e.modelMatrix=f.Matrix4.clone(f.Matrix4.IDENTITY),e};var z=new a.Cartesian3;function D(e,t){var r,i,s,u,d=e.length;e[0].modelMatrix;var y,c,m,C,h=n.defined(e[0][t].indices),b=e[0][t].primitiveType,g=function(e,t){var r,a=e.length,i={},s=e[0][t].attributes;for(r in s)if(s.hasOwnProperty(r)&&n.defined(s[r])&&n.defined(s[r].values)){for(var o=s[r],u=o.values.length,d=!0,v=1;v<a;++v){var y=e[v][t].attributes[r];if(!n.defined(y)||o.componentDatatype!==y.componentDatatype||o.componentsPerAttribute!==y.componentsPerAttribute||o.normalize!==y.normalize){d=!1;break}u+=y.values.length}d&&(i[r]=new l.GeometryAttribute({componentDatatype:o.componentDatatype,componentsPerAttribute:o.componentsPerAttribute,normalize:o.normalize,values:p.ComponentDatatype.createTypedArray(o.componentDatatype,u)}),o.isInstanceAttribute&&(i[r].isInstanceAttribute=!0))}return i}(e,t);for(r in g)if(g.hasOwnProperty(r))for(y=g[r].values,u=0,i=0;i<d;++i)for(m=(c=e[i][t].attributes[r].values).length,s=0;s<m;++s)y[u++]=c[s];if(h){var A=0;for(i=0;i<d;++i)A+=e[i][t].indices.length;var T=l.Geometry.computeNumberOfVertices(new l.Geometry({attributes:g,primitiveType:f.PrimitiveType.POINTS})),x=v.IndexDatatype.createTypedArray(T,A),P=0,w=0;for(i=0;i<d;++i){var I=e[i][t].indices,S=I.length;for(u=0;u<S;++u)x[P++]=w+I[u];w+=l.Geometry.computeNumberOfVertices(e[i][t])}C=x}var O,E=new a.Cartesian3,N=0;for(i=0;i<d;++i){if(O=e[i][t].boundingSphere,!n.defined(O)){E=void 0;break}a.Cartesian3.add(O.center,E,E)}if(n.defined(E))for(a.Cartesian3.divideByScalar(E,d,E),i=0;i<d;++i){O=e[i][t].boundingSphere;var L=a.Cartesian3.magnitude(a.Cartesian3.subtract(O.center,E,z))+O.radius;L>N&&(N=L)}return new l.Geometry({attributes:g,indices:C,primitiveType:b,boundingSphere:n.defined(E)?new o.BoundingSphere(E,N):void 0})}g.combineInstances=function(e){for(var t=[],r=[],a=e.length,i=0;i<a;++i){var s=e[i];n.defined(s.geometry)?t.push(s):n.defined(s.westHemisphereGeometry)&&n.defined(s.eastHemisphereGeometry)&&r.push(s)}var o=[];return t.length>0&&o.push(D(t,"geometry")),r.length>0&&(o.push(D(r,"westHemisphereGeometry")),o.push(D(r,"eastHemisphereGeometry"))),o};var M=new a.Cartesian3,G=new a.Cartesian3,R=new a.Cartesian3,V=new a.Cartesian3;g.computeNormal=function(e){var t,r=e.indices,i=e.attributes,n=i.position.values,o=i.position.values.length/3,u=r.length,d=new Array(o),v=new Array(u/3),y=new Array(u);for(t=0;t<o;t++)d[t]={indexOffset:0,count:0,currentCount:0};var f=0;for(t=0;t<u;t+=3){var c=r[t],m=r[t+1],C=r[t+2],h=3*c,b=3*m,g=3*C;G.x=n[h],G.y=n[h+1],G.z=n[h+2],R.x=n[b],R.y=n[b+1],R.z=n[b+2],V.x=n[g],V.y=n[g+1],V.z=n[g+2],d[c].count++,d[m].count++,d[C].count++,a.Cartesian3.subtract(R,G,R),a.Cartesian3.subtract(V,G,V),v[f]=a.Cartesian3.cross(R,V,new a.Cartesian3),f++}var A,T=0;for(t=0;t<o;t++)d[t].indexOffset+=T,T+=d[t].count;for(f=0,t=0;t<u;t+=3){var x=(A=d[r[t]]).indexOffset+A.currentCount;y[x]=f,A.currentCount++,y[x=(A=d[r[t+1]]).indexOffset+A.currentCount]=f,A.currentCount++,y[x=(A=d[r[t+2]]).indexOffset+A.currentCount]=f,A.currentCount++,f++}var P=new Float32Array(3*o);for(t=0;t<o;t++){var w=3*t;if(A=d[t],a.Cartesian3.clone(a.Cartesian3.ZERO,M),A.count>0){for(f=0;f<A.count;f++)a.Cartesian3.add(M,v[y[A.indexOffset+f]],M);a.Cartesian3.equalsEpsilon(a.Cartesian3.ZERO,M,s.CesiumMath.EPSILON10)&&a.Cartesian3.clone(v[y[A.indexOffset]],M)}a.Cartesian3.equalsEpsilon(a.Cartesian3.ZERO,M,s.CesiumMath.EPSILON10)&&(M.z=1),a.Cartesian3.normalize(M,M),P[w]=M.x,P[w+1]=M.y,P[w+2]=M.z}return e.attributes.normal=new l.GeometryAttribute({componentDatatype:p.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:P}),e};var F=new a.Cartesian3,B=new a.Cartesian3,k=new a.Cartesian3;g.computeTangentAndBitangent=function(e){e.attributes;var t,r,i,n,s=e.indices,o=e.attributes.position.values,u=e.attributes.normal.values,d=e.attributes.st.values,v=e.attributes.position.values.length/3,y=s.length,f=new Array(3*v);for(t=0;t<f.length;t++)f[t]=0;for(t=0;t<y;t+=3){var c=s[t],m=s[t+1],C=s[t+2];i=3*m,n=3*C;var h=2*c,b=2*m,g=2*C,A=o[r=3*c],T=o[r+1],x=o[r+2],P=d[h],w=d[h+1],I=d[b+1]-w,S=d[g+1]-w,O=1/((d[b]-P)*S-(d[g]-P)*I),E=(S*(o[i]-A)-I*(o[n]-A))*O,N=(S*(o[i+1]-T)-I*(o[n+1]-T))*O,L=(S*(o[i+2]-x)-I*(o[n+2]-x))*O;f[r]+=E,f[r+1]+=N,f[r+2]+=L,f[i]+=E,f[i+1]+=N,f[i+2]+=L,f[n]+=E,f[n+1]+=N,f[n+2]+=L}var z=new Float32Array(3*v),D=new Float32Array(3*v);for(t=0;t<v;t++){i=(r=3*t)+1,n=r+2;var M=a.Cartesian3.fromArray(u,r,F),G=a.Cartesian3.fromArray(f,r,k),R=a.Cartesian3.dot(M,G);a.Cartesian3.multiplyByScalar(M,R,B),a.Cartesian3.normalize(a.Cartesian3.subtract(G,B,G),G),z[r]=G.x,z[i]=G.y,z[n]=G.z,a.Cartesian3.normalize(a.Cartesian3.cross(M,G,G),G),D[r]=G.x,D[i]=G.y,D[n]=G.z}return e.attributes.tangent=new l.GeometryAttribute({componentDatatype:p.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:z}),e.attributes.bitangent=new l.GeometryAttribute({componentDatatype:p.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:D}),e};var _=new r.Cartesian2,U=new a.Cartesian3,q=new a.Cartesian3,Y=new a.Cartesian3,Z=new r.Cartesian2;function H(e){switch(e.primitiveType){case f.PrimitiveType.TRIANGLE_FAN:return function(e){var t=l.Geometry.computeNumberOfVertices(e),r=v.IndexDatatype.createTypedArray(t,3*(t-2));r[0]=1,r[1]=0,r[2]=2;for(var a=3,i=3;i<t;++i)r[a++]=i-1,r[a++]=0,r[a++]=i;return e.indices=r,e.primitiveType=f.PrimitiveType.TRIANGLES,e}(e);case f.PrimitiveType.TRIANGLE_STRIP:return function(e){var t=l.Geometry.computeNumberOfVertices(e),r=v.IndexDatatype.createTypedArray(t,3*(t-2));r[0]=0,r[1]=1,r[2]=2,t>3&&(r[3]=0,r[4]=2,r[5]=3);for(var a=6,i=3;i<t-1;i+=2)r[a++]=i,r[a++]=i-1,r[a++]=i+1,i+2<t&&(r[a++]=i,r[a++]=i+1,r[a++]=i+2);return e.indices=r,e.primitiveType=f.PrimitiveType.TRIANGLES,e}(e);case f.PrimitiveType.TRIANGLES:return function(e){if(n.defined(e.indices))return e;for(var t=l.Geometry.computeNumberOfVertices(e),r=v.IndexDatatype.createTypedArray(t,t),a=0;a<t;++a)r[a]=a;return e.indices=r,e}(e);case f.PrimitiveType.LINE_STRIP:return function(e){var t=l.Geometry.computeNumberOfVertices(e),r=v.IndexDatatype.createTypedArray(t,2*(t-1));r[0]=0,r[1]=1;for(var a=2,i=2;i<t;++i)r[a++]=i-1,r[a++]=i;return e.indices=r,e.primitiveType=f.PrimitiveType.LINES,e}(e);case f.PrimitiveType.LINE_LOOP:return function(e){var t=l.Geometry.computeNumberOfVertices(e),r=v.IndexDatatype.createTypedArray(t,2*t);r[0]=0,r[1]=1;for(var a=2,i=2;i<t;++i)r[a++]=i-1,r[a++]=i;return r[a++]=t-1,r[a]=0,e.indices=r,e.primitiveType=f.PrimitiveType.LINES,e}(e);case f.PrimitiveType.LINES:return function(e){if(n.defined(e.indices))return e;for(var t=l.Geometry.computeNumberOfVertices(e),r=v.IndexDatatype.createTypedArray(t,t),a=0;a<t;++a)r[a]=a;return e.indices=r,e}(e)}return e}function W(e,t){Math.abs(e.y)<s.CesiumMath.EPSILON6&&(e.y=t?-s.CesiumMath.EPSILON6:s.CesiumMath.EPSILON6)}g.compressVertices=function(e){var i,s,o=e.attributes.extrudeDirection;if(n.defined(o)){var u=o.values;s=u.length/3;var d=new Float32Array(2*s),v=0;for(i=0;i<s;++i)a.Cartesian3.fromArray(u,3*i,U),a.Cartesian3.equals(U,a.Cartesian3.ZERO)?v+=2:(Z=t.AttributeCompression.octEncodeInRange(U,65535,Z),d[v++]=Z.x,d[v++]=Z.y);return e.attributes.compressedAttributes=new l.GeometryAttribute({componentDatatype:p.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:d}),delete e.attributes.extrudeDirection,e}var y=e.attributes.normal,f=e.attributes.st,c=n.defined(y),m=n.defined(f);if(!c&&!m)return e;var C,h,b,g,A=e.attributes.tangent,T=e.attributes.bitangent,x=n.defined(A),P=n.defined(T);c&&(C=y.values),m&&(h=f.values),x&&(b=A.values),P&&(g=T.values);var w=s=(c?C.length:h.length)/(c?3:2),I=m&&c?2:1;I+=x||P?1:0;var S=new Float32Array(w*=I),O=0;for(i=0;i<s;++i){m&&(r.Cartesian2.fromArray(h,2*i,_),S[O++]=t.AttributeCompression.compressTextureCoordinates(_));var E=3*i;c&&n.defined(b)&&n.defined(g)?(a.Cartesian3.fromArray(C,E,U),a.Cartesian3.fromArray(b,E,q),a.Cartesian3.fromArray(g,E,Y),t.AttributeCompression.octPack(U,q,Y,_),S[O++]=_.x,S[O++]=_.y):(c&&(a.Cartesian3.fromArray(C,E,U),S[O++]=t.AttributeCompression.octEncodeFloat(U)),x&&(a.Cartesian3.fromArray(b,E,U),S[O++]=t.AttributeCompression.octEncodeFloat(U)),P&&(a.Cartesian3.fromArray(g,E,U),S[O++]=t.AttributeCompression.octEncodeFloat(U)))}return e.attributes.compressedAttributes=new l.GeometryAttribute({componentDatatype:p.ComponentDatatype.FLOAT,componentsPerAttribute:I,values:S}),c&&delete e.attributes.normal,m&&delete e.attributes.st,P&&delete e.attributes.bitangent,x&&delete e.attributes.tangent,e};var X=new a.Cartesian3;function j(e,t,r,i){a.Cartesian3.add(e,a.Cartesian3.multiplyByScalar(a.Cartesian3.subtract(t,e,X),e.y/(e.y-t.y),X),r),a.Cartesian3.clone(r,i),W(r,!0),W(i,!1)}var J=new a.Cartesian3,K=new a.Cartesian3,Q=new a.Cartesian3,$=new a.Cartesian3,ee={positions:new Array(7),indices:new Array(9)};function te(e,t,r){if(!(e.x>=0||t.x>=0||r.x>=0)){!function(e,t,r){if(0!==e.y&&0!==t.y&&0!==r.y)return W(e,e.y<0),W(t,t.y<0),void W(r,r.y<0);var a=Math.abs(e.y),i=Math.abs(t.y),n=Math.abs(r.y),o=(a>i?a>n?s.CesiumMath.sign(e.y):s.CesiumMath.sign(r.y):i>n?s.CesiumMath.sign(t.y):s.CesiumMath.sign(r.y))<0;W(e,o),W(t,o),W(r,o)}(e,t,r);var a=e.y<0,i=t.y<0,n=r.y<0,o=0;o+=a?1:0,o+=i?1:0;var u=ee.indices;1===(o+=n?1:0)?(u[1]=3,u[2]=4,u[5]=6,u[7]=6,u[8]=5,a?(j(e,t,J,Q),j(e,r,K,$),u[0]=0,u[3]=1,u[4]=2,u[6]=1):i?(j(t,r,J,Q),j(t,e,K,$),u[0]=1,u[3]=2,u[4]=0,u[6]=2):n&&(j(r,e,J,Q),j(r,t,K,$),u[0]=2,u[3]=0,u[4]=1,u[6]=0)):2===o&&(u[2]=4,u[4]=4,u[5]=3,u[7]=5,u[8]=6,a?i?n||(j(r,e,J,Q),j(r,t,K,$),u[0]=0,u[1]=1,u[3]=0,u[6]=2):(j(t,r,J,Q),j(t,e,K,$),u[0]=2,u[1]=0,u[3]=2,u[6]=1):(j(e,t,J,Q),j(e,r,K,$),u[0]=1,u[1]=2,u[3]=1,u[6]=0));var p=ee.positions;return p[0]=e,p[1]=t,p[2]=r,p.length=3,1!==o&&2!==o||(p[3]=J,p[4]=K,p[5]=Q,p[6]=$,p.length=7),ee}}function re(e,t){var r=e.attributes;if(0!==r.position.values.length){for(var a in r)if(r.hasOwnProperty(a)&&n.defined(r[a])&&n.defined(r[a].values)){var i=r[a];i.values=p.ComponentDatatype.createTypedArray(i.componentDatatype,i.values)}var s=l.Geometry.computeNumberOfVertices(e);return e.indices=v.IndexDatatype.createTypedArray(s,e.indices),t&&(e.boundingSphere=o.BoundingSphere.fromVertices(r.position.values)),e}}function ae(e){var t=e.attributes,r={};for(var a in t)if(t.hasOwnProperty(a)&&n.defined(t[a])&&n.defined(t[a].values)){var i=t[a];r[a]=new l.GeometryAttribute({componentDatatype:i.componentDatatype,componentsPerAttribute:i.componentsPerAttribute,normalize:i.normalize,values:[]})}return new l.Geometry({attributes:r,indices:[],primitiveType:e.primitiveType})}function ie(e,t,r){var a=n.defined(e.geometry.boundingSphere);t=re(t,a),r=re(r,a),n.defined(r)&&!n.defined(t)?e.geometry=r:!n.defined(r)&&n.defined(t)?e.geometry=t:(e.westHemisphereGeometry=t,e.eastHemisphereGeometry=r,e.geometry=void 0)}function ne(e,t){var r=new e,a=new e,i=new e;return function(n,s,o,u,p,d,l,v){var y=e.fromArray(p,n*t,r),f=e.fromArray(p,s*t,a),c=e.fromArray(p,o*t,i);e.multiplyByScalar(y,u.x,y),e.multiplyByScalar(f,u.y,f),e.multiplyByScalar(c,u.z,c);var m=e.add(y,f,y);e.add(m,c,m),v&&e.normalize(m,m),e.pack(m,d,l*t)}}var se=ne(u.Cartesian4,4),oe=ne(a.Cartesian3,3),ue=ne(r.Cartesian2,2),pe=new a.Cartesian3,de=new a.Cartesian3,le=new a.Cartesian3,ve=new a.Cartesian3;function ye(e,t,i,o,u,p,d,l,v,y,f,c,b,g,A,T){if(n.defined(p)||n.defined(d)||n.defined(l)||n.defined(v)||n.defined(y)||0!==g){var x=function(e,t,i,o,u){var p,d,l,v,y,f,c,b;if(n.defined(u)||(u=new a.Cartesian3),n.defined(t.z)){if(a.Cartesian3.equalsEpsilon(e,t,s.CesiumMath.EPSILON14))return a.Cartesian3.clone(a.Cartesian3.UNIT_X,u);if(a.Cartesian3.equalsEpsilon(e,i,s.CesiumMath.EPSILON14))return a.Cartesian3.clone(a.Cartesian3.UNIT_Y,u);if(a.Cartesian3.equalsEpsilon(e,o,s.CesiumMath.EPSILON14))return a.Cartesian3.clone(a.Cartesian3.UNIT_Z,u);p=a.Cartesian3.subtract(i,t,m),d=a.Cartesian3.subtract(o,t,C),l=a.Cartesian3.subtract(e,t,h),v=a.Cartesian3.dot(p,p),y=a.Cartesian3.dot(p,d),f=a.Cartesian3.dot(p,l),c=a.Cartesian3.dot(d,d),b=a.Cartesian3.dot(d,l)}else{if(r.Cartesian2.equalsEpsilon(e,t,s.CesiumMath.EPSILON14))return a.Cartesian3.clone(a.Cartesian3.UNIT_X,u);if(r.Cartesian2.equalsEpsilon(e,i,s.CesiumMath.EPSILON14))return a.Cartesian3.clone(a.Cartesian3.UNIT_Y,u);if(r.Cartesian2.equalsEpsilon(e,o,s.CesiumMath.EPSILON14))return a.Cartesian3.clone(a.Cartesian3.UNIT_Z,u);p=r.Cartesian2.subtract(i,t,m),d=r.Cartesian2.subtract(o,t,C),l=r.Cartesian2.subtract(e,t,h),v=r.Cartesian2.dot(p,p),y=r.Cartesian2.dot(p,d),f=r.Cartesian2.dot(p,l),c=r.Cartesian2.dot(d,d),b=r.Cartesian2.dot(d,l)}u.y=c*f-y*b,u.z=v*b-y*f;var g=v*c-y*y;return 0!==u.y&&(u.y/=g),0!==u.z&&(u.z/=g),u.x=1-u.y-u.z,u}(o,a.Cartesian3.fromArray(u,3*e,pe),a.Cartesian3.fromArray(u,3*t,de),a.Cartesian3.fromArray(u,3*i,le),ve);if(n.defined(p)&&oe(e,t,i,x,p,c.normal.values,T,!0),n.defined(y)){var P,w=a.Cartesian3.fromArray(y,3*e,pe),I=a.Cartesian3.fromArray(y,3*t,de),S=a.Cartesian3.fromArray(y,3*i,le);a.Cartesian3.multiplyByScalar(w,x.x,w),a.Cartesian3.multiplyByScalar(I,x.y,I),a.Cartesian3.multiplyByScalar(S,x.z,S),a.Cartesian3.equals(w,a.Cartesian3.ZERO)&&a.Cartesian3.equals(I,a.Cartesian3.ZERO)&&a.Cartesian3.equals(S,a.Cartesian3.ZERO)?((P=pe).x=0,P.y=0,P.z=0):(P=a.Cartesian3.add(w,I,w),a.Cartesian3.add(P,S,P),a.Cartesian3.normalize(P,P)),a.Cartesian3.pack(P,c.extrudeDirection.values,3*T)}if(n.defined(f)&&function(e,t,r,a,i,n,o){var u=i[e]*a.x,p=i[t]*a.y,d=i[r]*a.z;n[o]=u+p+d>s.CesiumMath.EPSILON6?1:0}(e,t,i,x,f,c.applyOffset.values,T),n.defined(d)&&oe(e,t,i,x,d,c.tangent.values,T,!0),n.defined(l)&&oe(e,t,i,x,l,c.bitangent.values,T,!0),n.defined(v)&&ue(e,t,i,x,v,c.st.values,T),g>0)for(var O=0;O<g;O++){var E=b[O];fe(e,t,i,x,T,A[E],c[E])}}}function fe(e,t,r,a,i,n,s){var o=n.componentsPerAttribute,u=n.values,p=s.values;switch(o){case 4:se(e,t,r,a,u,p,i,!1);break;case 3:oe(e,t,r,a,u,p,i,!1);break;case 2:ue(e,t,r,a,u,p,i,!1);break;default:p[i]=u[e]*a.x+u[t]*a.y+u[r]*a.z}}function ce(e,t,r,a,i,n){var s=e.position.values.length/3;if(-1!==i){var o=a[i],u=r[o];return-1===u?(r[o]=s,e.position.values.push(n.x,n.y,n.z),t.push(s),s):(t.push(u),u)}return e.position.values.push(n.x,n.y,n.z),t.push(s),s}var me={position:!0,normal:!0,bitangent:!0,tangent:!0,st:!0,extrudeDirection:!0,applyOffset:!0};function Ce(e){var t=e.geometry,r=t.attributes,i=r.position.values,s=n.defined(r.normal)?r.normal.values:void 0,o=n.defined(r.bitangent)?r.bitangent.values:void 0,u=n.defined(r.tangent)?r.tangent.values:void 0,p=n.defined(r.st)?r.st.values:void 0,d=n.defined(r.extrudeDirection)?r.extrudeDirection.values:void 0,l=n.defined(r.applyOffset)?r.applyOffset.values:void 0,v=t.indices,y=[];for(var f in r)r.hasOwnProperty(f)&&!me[f]&&n.defined(r[f])&&y.push(f);var c,m,C,h,b=y.length,g=ae(t),A=ae(t),T=[];T.length=i.length/3;var x=[];for(x.length=i.length/3,h=0;h<T.length;++h)T[h]=-1,x[h]=-1;var P=v.length;for(h=0;h<P;h+=3){var w=v[h],I=v[h+1],S=v[h+2],O=a.Cartesian3.fromArray(i,3*w),E=a.Cartesian3.fromArray(i,3*I),N=a.Cartesian3.fromArray(i,3*S),L=te(O,E,N);if(n.defined(L)&&L.positions.length>3)for(var z=L.positions,D=L.indices,M=D.length,G=0;G<M;++G){var R=D[G],V=z[R];V.y<0?(c=A.attributes,m=A.indices,C=T):(c=g.attributes,m=g.indices,C=x),ye(w,I,S,V,i,s,u,o,p,d,l,c,y,b,r,ce(c,m,C,v,R<3?h+R:-1,V))}else n.defined(L)&&(O=L.positions[0],E=L.positions[1],N=L.positions[2]),O.y<0?(c=A.attributes,m=A.indices,C=T):(c=g.attributes,m=g.indices,C=x),ye(w,I,S,O,i,s,u,o,p,d,l,c,y,b,r,ce(c,m,C,v,h,O)),ye(w,I,S,E,i,s,u,o,p,d,l,c,y,b,r,ce(c,m,C,v,h+1,E)),ye(w,I,S,N,i,s,u,o,p,d,l,c,y,b,r,ce(c,m,C,v,h+2,N))}ie(e,A,g)}var he=c.Plane.fromPointNormal(a.Cartesian3.ZERO,a.Cartesian3.UNIT_Y),be=new a.Cartesian3,ge=new a.Cartesian3;function Ae(e,t,r,i,o,u,p){if(n.defined(p)){var d=a.Cartesian3.fromArray(i,3*e,pe);a.Cartesian3.equalsEpsilon(d,r,s.CesiumMath.EPSILON10)?u.applyOffset.values[o]=p[e]:u.applyOffset.values[o]=p[t]}}function Te(e){var t,r=e.geometry,i=r.attributes,o=i.position.values,u=n.defined(i.applyOffset)?i.applyOffset.values:void 0,p=r.indices,d=ae(r),l=ae(r),v=p.length,f=[];f.length=o.length/3;var c=[];for(c.length=o.length/3,t=0;t<f.length;++t)f[t]=-1,c[t]=-1;for(t=0;t<v;t+=2){var m=p[t],C=p[t+1],h=a.Cartesian3.fromArray(o,3*m,pe),b=a.Cartesian3.fromArray(o,3*C,de);Math.abs(h.y)<s.CesiumMath.EPSILON6&&(h.y<0?h.y=-s.CesiumMath.EPSILON6:h.y=s.CesiumMath.EPSILON6),Math.abs(b.y)<s.CesiumMath.EPSILON6&&(b.y<0?b.y=-s.CesiumMath.EPSILON6:b.y=s.CesiumMath.EPSILON6);var g=d.attributes,A=d.indices,T=c,x=l.attributes,P=l.indices,w=f,I=y.IntersectionTests.lineSegmentPlane(h,b,he,le);if(n.defined(I)){var S=a.Cartesian3.multiplyByScalar(a.Cartesian3.UNIT_Y,5*s.CesiumMath.EPSILON9,be);h.y<0&&(a.Cartesian3.negate(S,S),g=l.attributes,A=l.indices,T=f,x=d.attributes,P=d.indices,w=c);var O=a.Cartesian3.add(I,S,ge);Ae(m,C,h,o,ce(g,A,T,p,t,h),g,u),Ae(m,C,O,o,ce(g,A,T,p,-1,O),g,u),a.Cartesian3.negate(S,S),a.Cartesian3.add(I,S,O),Ae(m,C,O,o,ce(x,P,w,p,-1,O),x,u),Ae(m,C,b,o,ce(x,P,w,p,t+1,b),x,u)}else{var E,N,L;h.y<0?(E=l.attributes,N=l.indices,L=f):(E=d.attributes,N=d.indices,L=c),Ae(m,C,h,o,ce(E,N,L,p,t,h),E,u),Ae(m,C,b,o,ce(E,N,L,p,t+1,b),E,u)}}ie(e,l,d)}var xe=new r.Cartesian2,Pe=new r.Cartesian2,we=new a.Cartesian3,Ie=new a.Cartesian3,Se=new a.Cartesian3,Oe=new a.Cartesian3,Ee=new a.Cartesian3,Ne=new a.Cartesian3,Le=new a.Cartesian3,ze=new u.Cartesian4;function De(e){for(var t=e.attributes,r=t.position.values,i=t.prevPosition.values,n=t.nextPosition.values,s=r.length,o=0;o<s;o+=3){var u=a.Cartesian3.unpack(r,o,we);if(!(u.x>0)){var p=a.Cartesian3.unpack(i,o,Ie);(u.y<0&&p.y>0||u.y>0&&p.y<0)&&(o-3>0?(i[o]=r[o-3],i[o+1]=r[o-2],i[o+2]=r[o-1]):a.Cartesian3.pack(u,i,o));var d=a.Cartesian3.unpack(n,o,Se);(u.y<0&&d.y>0||u.y>0&&d.y<0)&&(o+3<s?(n[o]=r[o+3],n[o+1]=r[o+4],n[o+2]=r[o+5]):a.Cartesian3.pack(u,n,o))}}}var Me=5*s.CesiumMath.EPSILON9,Ge=s.CesiumMath.EPSILON6;g.splitLongitude=function(e){var t=e.geometry,i=t.boundingSphere;if(n.defined(i)&&(i.center.x-i.radius>0||o.BoundingSphere.intersectPlane(i,c.Plane.ORIGIN_ZX_PLANE)!==o.Intersect.INTERSECTING))return e;if(t.geometryType!==l.GeometryType.NONE)switch(t.geometryType){case l.GeometryType.POLYLINES:!function(e){var t,i,o,p=e.geometry,d=p.attributes,l=d.position.values,v=d.prevPosition.values,f=d.nextPosition.values,c=d.expandAndWidth.values,m=n.defined(d.st)?d.st.values:void 0,C=n.defined(d.color)?d.color.values:void 0,h=n.defined(d.dist)?d.dist.values:void 0,b=ae(p),g=ae(p),A=!1,T=l.length/3;for(t=0;t<T;t+=4){var x=t,P=t+2,w=a.Cartesian3.fromArray(l,3*x,we),I=a.Cartesian3.fromArray(l,3*P,Ie);if(Math.abs(w.y)<Ge)for(w.y=Ge*(I.y<0?-1:1),l[3*t+1]=w.y,l[3*(t+1)+1]=w.y,i=3*x;i<3*x+12;i+=3)v[i]=l[3*t],v[i+1]=l[3*t+1],v[i+2]=l[3*t+2];if(Math.abs(I.y)<Ge)for(I.y=Ge*(w.y<0?-1:1),l[3*(t+2)+1]=I.y,l[3*(t+3)+1]=I.y,i=3*x;i<3*x+12;i+=3)f[i]=l[3*(t+2)],f[i+1]=l[3*(t+2)+1],f[i+2]=l[3*(t+2)+2];var S=b.attributes,O=b.indices,E=g.attributes,N=g.indices,L=y.IntersectionTests.lineSegmentPlane(w,I,he,Oe);if(n.defined(L)){A=!0;var z=a.Cartesian3.multiplyByScalar(a.Cartesian3.UNIT_Y,Me,Ee);w.y<0&&(a.Cartesian3.negate(z,z),S=g.attributes,O=g.indices,E=b.attributes,N=b.indices);var D=a.Cartesian3.add(L,z,Ne);S.position.values.push(w.x,w.y,w.z,w.x,w.y,w.z),S.position.values.push(D.x,D.y,D.z),S.position.values.push(D.x,D.y,D.z),S.prevPosition.values.push(v[3*x],v[3*x+1],v[3*x+2]),S.prevPosition.values.push(v[3*x+3],v[3*x+4],v[3*x+5]),S.prevPosition.values.push(w.x,w.y,w.z,w.x,w.y,w.z),S.nextPosition.values.push(D.x,D.y,D.z),S.nextPosition.values.push(D.x,D.y,D.z),S.nextPosition.values.push(D.x,D.y,D.z),S.nextPosition.values.push(D.x,D.y,D.z),a.Cartesian3.negate(z,z),a.Cartesian3.add(L,z,D),E.position.values.push(D.x,D.y,D.z),E.position.values.push(D.x,D.y,D.z),E.position.values.push(I.x,I.y,I.z,I.x,I.y,I.z),E.prevPosition.values.push(D.x,D.y,D.z),E.prevPosition.values.push(D.x,D.y,D.z),E.prevPosition.values.push(D.x,D.y,D.z),E.prevPosition.values.push(D.x,D.y,D.z),E.nextPosition.values.push(I.x,I.y,I.z,I.x,I.y,I.z),E.nextPosition.values.push(f[3*P],f[3*P+1],f[3*P+2]),E.nextPosition.values.push(f[3*P+3],f[3*P+4],f[3*P+5]);var M=r.Cartesian2.fromArray(c,2*x,xe),G=Math.abs(M.y);S.expandAndWidth.values.push(-1,G,1,G),S.expandAndWidth.values.push(-1,-G,1,-G),E.expandAndWidth.values.push(-1,G,1,G),E.expandAndWidth.values.push(-1,-G,1,-G);var R=a.Cartesian3.magnitudeSquared(a.Cartesian3.subtract(L,w,Se));if(R/=a.Cartesian3.magnitudeSquared(a.Cartesian3.subtract(I,w,Se)),n.defined(C)){var V=u.Cartesian4.fromArray(C,4*x,ze),F=u.Cartesian4.fromArray(C,4*P,ze),B=s.CesiumMath.lerp(V.x,F.x,R),k=s.CesiumMath.lerp(V.y,F.y,R),_=s.CesiumMath.lerp(V.z,F.z,R),U=s.CesiumMath.lerp(V.w,F.w,R);for(i=4*x;i<4*x+8;++i)S.color.values.push(C[i]);for(S.color.values.push(B,k,_,U),S.color.values.push(B,k,_,U),E.color.values.push(B,k,_,U),E.color.values.push(B,k,_,U),i=4*P;i<4*P+8;++i)E.color.values.push(C[i])}if(n.defined(m)){var q=r.Cartesian2.fromArray(m,2*x,xe),Y=r.Cartesian2.fromArray(m,2*(t+3),Pe),Z=s.CesiumMath.lerp(q.x,Y.x,R);for(i=2*x;i<2*x+4;++i)S.st.values.push(m[i]);for(S.st.values.push(Z,q.y),S.st.values.push(Z,Y.y),E.st.values.push(Z,q.y),E.st.values.push(Z,Y.y),i=2*P;i<2*P+4;++i)E.st.values.push(m[i])}if(n.defined(h)){var H=a.Cartesian3.fromArray(h,3*x,Le),W=a.Cartesian3.fromArray(h,3*P,Le),X=s.CesiumMath.lerp(H.x,W.x,R);for(i=3*x;i<3*x+6;++i)S.dist.values.push(h[i]);for(S.dist.values.push(X,H.y,H.z),S.dist.values.push(X,H.y,H.z),E.dist.values.push(X,W.y,W.z),E.dist.values.push(X,W.y,W.z),i=3*P;i<3*P+6;++i)E.dist.values.push(h[i])}o=S.position.values.length/3-4,O.push(o,o+2,o+1),O.push(o+1,o+2,o+3),o=E.position.values.length/3-4,N.push(o,o+2,o+1),N.push(o+1,o+2,o+3)}else{var j,J;for(w.y<0?(j=g.attributes,J=g.indices):(j=b.attributes,J=b.indices),j.position.values.push(w.x,w.y,w.z),j.position.values.push(w.x,w.y,w.z),j.position.values.push(I.x,I.y,I.z),j.position.values.push(I.x,I.y,I.z),i=3*t;i<3*t+12;++i)j.prevPosition.values.push(v[i]),j.nextPosition.values.push(f[i]);for(i=2*t;i<2*t+8;++i)j.expandAndWidth.values.push(c[i]),n.defined(m)&&j.st.values.push(m[i]);if(n.defined(C))for(i=4*t;i<4*t+16;++i)j.color.values.push(C[i]);if(n.defined(h))for(i=3*t;i<3*t+12;++i)j.dist.values.push(h[i]);o=j.position.values.length/3-4,J.push(o,o+2,o+1),J.push(o+1,o+2,o+3)}}A&&(De(g),De(b)),ie(e,g,b)}(e);break;case l.GeometryType.TRIANGLES:Ce(e);break;case l.GeometryType.LINES:Te(e)}else H(t),t.primitiveType===f.PrimitiveType.TRIANGLES?Ce(e):t.primitiveType===f.PrimitiveType.LINES&&Te(e);return e},e.GeometryPipeline=g}));
