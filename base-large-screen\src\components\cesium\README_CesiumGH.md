# CesiumGH 组件

基于原有 Cesium 组件的增强版本，集成了 gh.js 的功能特性，提供更丰富的地图交互和可视化效果。

## 功能特性

### 场景设置
- **2D/3D切换**: 支持在2D和3D视图之间切换
- **光照控制**: 启用/禁用全球光照效果
- **大气效果**: 控制大气层显示
- **雾效**: 添加雾化效果增强视觉体验
- **阴影**: 启用/禁用阴影渲染

### 图层管理
- **瓦片坐标**: 显示瓦片坐标系统，便于调试
- **网格显示**: 添加网格覆盖层
- **地球透明**: 设置地球透明度
- **地下模式**: 启用地下视图模式

### 交互功能
- **高级拾取**: 增强的位置和信息拾取功能
- **自定义视角**: 快速设置预定义视角
- **测量工具**: 距离和面积测量（待实现）
- **功能清除**: 一键清除所有GH增强功能

## 使用方法

### 基本使用

```vue
<template>
  <CesiumGH :showGHControls="true" />
</template>

<script setup>
import CesiumGH from './components/cesium/CesiumGH.vue'
</script>
```

### 高级使用

```vue
<template>
  <CesiumGH 
    ref="cesiumGHRef"
    :showGHControls="showControls"
  />
</template>

<script setup>
import { ref } from 'vue'
import CesiumGH from './components/cesium/CesiumGH.vue'

const cesiumGHRef = ref(null)
const showControls = ref(true)

// 程序化控制
function enableFogEffect() {
  cesiumGHRef.value.toggleFog()
}

function setCustomView() {
  cesiumGHRef.value.setCustomView()
}
</script>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| showGHControls | Boolean | true | 是否显示GH功能控制面板 |

## 暴露的方法

| 方法名 | 说明 |
|--------|------|
| toggleSceneMode | 切换2D/3D模式 |
| toggleLighting | 切换光照效果 |
| toggleAtmosphere | 切换大气效果 |
| toggleFog | 切换雾效 |
| toggleShadows | 切换阴影 |
| addTileCoordinates | 添加瓦片坐标显示 |
| addGrid | 添加网格显示 |
| toggleGlobeTranslucency | 切换地球透明度 |
| toggleUnderground | 切换地下模式 |
| enableAdvancedPicking | 启用高级拾取功能 |
| setCustomView | 设置自定义视角 |
| enableMeasurement | 启用测量工具 |
| clearGHFeatures | 清除所有GH功能 |

## 与原组件的区别

1. **保持兼容性**: 基于原有 `index.vue` 组件，保持所有原有功能
2. **功能增强**: 添加了 gh.js 中的高级功能
3. **独立性**: 作为新组件，不影响原有组件的使用
4. **可配置**: 通过 props 控制功能显示

## 技术实现

- 基于 Cesium.js 原生 API 实现 gh.js 功能
- 使用 Vue 3 Composition API
- 支持响应式状态管理
- 集成原有的地图配置和样式系统

## 注意事项

1. 确保 Cesium 库已正确加载
2. 某些功能可能需要特定的 Cesium 版本支持
3. 在使用高级功能时注意性能影响
4. 建议在生产环境中根据需要选择性启用功能

## 示例

参考 `CesiumGHExample.vue` 文件查看完整的使用示例。
