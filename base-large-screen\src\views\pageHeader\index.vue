<template>
  <LayoutBox>
    <div class="header">
      <img class="logo" src="../../assets/images/logo.png" alt="icon">
      <Time />
      <div style="flex: 1;"></div>
      <RouteButton name="Chart">Echarts 图表</RouteButton>
      <RouteButton name="Earth">3D地球</RouteButton>
      <RouteButton name="Wide">超宽屏Demo</RouteButton>
      <RouteButton name="Ol">Openlayers</RouteButton>
      <RouteButton name="Cesium">Cesium</RouteButton>
      <Weather />
    </div>
  </LayoutBox>
</template>
<script setup>
import { onMounted } from "vue";
import RouteButton from "./RouteButton.vue"
import Time from "../../components/common/Time.vue"
import Weather from "../../components/common/Weather.vue"

</script>
<style scoped>
.header {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 80px;
  background-color: #eff5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, .1);
  padding-left: 100px;
  padding-right: 100px;
}

.logo {
  width: 70px;
  height: 70px;
}
</style>
