<template>
  <div class="title-box">
    <img v-if="props.icon" class="title-icon" :src="props.icon" alt="icon">
    <AnimationText class="title-text" :text="props.title" :position="props.position" />
    <div class="blank"></div>
    <slot />
  </div>
</template>
<script setup>
import { onMounted } from "vue";
import AnimationText from "./AnimationText.vue"

const props = defineProps({
  title: {
    type: String
  },
  icon: {
    type: String
  },
  position: {
    type: Number
  }
})

onMounted(() => {

})
</script>
<style scoped>
.title-box {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 3px 10px;
  border-left: 6px solid #019fde;
  background: linear-gradient(to right, #019fde47, #019fde2b)
}

.title-icon {
  width: 25px;
  height: 25px;
  flex-shrink: 0;
  margin-right: 10px;
}

.title-text {
  font-size: 20px;
  font-weight: bold;
  flex-shrink: 0;
}

.blank {
  width: 0;
  height: 0;
  flex: 1;
}
</style>
