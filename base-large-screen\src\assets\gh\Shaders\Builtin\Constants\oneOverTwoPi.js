//This file is automatically rebuilt by the Cesium build process.
export default "/**\n\
 * A built-in GLSL floating-point constant for <code>1/2pi</code>.\n\
 *\n\
 * @alias czm_oneOverTwoPi\n\
 * @glslConstant\n\
 *\n\
 * @see CesiumMath.ONE_OVER_TWO_PI\n\
 *\n\
 * @example\n\
 * // GLSL declaration\n\
 * const float czm_oneOverTwoPi = ...;\n\
 *\n\
 * // Example\n\
 * float pi = 2.0 * czm_oneOverTwoPi;\n\
 */\n\
const float czm_oneOverTwoPi = 0.15915494309189535;\n\
";
