import { defineStore } from "pinia";
import { ref } from "vue";
import { useMapStore } from "./map";
import { getGrainData } from "../../api";
import mapLabel from "../../assets/images/map_label.png";
import mapDot1 from "../../assets/images/map_dot.png";
import mapDot2 from "../../assets/images/map_dot2.png";

const PositionMap = {
  黑龙江: [850.1059, 688.7785],
  吉林: [835.9024, 639.6125],
  辽宁: [791.1067, 609.0204],
  北京: [697.1451, 598.0946],
  天津: [713.5337, 581.706],
  山东: [722.2744, 540.188],
  江苏: [757.2368, 493.2072],
  上海: [805.3102, 456.0596],
  浙江: [779.2509, 430.6005],
  福建: [749.458, 377.5867],
  台湾: [812.1107, 337.2786],
  广东: [670.5944, 335.5261],
  海南: [608.818, 255.7863],
  广西: [584.7208, 336.8405],
  云南: [450.6527, 342.5362],
  西藏: [234.5998, 454.4805],
  新疆: [224.9609, 609.2681],
  甘肃: [511.9367, 516.4285],
  内蒙古: [727.9352, 654.0016],
  河北: [680.179, 563.7467],
  河南: [661.3394, 501.9702],
  山西: [631.9846, 540.9638],
  陕西: [574.5895, 501.5321],
  宁夏: [537.7865, 551.9171],
  青海: [370.6249, 528.4917],
  四川: [477.967, 445.6277],
  重庆: [548.9442, 434.6744],
  贵州: [552.8874, 389.5469],
  湖北: [639.6373, 457.8953],
  湖南: [633.0653, 409.7009],
  江西: [702.29, 404.4433],
  安徽: [725.511, 468.4105],
};

export const useGrainStore = defineStore("grain", () => {
  const pageData = ref({});
  const mapStore = useMapStore();
  async function getData(query) {
    try {
      mapStore.$reset();
      const params = {
        ...query,
      };
      const grainData = await getGrainData(params);
      pageData.value = grainData;

      const markData = createMarkData(grainData.mapLabels);
      mapStore.setMarkData(markData);
    } catch (error) {
      console.log(error);
    }
  }

  return { pageData, getData };
});

function createMarkData(mapLabels) {
  const nameDatas = [];
  const labelDatas = [];
  const labelMap = {};
  for (let index = 0; index < mapLabels.length; index++) {
    const item = mapLabels[index];
    labelMap[item.province] = true;
    labelDatas.push({
      id: `label${index}`,
      coordinate: PositionMap[item.province],
      text: item.count + "",
      image: mapLabel,
      status: "normal",
      styleType: "grainLabel",
    });
  }
  for (const province in PositionMap) {
    if (Object.hasOwnProperty.call(PositionMap, province)) {
      nameDatas.push({
        id: `name${province}`,
        coordinate: PositionMap[province],
        text: province,
        image: labelMap[province] ? mapDot2 : mapDot1,
        status: "normal",
        styleType: labelMap[province] ? "grainNameActive" : "grainName",
      });
    }
  }
  const markData = [
    {
      markType: "grainLabel",
      name: "订单个数",
      visible: true,
      datas: labelDatas,
      zIndex: 3,
    },
    {
      markType: "grainName",
      name: "省份名称",
      visible: true,
      datas: nameDatas,
      zIndex: 1,
    },
  ];
  return markData;
}
