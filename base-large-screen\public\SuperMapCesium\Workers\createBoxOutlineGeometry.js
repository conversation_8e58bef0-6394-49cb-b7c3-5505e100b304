define(["./arrayFill-4513d7ad","./buildModuleUrl-8958744c","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./when-b60132fc","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./FeatureDetection-806b12f0","./Cartesian2-47311507","./Math-119be1a3","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian4-3ca25aab"],(function(e,t,a,n,i,r,u,o,m,s,f,c,d,l,b,p){"use strict";var y=new a.Cartesian3;function C(e){var t=(e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT)).minimum,n=e.maximum;this._min=a.Cartesian3.clone(t),this._max=a.Cartesian3.clone(n),this._offsetAttribute=e.offsetAttribute,this._workerName="createBoxOutlineGeometry"}C.fromDimensions=function(e){var t=(e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT)).dimensions,n=a.Cartesian3.multiplyByScalar(t,.5,new a.Cartesian3);return new C({minimum:a.Cartesian3.negate(n,new a.Cartesian3),maximum:n,offsetAttribute:e.offsetAttribute})},C.fromAxisAlignedBoundingBox=function(e){return new C({minimum:e.minimum,maximum:e.maximum})},C.packedLength=2*a.Cartesian3.packedLength+1,C.pack=function(e,t,n){return n=r.defaultValue(n,0),a.Cartesian3.pack(e._min,t,n),a.Cartesian3.pack(e._max,t,n+a.Cartesian3.packedLength),t[n+2*a.Cartesian3.packedLength]=r.defaultValue(e._offsetAttribute,-1),t};var A=new a.Cartesian3,x=new a.Cartesian3,_={minimum:A,maximum:x,offsetAttribute:void 0};return C.unpack=function(e,t,n){t=r.defaultValue(t,0);var i=a.Cartesian3.unpack(e,t,A),u=a.Cartesian3.unpack(e,t+a.Cartesian3.packedLength,x),o=e[t+2*a.Cartesian3.packedLength];return r.defined(n)?(n._min=a.Cartesian3.clone(i,n._min),n._max=a.Cartesian3.clone(u,n._max),n._offsetAttribute=-1===o?void 0:o,n):(_.offsetAttribute=-1===o?void 0:o,new C(_))},C.createGeometry=function(n){var f=n._min,c=n._max;if(!a.Cartesian3.equals(f,c)){var d=new o.GeometryAttributes,l=new Uint16Array(24),b=new Float64Array(24);b[0]=f.x,b[1]=f.y,b[2]=f.z,b[3]=c.x,b[4]=f.y,b[5]=f.z,b[6]=c.x,b[7]=c.y,b[8]=f.z,b[9]=f.x,b[10]=c.y,b[11]=f.z,b[12]=f.x,b[13]=f.y,b[14]=c.z,b[15]=c.x,b[16]=f.y,b[17]=c.z,b[18]=c.x,b[19]=c.y,b[20]=c.z,b[21]=f.x,b[22]=c.y,b[23]=c.z,d.position=new u.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:b}),l[0]=4,l[1]=5,l[2]=5,l[3]=6,l[4]=6,l[5]=7,l[6]=7,l[7]=4,l[8]=0,l[9]=1,l[10]=1,l[11]=2,l[12]=2,l[13]=3,l[14]=3,l[15]=0,l[16]=0,l[17]=4,l[18]=1,l[19]=5,l[20]=2,l[21]=6,l[22]=3,l[23]=7;var p=a.Cartesian3.subtract(c,f,y),C=.5*a.Cartesian3.magnitude(p);if(r.defined(n._offsetAttribute)){var A=b.length,x=new Uint8Array(A/3),_=n._offsetAttribute===m.GeometryOffsetAttribute.NONE?0:1;e.arrayFill(x,_),d.applyOffset=new u.GeometryAttribute({componentDatatype:i.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:x})}return new u.Geometry({attributes:d,indices:l,primitiveType:s.PrimitiveType.LINES,boundingSphere:new t.BoundingSphere(a.Cartesian3.ZERO,C),offsetAttribute:n._offsetAttribute})}},function(e,t){return r.defined(t)&&(e=C.unpack(e,t)),C.createGeometry(e)}}));
