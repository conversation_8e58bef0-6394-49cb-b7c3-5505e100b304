//This file is automatically rebuilt by the Cesium build process.
export default "/**\n\
 * A built-in GLSL floating-point constant for converting radians to degrees.\n\
 *\n\
 * @alias czm_degreesPerRadian\n\
 * @glslConstant\n\
 *\n\
 * @see CesiumMath.DEGREES_PER_RADIAN\n\
 *\n\
 * @example\n\
 * // GLSL declaration\n\
 * const float czm_degreesPerRadian = ...;\n\
 *\n\
 * // Example\n\
 * float deg = czm_degreesPerRadian * rad;\n\
 */\n\
const float czm_degreesPerRadian = 57.29577951308232;\n\
";
