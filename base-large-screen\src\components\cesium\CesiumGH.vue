<template>
  <div class="box">
    <div ref="mapDom" class="map-box">
    </div>
    <div ref="dialogDom" :style="dialogDomStyle" id="cesium-overlay-container"></div>

    <!-- GH功能增强控制面板 -->
    <div class="gh-control-panel" v-if="showGHControls">
      <div class="control-group">
        <h4>场景设置</h4>
        <button @click="toggleSceneMode">切换2D/3D</button>
        <button @click="toggleLighting">切换光照</button>
        <button @click="toggleAtmosphere">切换大气</button>
        <button @click="toggleFog">切换雾效</button>
        <button @click="toggleShadows">切换阴影</button>
      </div>

      <div class="control-group">
        <h4>图层管理</h4>
        <button @click="addTileCoordinates">显示瓦片坐标</button>
        <button @click="addGrid">显示网格</button>
        <button @click="toggleGlobeTranslucency">地球透明</button>
        <button @click="toggleUnderground">地下模式</button>
      </div>

      <div class="control-group">
        <h4>交互功能</h4>
        <button @click="enableAdvancedPicking">高级拾取</button>
        <button @click="setCustomView">自定义视角</button>
        <button @click="enableMeasurement">测量工具</button>
        <button @click="clearGHFeatures">清除GH功能</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, onBeforeUnmount, ref, watch, computed } from "vue";
import { onBeforeRouteLeave } from 'vue-router'
import { MAP_CONFIG } from "../../config"
import { useMapStore } from "../../stores/modules/map";
import { useMapDialogStore } from "../../stores/modules/mapDialog"
import { useSystemStore } from "../../stores/system";
import { useGeoJsonStore } from "../../stores/modules/geoJson";
import { ImageStyle, TextStyle, PolygonStyle, WallStyle, CircleStyle, GeoJsonStyle } from "./style"
import { getGifBillboard, unbindGif, destroyCache, isGif } from "../../utils/gifRender"
import { request } from "../../api/request";
import { usePageScale } from '../../composables/pageScale'

// Props
const props = defineProps({
  showGHControls: {
    type: Boolean,
    default: true
  }
});

const mapStore = useMapStore();
const dialogStore = useMapDialogStore();
const systemStore = useSystemStore();
const geoJsonStore = useGeoJsonStore();

let isLeave = false
const mapDom = ref(null)
const dialogDom = ref(null)
let mapViewer

// GH功能增强变量
let ghFeatures = {
  tileCoordinatesLayer: null,
  gridLayer: null,
  fogEnabled: false,
  lightingEnabled: true,
  atmosphereEnabled: true,
  shadowsEnabled: false,
  translucencyEnabled: false,
  undergroundEnabled: false,
  advancedPickingEnabled: false
}

//俯视角参数
const lookDownOrientation = {
  heading: Cesium.Math.toRadians(0),
  pitch: Cesium.Math.toRadians(-90),
  roll: 0.0
}

// 弹窗大屏适配
const { layout, scale, adaptScale } = usePageScale();
const dialogDomStyle = computed(() => {
  if (layout.value === 'adapt') {
    return {
      transformOrigin: 'top left',
      transform: `scale(${adaptScale.value})`,
    }
  }
  return {
    transformOrigin: 'top left',
    transform: `scale(${scale.value})`,
  }
});

// 初始化地图 - 基于原有组件但增加GH功能
function initMap() {
  if (Cesium.SuperMapVersion) {
    Cesium.Ellipsoid.WGS84 = new Cesium.Ellipsoid(6378137.0, 6378137.0, 6356752.3142451793)
  }
  mapViewer = new Cesium.Viewer(mapDom.value, {
    infoBox: false,
    baseLayerPicker: false,
    fullscreenButton: false,
    vrButton: false,
    geocoder: false,
    homeButton: false,
    sceneModePicker: false,
    selectionIndicator: false,
    navigationHelpButton: false,
    navigationInstructionsInitiallyVisible: false,
    timeline: false,
    scene3DOnly: true,
    animation: false,
    skyBox: false,
    shouldAnimate: false,
    requestRenderMode: false,
    baseLayer: false,
    imageryProvider: false,
    navigation: false,
    contextOptions: { requestWebgl1: true }
  });

  mapViewer.resolutionScale = window.devicePixelRatio;
  mapViewer.cesiumWidget.creditContainer.remove()

  // 初始化基础地图
  if (MAP_CONFIG.provider === 'TDT') {
    initTDTMap()
  } else if (MAP_CONFIG.provider === 'SDTDT') {
    initSDTDTMap()
  }

  initCamera()
  initPickListener()
  initMoveListener()

  // 初始化GH增强功能
  initGHFeatures()
}

const layersMap = []

//初始化天地图
function initTDTMap() {
  addTDTLayers(mapStore.mapType)
}

//创建天地图
function addTDTLayers(type) {
  if (type === 'vector') {
    const [layerA, layerB] = createTDTVector()
    mapViewer.imageryLayers.add(layerA)
    mapViewer.imageryLayers.add(layerB)
    layersMap.push('vector')
    layersMap.push('vector')
  } else if (type === 'raster') {
    const [layerA, layerB] = createTDTRaster()
    mapViewer.imageryLayers.add(layerA)
    mapViewer.imageryLayers.add(layerB)
    layersMap.push('raster')
    layersMap.push('raster')
  }
}

//天地图影像地图
function createTDTRaster() {
  const projection = MAP_CONFIG.projection
  const urlMark = projection === 'EPSG:4326' ? 'c' : 'w'
  const layerAParams = {
    url: `https://t${Math.floor(Math.random() * 8)}.tianditu.gov.cn/img_${urlMark}/wmts?tk=${systemStore.mapKey}`,
    layer: 'img',
    tileMatrixSetID: urlMark,
    format: 'tiles',
    style: 'default',
    maximumLevel: 18,
  }
  const layerBParams = {
    url: `https://t${Math.floor(Math.random() * 8)}.tianditu.gov.cn/cia_${urlMark}/wmts?tk=${systemStore.mapKey}`,
    layer: 'cia',
    tileMatrixSetID: urlMark,
    format: 'tiles',
    style: 'default',
    maximumLevel: 18,
  }
  return [createWMTSLayer(layerAParams, projection), createWMTSLayer(layerBParams, projection)]
}

//天地图电子地图
function createTDTVector() {
  const projection = MAP_CONFIG.projection
  const urlMark = projection === 'EPSG:4326' ? 'c' : 'w'
  const layerAParams = {
    url: `https://t${Math.floor(Math.random() * 8)}.tianditu.gov.cn/vec_${urlMark}/wmts?tk=${systemStore.mapKey}`,
    layer: 'vec',
    tileMatrixSetID: urlMark,
    format: 'tiles',
    style: 'default',
    maximumLevel: 18,
  }
  const layerBParams = {
    url: `https://t${Math.floor(Math.random() * 8)}.tianditu.gov.cn/cva_${urlMark}/wmts?tk=${systemStore.mapKey}`,
    layer: 'cva',
    tileMatrixSetID: urlMark,
    format: 'tiles',
    style: 'default',
    maximumLevel: 18,
  }
  return [createWMTSLayer(layerAParams, projection), createWMTSLayer(layerBParams, projection)]
}

//初始化山东天地图
function initSDTDTMap() {
  addSDTDTLayers(mapStore.mapType)
  limitCameraDistance(1116387)
}

//创建山东天地图
function addSDTDTLayers(type) {
  if (type === 'vector') {
    const layer = createSDTDTVector()
    mapViewer.imageryLayers.add(layer)
    layersMap.push('vector')
  } else if (type === 'raster') {
    const [layerA, layerB] = createSDTDTRaster()
    mapViewer.imageryLayers.add(layerA)
    mapViewer.imageryLayers.add(layerB)
    layersMap.push('raster')
    layersMap.push('raster')
  }
}

//山东天地图影像地图
function createSDTDTRaster() {
  const projection = 'EPSG:4326'
  const layerAParams = {
    url: `${document.location.protocol}//service.sdmap.gov.cn/tileservice/sdrasterpubmap?tk=${systemStore.mapKey}`,
    layer: 'SDRasterPubMap',
    tileMatrixSetID: 'raster',
    format: 'image/jpeg',
    style: 'default',
    maximumLevel: 18
  }
  const layerBParams = {
    url: `${document.location.protocol}//service.sdmap.gov.cn/tileservice/sdrasterpubmapdj?tk=${systemStore.mapKey}`,
    layer: 'SDRasterPubMapDJ',
    tileMatrixSetID: 'rasterdj',
    format: 'image/png',
    style: 'default',
    maximumLevel: 18
  }
  return [createWMTSLayer(layerAParams, projection), createWMTSLayer(layerBParams, projection)]
}

//山东天地图电子地图
function createSDTDTVector() {
  const projection = 'EPSG:4326'
  const layerAParams = {
    url: `${document.location.protocol}//service.sdmap.gov.cn/tileservice/sdpubmap?tk=${systemStore.mapKey}`,
    layer: 'SDPubMap',
    tileMatrixSetID: 'vector',
    format: 'image/png',
    style: 'default',
    maximumLevel: 18
  }
  return createWMTSLayer(layerAParams, projection)
}

//创建wmts图层
function createWMTSLayer(baseParams, projection) {
  const tileMatrixLabels = [];
  const startZ = projection === 'EPSG:4326' ? 1 : 0
  for (let z = startZ; z <= baseParams.maximumLevel; z++) {
    tileMatrixLabels.push(z);
  }
  const params = {
    ...baseParams,
    tileMatrixLabels
  }
  if (projection === 'EPSG:4326') {
    params.tilingScheme = new Cesium.GeographicTilingScheme()
    params.maximumLevel = params.maximumLevel - 1
  }
  const provider = new Cesium.WebMapTileServiceImageryProvider(params)
  return new Cesium.ImageryLayer(provider)
}

//初始化视角
function initCamera() {
  mapViewer.camera.setView(getViewData(mapStore.mapView))
}

//获取视角数据
function getViewData(params) {
  const { destination, orientation = lookDownOrientation, center, height } = params
  if (destination) {
    return {
      destination,
      orientation
    }
  } else if (center && center.length) {
    return {
      destination: Cesium.Cartesian3.fromDegrees(center[0], center[1], height),
      orientation
    }
  }
}

//初始化点击事件监听
function initPickListener() {
  let handler = new Cesium.ScreenSpaceEventHandler(mapViewer.scene.canvas);
  handler.setInputAction((click) => {
    dialogStore.close()
    const pickedObject = mapViewer.scene.pick(click.position);
    if (Cesium.defined(pickedObject) && pickedObject.id instanceof Cesium.Entity) {
      const pickedEntity = pickedObject.id;
      if (pickedEntity?.entityCollection?.owner?.markType) {
        const dialogData = {
          id: pickedEntity.id,
          name: pickedEntity.entityCollection.owner.markType,
          position: pickedEntity.position.getValue(),
        }
        dialogStore.openRequest(dialogData, 'cesium')
      }
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
}

//初始化鼠标移动事件监听
function initMoveListener() {
  let handler = new Cesium.ScreenSpaceEventHandler(mapViewer.scene.canvas);
  handler.setInputAction((movement) => {
    // 基础移动监听逻辑
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
}

//限制显示高度
function limitCameraDistance(max, min) {
  const screenSpaceCameraController = mapViewer.scene.screenSpaceCameraController;
  if (max) {
    screenSpaceCameraController.maximumZoomDistance = max;
  }
  if (min) {
    screenSpaceCameraController.minimumZoomDistance = min;
  }
}

// 初始化GH增强功能
function initGHFeatures() {
  console.log('Initializing GH enhanced features...')
  // 设置默认的GH功能状态
  ghFeatures.lightingEnabled = true
  ghFeatures.atmosphereEnabled = true
  ghFeatures.shadowsEnabled = false
  ghFeatures.fogEnabled = false
}

// GH功能增强控制方法
function toggleSceneMode() {
  if (!mapViewer) return;

  const currentMode = mapViewer.scene.mode;
  if (currentMode === Cesium.SceneMode.SCENE3D) {
    mapViewer.scene.mode = Cesium.SceneMode.SCENE2D;
    console.log('Switched to 2D mode');
  } else {
    mapViewer.scene.mode = Cesium.SceneMode.SCENE3D;
    console.log('Switched to 3D mode');
  }
}

function toggleLighting() {
  if (!mapViewer) return;

  ghFeatures.lightingEnabled = !ghFeatures.lightingEnabled;
  mapViewer.scene.globe.enableLighting = ghFeatures.lightingEnabled;
  mapViewer.scene.globe.dynamicAtmosphereLighting = ghFeatures.lightingEnabled;
  console.log('Lighting:', ghFeatures.lightingEnabled ? 'enabled' : 'disabled');
}

function toggleAtmosphere() {
  if (!mapViewer) return;

  ghFeatures.atmosphereEnabled = !ghFeatures.atmosphereEnabled;
  mapViewer.scene.skyAtmosphere.show = ghFeatures.atmosphereEnabled;
  mapViewer.scene.globe.showGroundAtmosphere = ghFeatures.atmosphereEnabled;
  console.log('Atmosphere:', ghFeatures.atmosphereEnabled ? 'enabled' : 'disabled');
}

function toggleFog() {
  if (!mapViewer) return;

  ghFeatures.fogEnabled = !ghFeatures.fogEnabled;
  mapViewer.scene.fog.enabled = ghFeatures.fogEnabled;
  if (ghFeatures.fogEnabled) {
    mapViewer.scene.fog.density = 0.0002;
  }
  console.log('Fog:', ghFeatures.fogEnabled ? 'enabled' : 'disabled');
}

function toggleShadows() {
  if (!mapViewer) return;

  ghFeatures.shadowsEnabled = !ghFeatures.shadowsEnabled;
  mapViewer.scene.globe.shadows = ghFeatures.shadowsEnabled ?
    Cesium.ShadowMode.ENABLED : Cesium.ShadowMode.DISABLED;
  console.log('Shadows:', ghFeatures.shadowsEnabled ? 'enabled' : 'disabled');
}

function addTileCoordinates() {
  if (!mapViewer || ghFeatures.tileCoordinatesLayer) return;

  const tileCoordinatesImageryProvider = new Cesium.TileCoordinatesImageryProvider({
    color: Cesium.Color.fromCssColorString('rgba(255, 255, 0, 1)')
  });
  ghFeatures.tileCoordinatesLayer = mapViewer.imageryLayers.addImageryProvider(tileCoordinatesImageryProvider);
  console.log('Tile coordinates layer added');
}

function addGrid() {
  if (!mapViewer || ghFeatures.gridLayer) return;

  const gridImageryProvider = new Cesium.GridImageryProvider({
    color: Cesium.Color.fromCssColorString('rgba(255, 255, 255, 0.8)'),
    glowColor: Cesium.Color.fromCssColorString('rgba(0, 255, 255, 1)'),
    glowWidth: 6,
    backgroundColor: Cesium.Color.fromCssColorString('rgba(0, 0, 0, 0)')
  });
  ghFeatures.gridLayer = mapViewer.imageryLayers.addImageryProvider(gridImageryProvider);
  console.log('Grid layer added');
}

function toggleGlobeTranslucency() {
  if (!mapViewer) return;

  ghFeatures.translucencyEnabled = !ghFeatures.translucencyEnabled;
  mapViewer.scene.globe.translucency.enabled = ghFeatures.translucencyEnabled;
  if (ghFeatures.translucencyEnabled) {
    mapViewer.scene.globe.translucency.frontFaceAlpha = 0.5;
    mapViewer.scene.globe.translucency.backFaceAlpha = 0.5;
  }
  console.log('Globe translucency:', ghFeatures.translucencyEnabled ? 'enabled' : 'disabled');
}

function toggleUnderground() {
  if (!mapViewer) return;

  ghFeatures.undergroundEnabled = !ghFeatures.undergroundEnabled;
  mapViewer.scene.screenSpaceCameraController.enableCollisionDetection = !ghFeatures.undergroundEnabled;
  console.log('Underground mode:', ghFeatures.undergroundEnabled ? 'enabled' : 'disabled');
}

function enableAdvancedPicking() {
  if (!mapViewer) return;

  ghFeatures.advancedPickingEnabled = !ghFeatures.advancedPickingEnabled;

  if (ghFeatures.advancedPickingEnabled) {
    const handler = new Cesium.ScreenSpaceEventHandler(mapViewer.scene.canvas);
    handler.setInputAction((click) => {
      const pickedObject = mapViewer.scene.pick(click.position);
      const cartesian = mapViewer.camera.pickEllipsoid(click.position, mapViewer.scene.globe.ellipsoid);

      if (cartesian) {
        const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
        const longitude = Cesium.Math.toDegrees(cartographic.longitude);
        const latitude = Cesium.Math.toDegrees(cartographic.latitude);
        const height = cartographic.height;

        console.log('Advanced picking result:', {
          longitude: longitude.toFixed(6),
          latitude: latitude.toFixed(6),
          height: height.toFixed(2),
          pickedObject: pickedObject
        });
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

    ghFeatures.pickingHandler = handler;
    console.log('Advanced picking enabled');
  } else {
    if (ghFeatures.pickingHandler) {
      ghFeatures.pickingHandler.destroy();
      ghFeatures.pickingHandler = null;
    }
    console.log('Advanced picking disabled');
  }
}

function setCustomView() {
  if (!mapViewer) return;

  // 设置一个自定义视角，类似gh.js的setView功能
  mapViewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(116.4, 39.9, 15000000),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-45),
      roll: Cesium.Math.toRadians(0)
    },
    duration: 3
  });
  console.log('Custom view set');
}

function enableMeasurement() {
  console.log('Measurement tool enabled (placeholder)');
  // 这里可以添加测量工具的实现
}

function clearGHFeatures() {
  if (!mapViewer) return;

  // 清除瓦片坐标图层
  if (ghFeatures.tileCoordinatesLayer) {
    mapViewer.imageryLayers.remove(ghFeatures.tileCoordinatesLayer, true);
    ghFeatures.tileCoordinatesLayer = null;
  }

  // 清除网格图层
  if (ghFeatures.gridLayer) {
    mapViewer.imageryLayers.remove(ghFeatures.gridLayer, true);
    ghFeatures.gridLayer = null;
  }

  // 清除拾取处理器
  if (ghFeatures.pickingHandler) {
    ghFeatures.pickingHandler.destroy();
    ghFeatures.pickingHandler = null;
  }

  // 重置所有功能状态
  ghFeatures.fogEnabled = false;
  ghFeatures.translucencyEnabled = false;
  ghFeatures.undergroundEnabled = false;
  ghFeatures.advancedPickingEnabled = false;

  // 重置场景状态
  mapViewer.scene.fog.enabled = false;
  mapViewer.scene.globe.translucency.enabled = false;
  mapViewer.scene.screenSpaceCameraController.enableCollisionDetection = true;

  console.log('All GH features cleared');
}

// 生命周期
onMounted(() => {
  initMap();
});

onUnmounted(() => {
  if (mapViewer) {
    mapViewer.destroy();
  }
  clearGHFeatures();
});

// 暴露方法给父组件
defineExpose({
  mapViewer,
  toggleSceneMode,
  toggleLighting,
  toggleAtmosphere,
  toggleFog,
  toggleShadows,
  addTileCoordinates,
  addGrid,
  toggleGlobeTranslucency,
  toggleUnderground,
  enableAdvancedPicking,
  setCustomView,
  enableMeasurement,
  clearGHFeatures
});
</script>

<style scoped>
.box {
  width: 100%;
  height: 100%;
}

.map-box {
  width: 100%;
  height: 100%;
}

#cesium-overlay-container {
  position: absolute;
}

.gh-control-panel {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 8px;
  min-width: 220px;
  max-height: 85vh;
  overflow-y: auto;
  z-index: 1000;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.control-group {
  margin-bottom: 20px;
}

.control-group:last-child {
  margin-bottom: 0;
}

.control-group h4 {
  margin: 0 0 12px 0;
  color: #00d4ff;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
  padding-bottom: 5px;
}

.control-group button {
  display: block;
  width: 100%;
  margin-bottom: 6px;
  padding: 10px 14px;
  background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
  color: white;
  border: 1px solid #444;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.control-group button:hover {
  background: linear-gradient(135deg, #3a3a3a, #2a2a2a);
  border-color: #00d4ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2);
}

.control-group button:active {
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 212, 255, 0.4);
}

.control-group button:last-child {
  margin-bottom: 0;
}

/* 滚动条样式 */
.gh-control-panel::-webkit-scrollbar {
  width: 6px;
}

.gh-control-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.gh-control-panel::-webkit-scrollbar-thumb {
  background: rgba(0, 212, 255, 0.6);
  border-radius: 3px;
}

.gh-control-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 212, 255, 0.8);
}
</style>
