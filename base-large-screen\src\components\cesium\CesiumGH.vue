<template>
  <div class="cesium-gh-container">
    <div ref="cesiumContainer" class="cesium-container" :id="containerId"></div>
    <div ref="dialogDom" :style="dialogDomStyle" id="cesium-gh-overlay-container"></div>
    
    <!-- 控制面板 -->
    <div class="control-panel" v-if="showControls">
      <div class="control-group">
        <h4>地图设置</h4>
        <button @click="toggleMapMode">切换2D/3D</button>
        <button @click="toggleLighting">切换光照</button>
        <button @click="toggleAtmosphere">切换大气</button>
        <button @click="toggleFog">切换雾效</button>
      </div>
      
      <div class="control-group">
        <h4>图层管理</h4>
        <button @click="createTestLayer">创建测试图层</button>
        <button @click="loadTiandituLayer">加载天地图</button>
        <button @click="clearAllLayers">清除所有图层</button>
      </div>
      
      <div class="control-group">
        <h4>交互功能</h4>
        <button @click="enablePositionPicking">启用位置拾取</button>
        <button @click="enableInfoPicking">启用信息拾取</button>
        <button @click="clearInteractions">清除交互</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, computed, nextTick } from "vue";
import { useMapStore } from "../../stores/modules/map";
import { useMapDialogStore } from "../../stores/modules/mapDialog";
import { usePageScale } from '../../composables/pageScale';

// Props
const props = defineProps({
  showControls: {
    type: Boolean,
    default: true
  },
  containerId: {
    type: String,
    default: 'cesium-gh-container'
  },
  mapConfig: {
    type: Object,
    default: () => ({})
  }
});

// Stores
const mapStore = useMapStore();
const dialogStore = useMapDialogStore();

// Refs
const cesiumContainer = ref(null);
const dialogDom = ref(null);

// GH Map instance
let ghMap = null;
let isInitialized = false;

// 生成唯一容器ID
const containerId = ref(props.containerId + '_' + Date.now());

// 弹窗大屏适配
const { layout, scale, adaptScale } = usePageScale();
const dialogDomStyle = computed(() => {
  if (layout.value === 'adapt') {
    return {
      transformOrigin: 'top left',
      transform: `scale(${adaptScale.value})`,
    }
  }
  return {
    transformOrigin: 'top left',
    transform: `scale(${scale.value})`,
  }
});

// 初始化GH Map
async function initGHMap() {
  try {
    // 动态加载gh.js
    if (!window.gh) {
      await loadGHScript();
    }
    
    // 创建GH Map实例
    ghMap = new window.gh.Map();
    
    // 绑定到容器
    await nextTick();
    ghMap.bind(containerId.value);
    
    // 设置默认配置
    setupDefaultSettings();
    
    // 初始化交互
    setupInteractions();
    
    isInitialized = true;
    console.log('GH Map initialized successfully');
    
  } catch (error) {
    console.error('Failed to initialize GH Map:', error);
  }
}

// 动态加载gh.js脚本
function loadGHScript() {
  return new Promise((resolve, reject) => {
    if (window.gh) {
      resolve();
      return;
    }
    
    const script = document.createElement('script');
    script.src = '/src/assets/gh/gh.js';
    script.onload = () => {
      console.log('GH script loaded');
      resolve();
    };
    script.onerror = () => {
      reject(new Error('Failed to load GH script'));
    };
    document.head.appendChild(script);
  });
}

// 设置默认配置
function setupDefaultSettings() {
  if (!ghMap) return;
  
  // 设置基本地图设置
  ghMap.set(window.gh.TYPE.SETTING.MAP_3D);
  ghMap.set(window.gh.TYPE.SETTING.LIGHTING_ENABLE);
  ghMap.set(window.gh.TYPE.SETTING.ATMOSPHERE_ENABLE);
  
  // 加载默认地形
  ghMap.loader.terrain({
    terrain: 'default',
    waterMask: true,
    vertexNormals: true
  });
}

// 设置交互
function setupInteractions() {
  if (!ghMap) return;
  
  // 默认启用位置拾取
  enablePositionPicking();
}

// 控制面板方法
function toggleMapMode() {
  if (!ghMap) return;
  
  // 这里可以添加2D/3D切换逻辑
  ghMap.set(window.gh.TYPE.SETTING.MAP_2D);
  setTimeout(() => {
    ghMap.set(window.gh.TYPE.SETTING.MAP_3D);
  }, 2000);
}

function toggleLighting() {
  if (!ghMap) return;
  
  // 切换光照
  ghMap.set(window.gh.TYPE.SETTING.LIGHTING_DISABLE);
  setTimeout(() => {
    ghMap.set(window.gh.TYPE.SETTING.LIGHTING_ENABLE);
  }, 1000);
}

function toggleAtmosphere() {
  if (!ghMap) return;
  
  ghMap.set(window.gh.TYPE.SETTING.ATMOSPHERE_DISABLE);
  setTimeout(() => {
    ghMap.set(window.gh.TYPE.SETTING.ATMOSPHERE_ENABLE);
  }, 1000);
}

function toggleFog() {
  if (!ghMap) return;
  
  ghMap.set(window.gh.TYPE.SETTING.FOG_ENABLE, { density: 0.0002 });
  setTimeout(() => {
    ghMap.set(window.gh.TYPE.SETTING.FOG_DISABLE);
  }, 3000);
}

function createTestLayer() {
  if (!ghMap) return;
  
  // 创建测试图层
  const layerName = ghMap.layer.create({
    name: 'testLayer_' + Date.now(),
    type: window.gh.TYPE.LAYER.POI3D
  });
  
  if (layerName) {
    console.log('Created test layer:', layerName);
  }
}

function loadTiandituLayer() {
  if (!ghMap) return;
  
  // 加载天地图
  const layerId = ghMap.loader.tianditu({
    layer: 'tianditu_' + Date.now(),
    type: window.gh.TYPE.MAP.T_SATELLITE,
    key: '9e624bfed46a72f99909f98dbef14c99'
  });
  
  if (layerId) {
    console.log('Loaded Tianditu layer:', layerId);
  }
}

function clearAllLayers() {
  if (!ghMap) return;
  
  // 这里可以添加清除所有图层的逻辑
  console.log('Clear all layers');
}

function enablePositionPicking() {
  if (!ghMap) return;
  
  ghMap.operator.get_position({
    type: window.gh.TYPE.OPERATION.LEFT_CLICK
  }, (position) => {
    console.log('Picked position:', position);
    // 可以在这里处理位置信息
  });
}

function enableInfoPicking() {
  if (!ghMap) return;
  
  ghMap.operator.get_info({
    type: window.gh.TYPE.OPERATION.LEFT_CLICK
  }, (info) => {
    console.log('Picked info:', info);
    // 可以在这里处理拾取的信息
  });
}

function clearInteractions() {
  if (!ghMap) return;
  
  ghMap.operator.clear({
    type: window.gh.TYPE.OPERATION.LEFT_CLICK
  });
}

// 生命周期
onMounted(async () => {
  await initGHMap();
});

onUnmounted(() => {
  if (ghMap) {
    // 清理资源
    ghMap = null;
  }
  isInitialized = false;
});

// 暴露方法给父组件
defineExpose({
  ghMap,
  isInitialized: () => isInitialized,
  toggleMapMode,
  toggleLighting,
  toggleAtmosphere,
  toggleFog,
  createTestLayer,
  loadTiandituLayer,
  enablePositionPicking,
  enableInfoPicking,
  clearInteractions
});
</script>

<style scoped>
.cesium-gh-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.cesium-container {
  width: 100%;
  height: 100%;
}

#cesium-gh-overlay-container {
  position: absolute;
  pointer-events: none;
}

.control-panel {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 15px;
  border-radius: 5px;
  min-width: 200px;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 1000;
}

.control-group {
  margin-bottom: 15px;
}

.control-group h4 {
  margin: 0 0 10px 0;
  color: #00d4ff;
  font-size: 14px;
}

.control-group button {
  display: block;
  width: 100%;
  margin-bottom: 5px;
  padding: 8px 12px;
  background: #333;
  color: white;
  border: 1px solid #555;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.3s;
}

.control-group button:hover {
  background: #555;
}

.control-group button:active {
  background: #00d4ff;
}
</style>
