import { ref, watch } from "vue";
import { useRoute } from "vue-router";
import { useEventListener } from "./event";
import { APP_CONFIG } from "../config";
//窗口尺寸和设计尺寸的比例
function getScale(windowW, windowH, pageW, pageH) {
  return Math.min(windowW / pageW, windowH / pageH);
}
//adaptScale：窗口高度和设计高度的比例    aspectRatio：窗口宽度和窗口高度的比例
export function usePageScale() {
  const route = useRoute();
  const pageSize = route.meta.pageSize || APP_CONFIG.pageSize;
  const layout = ref(route.meta.layout || APP_CONFIG.layout);
  const { clientWidth, clientHeight } = document.documentElement;
  const scale = ref(getScale(clientWidth, clientHeight, pageSize.width, pageSize.height));
  const adaptScale = ref(clientHeight / pageSize.height);
  const aspectRatio = ref(clientWidth / clientHeight);

  let running = false;

  watch(route, (newRoute, oldRoute) => {
    layout.value = newRoute.meta.layout || APP_CONFIG.layout;
  });

  useEventListener(window, "resize", () => {
    if (running) {
      return;
    }
    running = true;
    const { clientWidth, clientHeight } = document.documentElement;
    requestAnimationFrame(() => {
      scale.value = getScale(clientWidth, clientHeight, pageSize.width, pageSize.height);
      adaptScale.value = clientHeight / pageSize.height;
      aspectRatio.value = clientWidth / clientHeight;
      running = false;
    });
  });

  useEventListener(document, "keydown", (e) => {
    //ctrl+y
    if (e.ctrlKey && e.keyCode === 89) {
      layout.value = layout.value === "default" ? "adapt" : "default";
    }
  });

  return { layout, scale, adaptScale, aspectRatio, pageSize };
}
