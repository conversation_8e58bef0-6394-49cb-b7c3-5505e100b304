import { defineStore } from "pinia";
import { useMapStore } from "./map";
import { getMapData } from "../../api";

export const useOlStore = defineStore("ol", () => {
  const mapStore = useMapStore();
  async function getData(query) {
    try {
      const mapParams = {
        ...query,
      };
      const mapData = await getMapData(mapParams);
      if (mapData.placeList) {
        mapStore.setPlaceList(mapData.placeList);
      }
      if (mapData.mapView) {
        mapStore.setMapView(mapData.mapView);
      } else {
        mapStore.setDefaultView();
      }
      if (mapData.markData) {
        mapStore.setMarkData(mapData.markData);
      }
      if (mapData.polygonData) {
        mapStore.setPolygonData(mapData.polygonData);
      }
    } catch (error) {
      console.log(error);
    }
  }

  return { getData };
});
