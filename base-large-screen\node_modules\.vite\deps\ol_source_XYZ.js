import {
  TileImage_default,
  createXYZ,
  extentFromProjection
} from "./chunk-STFPBUNS.js";
import "./chunk-6ZJINPN3.js";
import "./chunk-3FQDMCK3.js";
import "./chunk-R6EOBB4S.js";
import "./chunk-LRXO5GLT.js";
import "./chunk-5D2XPBR2.js";
import "./chunk-CF5HEEKX.js";
import "./chunk-PPP4FLHO.js";
import "./chunk-VM3OS2EV.js";
import "./chunk-LMC3RO5P.js";
import "./chunk-FM44FOIC.js";
import "./chunk-5TDNKDLD.js";
import "./chunk-7ZCJ3QVN.js";
import "./chunk-FIJEAF7O.js";
import "./chunk-T5CHBIOF.js";
import "./chunk-EJONY5RU.js";
import "./chunk-IB3752ZZ.js";
import "./chunk-GW2WF4ZN.js";
import "./chunk-F65TP262.js";
import "./chunk-3L7AZTGC.js";
import "./chunk-JBPHGVFI.js";
import "./chunk-EPJCCVXM.js";
import "./chunk-X7AUGEB2.js";
import "./chunk-F2MRU6YO.js";
import "./chunk-5WWUZCGV.js";

// node_modules/ol/source/XYZ.js
var XYZ = class extends TileImage_default {
  /**
   * @param {Options} [options] XYZ options.
   */
  constructor(options) {
    options = options || {};
    const projection = options.projection !== void 0 ? options.projection : "EPSG:3857";
    const tileGrid = options.tileGrid !== void 0 ? options.tileGrid : createXYZ({
      extent: extentFromProjection(projection),
      maxResolution: options.maxResolution,
      maxZoom: options.maxZoom,
      minZoom: options.minZoom,
      tileSize: options.tileSize
    });
    super({
      attributions: options.attributions,
      cacheSize: options.cacheSize,
      crossOrigin: options.crossOrigin,
      interpolate: options.interpolate,
      opaque: options.opaque,
      projection,
      reprojectionErrorThreshold: options.reprojectionErrorThreshold,
      tileGrid,
      tileLoadFunction: options.tileLoadFunction,
      tilePixelRatio: options.tilePixelRatio,
      tileUrlFunction: options.tileUrlFunction,
      url: options.url,
      urls: options.urls,
      wrapX: options.wrapX !== void 0 ? options.wrapX : true,
      transition: options.transition,
      attributionsCollapsible: options.attributionsCollapsible,
      zDirection: options.zDirection
    });
    this.gutter_ = options.gutter !== void 0 ? options.gutter : 0;
  }
  /**
   * @return {number} Gutter.
   */
  getGutter() {
    return this.gutter_;
  }
};
var XYZ_default = XYZ;
export {
  XYZ_default as default
};
//# sourceMappingURL=ol_source_XYZ.js.map
