<template>
  <div class="box">
    <GrainPartTitle title="证件管理" :position="2" />
    <swiper-container class="swiper" direction="vertical" :autoplay="true" :autoplay-delay="3000" :speed="1000"
      loop="true" slidesPerView='2'>
      <swiper-slide class="line" v-for="map in documentMap">
        <div class="item" v-for="(item, index) in map">
          <img class="icon" :src="item.icon" alt="icon" />
          <div class="right">
            <div class="text">{{ item.name }}</div>
            <div class="effective">
              <GrowNumber class="number" :value="item.effective" :position="2" />
              <div class="text">天/过期</div>
            </div>
          </div>
        </div>
      </swiper-slide>
    </swiper-container>
  </div>
</template>
<script setup>
import { computed } from "vue";
import GrainPartTitle from "../../components/common/GrainPartTitle.vue"
import { useGrainStore } from "../../stores/modules/grain";
import GrowNumber from "../../components/common/GrowNumber.vue"

const { pageData } = useGrainStore()

const documentMap = computed(() => {
  const map = []
  if (!pageData.document) {
    return map
  }
  for (let index = 0; index < pageData.document.length; index += 2) {
    map.push(pageData.document.slice(index, index + 2))
  }
  return map
})

</script>
<style scoped lang="scss">
.box {
  height: 252px;
  width: 450px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.swiper {
  width: 426px;
  height: 212px;

  .line {
    width: 426px;
    height: 106px;
    display: flex;
    align-items: center;
  }

  .item {
    width: 213px;
    height: 106px;
    display: flex;
    align-items: center;

    .icon {
      width: 64px;
      height: 64px;
      margin-right: 12px;
      flex-shrink: 0;
    }

    .right {
      height: 64px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-around;

      .text {
        font-size: 18px;
        line-height: 19px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #A2B7D9;
        white-space: nowrap;
      }

      .effective {
        display: flex;
        align-items: flex-end;
      }

      .number {
        font-size: 26px;
        line-height: 27px;
        font-family: Myriad Pro;
        font-weight: 400;
        color: #C9DFFF;
        margin-right: 8px;
      }
    }
  }
}
</style>