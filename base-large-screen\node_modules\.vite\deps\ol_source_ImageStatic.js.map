{"version": 3, "sources": ["../../ol/reproj/Image.js", "../../ol/source/Image.js", "../../ol/source/ImageStatic.js"], "sourcesContent": ["/**\n * @module ol/reproj/Image\n */\nimport {ERROR_THRESHOLD} from './common.js';\n\nimport EventType from '../events/EventType.js';\nimport ImageBase from '../ImageBase.js';\nimport ImageState from '../ImageState.js';\nimport Triangulation from './Triangulation.js';\nimport {\n  calculateSourceResolution,\n  render as renderReprojected,\n} from '../reproj.js';\nimport {\n  getCenter,\n  getHeight,\n  getIntersection,\n  getWidth,\n  isEmpty,\n} from '../extent.js';\nimport {listen, unlistenByKey} from '../events.js';\n\n/**\n * @typedef {function(import(\"../extent.js\").Extent, number, number) : import(\"../ImageBase.js\").default} FunctionType\n */\n\n/**\n * @classdesc\n * Class encapsulating single reprojected image.\n * See {@link module:ol/source/Image~ImageSource}.\n */\nclass ReprojImage extends ImageBase {\n  /**\n   * @param {import(\"../proj/Projection.js\").default} sourceProj Source projection (of the data).\n   * @param {import(\"../proj/Projection.js\").default} targetProj Target projection.\n   * @param {import(\"../extent.js\").Extent} targetExtent Target extent.\n   * @param {number} targetResolution Target resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {FunctionType} getImageFunction\n   *     Function returning source images (extent, resolution, pixelRatio).\n   * @param {boolean} interpolate Use linear interpolation when resampling.\n   */\n  constructor(\n    sourceProj,\n    targetProj,\n    targetExtent,\n    targetResolution,\n    pixelRatio,\n    getImageFunction,\n    interpolate\n  ) {\n    let maxSourceExtent = sourceProj.getExtent();\n    if (maxSourceExtent && sourceProj.canWrapX()) {\n      maxSourceExtent = maxSourceExtent.slice();\n      maxSourceExtent[0] = -Infinity;\n      maxSourceExtent[2] = Infinity;\n    }\n    let maxTargetExtent = targetProj.getExtent();\n    if (maxTargetExtent && targetProj.canWrapX()) {\n      maxTargetExtent = maxTargetExtent.slice();\n      maxTargetExtent[0] = -Infinity;\n      maxTargetExtent[2] = Infinity;\n    }\n\n    const limitedTargetExtent = maxTargetExtent\n      ? getIntersection(targetExtent, maxTargetExtent)\n      : targetExtent;\n\n    const targetCenter = getCenter(limitedTargetExtent);\n    const sourceResolution = calculateSourceResolution(\n      sourceProj,\n      targetProj,\n      targetCenter,\n      targetResolution\n    );\n\n    const errorThresholdInPixels = ERROR_THRESHOLD;\n\n    const triangulation = new Triangulation(\n      sourceProj,\n      targetProj,\n      limitedTargetExtent,\n      maxSourceExtent,\n      sourceResolution * errorThresholdInPixels,\n      targetResolution\n    );\n\n    const sourceExtent = triangulation.calculateSourceExtent();\n    const sourceImage = isEmpty(sourceExtent)\n      ? null\n      : getImageFunction(sourceExtent, sourceResolution, pixelRatio);\n    const state = sourceImage ? ImageState.IDLE : ImageState.EMPTY;\n    const sourcePixelRatio = sourceImage ? sourceImage.getPixelRatio() : 1;\n\n    super(targetExtent, targetResolution, sourcePixelRatio, state);\n\n    /**\n     * @private\n     * @type {import(\"../proj/Projection.js\").default}\n     */\n    this.targetProj_ = targetProj;\n\n    /**\n     * @private\n     * @type {import(\"../extent.js\").Extent}\n     */\n    this.maxSourceExtent_ = maxSourceExtent;\n\n    /**\n     * @private\n     * @type {!import(\"./Triangulation.js\").default}\n     */\n    this.triangulation_ = triangulation;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.targetResolution_ = targetResolution;\n\n    /**\n     * @private\n     * @type {import(\"../extent.js\").Extent}\n     */\n    this.targetExtent_ = targetExtent;\n\n    /**\n     * @private\n     * @type {import(\"../ImageBase.js\").default}\n     */\n    this.sourceImage_ = sourceImage;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.sourcePixelRatio_ = sourcePixelRatio;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.interpolate_ = interpolate;\n\n    /**\n     * @private\n     * @type {HTMLCanvasElement}\n     */\n    this.canvas_ = null;\n\n    /**\n     * @private\n     * @type {?import(\"../events.js\").EventsKey}\n     */\n    this.sourceListenerKey_ = null;\n  }\n\n  /**\n   * Clean up.\n   */\n  disposeInternal() {\n    if (this.state == ImageState.LOADING) {\n      this.unlistenSource_();\n    }\n    super.disposeInternal();\n  }\n\n  /**\n   * @return {HTMLCanvasElement} Image.\n   */\n  getImage() {\n    return this.canvas_;\n  }\n\n  /**\n   * @return {import(\"../proj/Projection.js\").default} Projection.\n   */\n  getProjection() {\n    return this.targetProj_;\n  }\n\n  /**\n   * @private\n   */\n  reproject_() {\n    const sourceState = this.sourceImage_.getState();\n    if (sourceState == ImageState.LOADED) {\n      const width = getWidth(this.targetExtent_) / this.targetResolution_;\n      const height = getHeight(this.targetExtent_) / this.targetResolution_;\n\n      this.canvas_ = renderReprojected(\n        width,\n        height,\n        this.sourcePixelRatio_,\n        this.sourceImage_.getResolution(),\n        this.maxSourceExtent_,\n        this.targetResolution_,\n        this.targetExtent_,\n        this.triangulation_,\n        [\n          {\n            extent: this.sourceImage_.getExtent(),\n            image: this.sourceImage_.getImage(),\n          },\n        ],\n        0,\n        undefined,\n        this.interpolate_\n      );\n    }\n    this.state = sourceState;\n    this.changed();\n  }\n\n  /**\n   * Load not yet loaded URI.\n   */\n  load() {\n    if (this.state == ImageState.IDLE) {\n      this.state = ImageState.LOADING;\n      this.changed();\n\n      const sourceState = this.sourceImage_.getState();\n      if (sourceState == ImageState.LOADED || sourceState == ImageState.ERROR) {\n        this.reproject_();\n      } else {\n        this.sourceListenerKey_ = listen(\n          this.sourceImage_,\n          EventType.CHANGE,\n          function (e) {\n            const sourceState = this.sourceImage_.getState();\n            if (\n              sourceState == ImageState.LOADED ||\n              sourceState == ImageState.ERROR\n            ) {\n              this.unlistenSource_();\n              this.reproject_();\n            }\n          },\n          this\n        );\n        this.sourceImage_.load();\n      }\n    }\n  }\n\n  /**\n   * @private\n   */\n  unlistenSource_() {\n    unlistenByKey(\n      /** @type {!import(\"../events.js\").EventsKey} */ (this.sourceListenerKey_)\n    );\n    this.sourceListenerKey_ = null;\n  }\n}\n\nexport default ReprojImage;\n", "/**\n * @module ol/source/Image\n */\nimport Event from '../events/Event.js';\nimport ImageState from '../ImageState.js';\nimport ReprojImage from '../reproj/Image.js';\nimport Source from './Source.js';\nimport {abstract} from '../util.js';\nimport {equals} from '../extent.js';\nimport {equivalent} from '../proj.js';\nimport {linearFindNearest} from '../array.js';\n\n/**\n * @enum {string}\n */\nexport const ImageSourceEventType = {\n  /**\n   * Triggered when an image starts loading.\n   * @event module:ol/source/Image.ImageSourceEvent#imageloadstart\n   * @api\n   */\n  IMAGELOADSTART: 'imageloadstart',\n\n  /**\n   * Triggered when an image finishes loading.\n   * @event module:ol/source/Image.ImageSourceEvent#imageloadend\n   * @api\n   */\n  IMAGELOADEND: 'imageloadend',\n\n  /**\n   * Triggered if image loading results in an error.\n   * @event module:ol/source/Image.ImageSourceEvent#imageloaderror\n   * @api\n   */\n  IMAGELOADERROR: 'imageloaderror',\n};\n\n/**\n * @typedef {'imageloadend'|'imageloaderror'|'imageloadstart'} ImageSourceEventTypes\n */\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/source/Image~ImageSource} instances are instances of this\n * type.\n */\nexport class ImageSourceEvent extends Event {\n  /**\n   * @param {string} type Type.\n   * @param {import(\"../Image.js\").default} image The image.\n   */\n  constructor(type, image) {\n    super(type);\n\n    /**\n     * The image related to the event.\n     * @type {import(\"../Image.js\").default}\n     * @api\n     */\n    this.image = image;\n  }\n}\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types, import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").OnSignature<ImageSourceEventTypes, ImageSourceEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"../ObjectEventType\").Types\n *     |ImageSourceEventTypes, Return>} ImageSourceOnSignature\n */\n\n/**\n * @typedef {Object} Options\n * @property {import(\"./Source.js\").AttributionLike} [attributions] Attributions.\n * @property {boolean} [interpolate=true] Use interpolated values when resampling.  By default,\n * linear interpolation is used when resampling.  Set to false to use the nearest neighbor instead.\n * @property {import(\"../proj.js\").ProjectionLike} [projection] Projection.\n * @property {Array<number>} [resolutions] Resolutions.\n * @property {import(\"./Source.js\").State} [state] State.\n */\n\n/**\n * @classdesc\n * Abstract base class; normally only used for creating subclasses and not\n * instantiated in apps.\n * Base class for sources providing a single image.\n * @abstract\n * @fires module:ol/source/Image.ImageSourceEvent\n * @api\n */\nclass ImageSource extends Source {\n  /**\n   * @param {Options} options Single image source options.\n   */\n  constructor(options) {\n    super({\n      attributions: options.attributions,\n      projection: options.projection,\n      state: options.state,\n      interpolate:\n        options.interpolate !== undefined ? options.interpolate : true,\n    });\n\n    /***\n     * @type {ImageSourceOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {ImageSourceOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {ImageSourceOnSignature<void>}\n     */\n    this.un;\n\n    /**\n     * @private\n     * @type {Array<number>|null}\n     */\n    this.resolutions_ =\n      options.resolutions !== undefined ? options.resolutions : null;\n\n    /**\n     * @private\n     * @type {import(\"../reproj/Image.js\").default}\n     */\n    this.reprojectedImage_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.reprojectedRevision_ = 0;\n  }\n\n  /**\n   * @return {Array<number>|null} Resolutions.\n   */\n  getResolutions() {\n    return this.resolutions_;\n  }\n\n  /**\n   * @param {Array<number>|null} resolutions Resolutions.\n   */\n  setResolutions(resolutions) {\n    this.resolutions_ = resolutions;\n  }\n\n  /**\n   * @protected\n   * @param {number} resolution Resolution.\n   * @return {number} Resolution.\n   */\n  findNearestResolution(resolution) {\n    const resolutions = this.getResolutions();\n    if (resolutions) {\n      const idx = linearFindNearest(resolutions, resolution, 0);\n      resolution = resolutions[idx];\n    }\n    return resolution;\n  }\n\n  /**\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {number} resolution Resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {import(\"../ImageBase.js\").default} Single image.\n   */\n  getImage(extent, resolution, pixelRatio, projection) {\n    const sourceProjection = this.getProjection();\n    if (\n      !sourceProjection ||\n      !projection ||\n      equivalent(sourceProjection, projection)\n    ) {\n      if (sourceProjection) {\n        projection = sourceProjection;\n      }\n      return this.getImageInternal(extent, resolution, pixelRatio, projection);\n    }\n    if (this.reprojectedImage_) {\n      if (\n        this.reprojectedRevision_ == this.getRevision() &&\n        equivalent(this.reprojectedImage_.getProjection(), projection) &&\n        this.reprojectedImage_.getResolution() == resolution &&\n        equals(this.reprojectedImage_.getExtent(), extent)\n      ) {\n        return this.reprojectedImage_;\n      }\n      this.reprojectedImage_.dispose();\n      this.reprojectedImage_ = null;\n    }\n\n    this.reprojectedImage_ = new ReprojImage(\n      sourceProjection,\n      projection,\n      extent,\n      resolution,\n      pixelRatio,\n      (extent, resolution, pixelRatio) =>\n        this.getImageInternal(extent, resolution, pixelRatio, sourceProjection),\n      this.getInterpolate()\n    );\n    this.reprojectedRevision_ = this.getRevision();\n\n    return this.reprojectedImage_;\n  }\n\n  /**\n   * @abstract\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {number} resolution Resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {import(\"../ImageBase.js\").default} Single image.\n   * @protected\n   */\n  getImageInternal(extent, resolution, pixelRatio, projection) {\n    return abstract();\n  }\n\n  /**\n   * Handle image change events.\n   * @param {import(\"../events/Event.js\").default} event Event.\n   * @protected\n   */\n  handleImageChange(event) {\n    const image = /** @type {import(\"../Image.js\").default} */ (event.target);\n    let type;\n    switch (image.getState()) {\n      case ImageState.LOADING:\n        this.loading = true;\n        type = ImageSourceEventType.IMAGELOADSTART;\n        break;\n      case ImageState.LOADED:\n        this.loading = false;\n        type = ImageSourceEventType.IMAGELOADEND;\n        break;\n      case ImageState.ERROR:\n        this.loading = false;\n        type = ImageSourceEventType.IMAGELOADERROR;\n        break;\n      default:\n        return;\n    }\n    if (this.hasListener(type)) {\n      this.dispatchEvent(new ImageSourceEvent(type, image));\n    }\n  }\n}\n\n/**\n * Default image load function for image sources that use import(\"../Image.js\").Image image\n * instances.\n * @param {import(\"../Image.js\").default} image Image.\n * @param {string} src Source.\n */\nexport function defaultImageLoadFunction(image, src) {\n  /** @type {HTMLImageElement|HTMLVideoElement} */ (image.getImage()).src = src;\n}\n\nexport default ImageSource;\n", "/**\n * @module ol/source/ImageStatic\n */\n\nimport EventType from '../events/EventType.js';\nimport ImageSource, {defaultImageLoadFunction} from './Image.js';\nimport ImageState from '../ImageState.js';\nimport ImageWrapper from '../Image.js';\nimport {createCanvasContext2D} from '../dom.js';\nimport {getHeight, getWidth, intersects} from '../extent.js';\nimport {get as getProjection} from '../proj.js';\n\n/**\n * @typedef {Object} Options\n * @property {import(\"./Source.js\").AttributionLike} [attributions] Attributions.\n * @property {null|string} [crossOrigin] The `crossOrigin` attribute for loaded images.  Note that\n * you must provide a `crossOrigin` value if you want to access pixel data with the Canvas renderer.\n * See https://developer.mozilla.org/en-US/docs/Web/HTML/CORS_enabled_image for more detail.\n * @property {import(\"../extent.js\").Extent} [imageExtent] Extent of the image in map coordinates.\n * This is the [left, bottom, right, top] map coordinates of your image.\n * @property {import(\"../Image.js\").LoadFunction} [imageLoadFunction] Optional function to load an image given a URL.\n * @property {boolean} [interpolate=true] Use interpolated values when resampling.  By default,\n * linear interpolation is used when resampling.  Set to false to use the nearest neighbor instead.\n * @property {import(\"../proj.js\").ProjectionLike} [projection] Projection. Default is the view projection.\n * @property {import(\"../size.js\").Size} [imageSize] Size of the image in pixels. Usually the image size is auto-detected, so this\n * only needs to be set if auto-detection fails for some reason.\n * @property {string} url Image URL.\n */\n\n/**\n * @classdesc\n * A layer source for displaying a single, static image.\n * @api\n */\nclass Static extends ImageSource {\n  /**\n   * @param {Options} options ImageStatic options.\n   */\n  constructor(options) {\n    const crossOrigin =\n      options.crossOrigin !== undefined ? options.crossOrigin : null;\n\n    const /** @type {import(\"../Image.js\").LoadFunction} */ imageLoadFunction =\n        options.imageLoadFunction !== undefined\n          ? options.imageLoadFunction\n          : defaultImageLoadFunction;\n\n    super({\n      attributions: options.attributions,\n      interpolate: options.interpolate,\n      projection: getProjection(options.projection),\n    });\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.url_ = options.url;\n\n    /**\n     * @private\n     * @type {import(\"../extent.js\").Extent}\n     */\n    this.imageExtent_ = options.imageExtent;\n\n    /**\n     * @private\n     * @type {import(\"../Image.js\").default}\n     */\n    this.image_ = new ImageWrapper(\n      this.imageExtent_,\n      undefined,\n      1,\n      this.url_,\n      crossOrigin,\n      imageLoadFunction,\n      createCanvasContext2D(1, 1)\n    );\n\n    /**\n     * @private\n     * @type {import(\"../size.js\").Size|null}\n     */\n    this.imageSize_ = options.imageSize ? options.imageSize : null;\n\n    this.image_.addEventListener(\n      EventType.CHANGE,\n      this.handleImageChange.bind(this)\n    );\n  }\n\n  /**\n   * Returns the image extent\n   * @return {import(\"../extent.js\").Extent} image extent.\n   * @api\n   */\n  getImageExtent() {\n    return this.imageExtent_;\n  }\n\n  /**\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {number} resolution Resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {import(\"../Image.js\").default} Single image.\n   */\n  getImageInternal(extent, resolution, pixelRatio, projection) {\n    if (intersects(extent, this.image_.getExtent())) {\n      return this.image_;\n    }\n    return null;\n  }\n\n  /**\n   * Return the URL used for this image source.\n   * @return {string} URL.\n   * @api\n   */\n  getUrl() {\n    return this.url_;\n  }\n\n  /**\n   * @param {import(\"../events/Event.js\").default} evt Event.\n   */\n  handleImageChange(evt) {\n    if (this.image_.getState() == ImageState.LOADED) {\n      const imageExtent = this.image_.getExtent();\n      const image = this.image_.getImage();\n      let imageWidth, imageHeight;\n      if (this.imageSize_) {\n        imageWidth = this.imageSize_[0];\n        imageHeight = this.imageSize_[1];\n      } else {\n        imageWidth = image.width;\n        imageHeight = image.height;\n      }\n      const extentWidth = getWidth(imageExtent);\n      const extentHeight = getHeight(imageExtent);\n      const xResolution = extentWidth / imageWidth;\n      const yResolution = extentHeight / imageHeight;\n      let targetWidth = imageWidth;\n      let targetHeight = imageHeight;\n      if (xResolution > yResolution) {\n        targetWidth = Math.round(extentWidth / yResolution);\n      } else {\n        targetHeight = Math.round(extentHeight / xResolution);\n      }\n      if (targetWidth !== imageWidth || targetHeight !== imageHeight) {\n        const context = createCanvasContext2D(targetWidth, targetHeight);\n        if (!this.getInterpolate()) {\n          context.imageSmoothingEnabled = false;\n        }\n        const canvas = context.canvas;\n        context.drawImage(\n          image,\n          0,\n          0,\n          imageWidth,\n          imageHeight,\n          0,\n          0,\n          canvas.width,\n          canvas.height\n        );\n        this.image_.setImage(canvas);\n      }\n    }\n    super.handleImageChange(evt);\n  }\n}\n\nexport default Static;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,IAAM,cAAN,cAA0B,kBAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWlC,YACE,YACA,YACA,cACA,kBACA,YACA,kBACA,aACA;AACA,QAAI,kBAAkB,WAAW,UAAU;AAC3C,QAAI,mBAAmB,WAAW,SAAS,GAAG;AAC5C,wBAAkB,gBAAgB,MAAM;AACxC,sBAAgB,CAAC,IAAI;AACrB,sBAAgB,CAAC,IAAI;AAAA,IACvB;AACA,QAAI,kBAAkB,WAAW,UAAU;AAC3C,QAAI,mBAAmB,WAAW,SAAS,GAAG;AAC5C,wBAAkB,gBAAgB,MAAM;AACxC,sBAAgB,CAAC,IAAI;AACrB,sBAAgB,CAAC,IAAI;AAAA,IACvB;AAEA,UAAM,sBAAsB,kBACxB,gBAAgB,cAAc,eAAe,IAC7C;AAEJ,UAAM,eAAe,UAAU,mBAAmB;AAClD,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,UAAM,yBAAyB;AAE/B,UAAM,gBAAgB,IAAI;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,mBAAmB;AAAA,MACnB;AAAA,IACF;AAEA,UAAM,eAAe,cAAc,sBAAsB;AACzD,UAAM,cAAc,QAAQ,YAAY,IACpC,OACA,iBAAiB,cAAc,kBAAkB,UAAU;AAC/D,UAAM,QAAQ,cAAc,mBAAW,OAAO,mBAAW;AACzD,UAAM,mBAAmB,cAAc,YAAY,cAAc,IAAI;AAErE,UAAM,cAAc,kBAAkB,kBAAkB,KAAK;AAM7D,SAAK,cAAc;AAMnB,SAAK,mBAAmB;AAMxB,SAAK,iBAAiB;AAMtB,SAAK,oBAAoB;AAMzB,SAAK,gBAAgB;AAMrB,SAAK,eAAe;AAMpB,SAAK,oBAAoB;AAMzB,SAAK,eAAe;AAMpB,SAAK,UAAU;AAMf,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,QAAI,KAAK,SAAS,mBAAW,SAAS;AACpC,WAAK,gBAAgB;AAAA,IACvB;AACA,UAAM,gBAAgB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,UAAM,cAAc,KAAK,aAAa,SAAS;AAC/C,QAAI,eAAe,mBAAW,QAAQ;AACpC,YAAM,QAAQ,SAAS,KAAK,aAAa,IAAI,KAAK;AAClD,YAAM,SAAS,UAAU,KAAK,aAAa,IAAI,KAAK;AAEpD,WAAK,UAAU;AAAA,QACb;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,KAAK,aAAa,cAAc;AAAA,QAChC,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,UACE;AAAA,YACE,QAAQ,KAAK,aAAa,UAAU;AAAA,YACpC,OAAO,KAAK,aAAa,SAAS;AAAA,UACpC;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,MACP;AAAA,IACF;AACA,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,QAAI,KAAK,SAAS,mBAAW,MAAM;AACjC,WAAK,QAAQ,mBAAW;AACxB,WAAK,QAAQ;AAEb,YAAM,cAAc,KAAK,aAAa,SAAS;AAC/C,UAAI,eAAe,mBAAW,UAAU,eAAe,mBAAW,OAAO;AACvE,aAAK,WAAW;AAAA,MAClB,OAAO;AACL,aAAK,qBAAqB;AAAA,UACxB,KAAK;AAAA,UACL,kBAAU;AAAA,UACV,SAAU,GAAG;AACX,kBAAMA,eAAc,KAAK,aAAa,SAAS;AAC/C,gBACEA,gBAAe,mBAAW,UAC1BA,gBAAe,mBAAW,OAC1B;AACA,mBAAK,gBAAgB;AACrB,mBAAK,WAAW;AAAA,YAClB;AAAA,UACF;AAAA,UACA;AAAA,QACF;AACA,aAAK,aAAa,KAAK;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB;AAAA;AAAA,MACoD,KAAK;AAAA,IACzD;AACA,SAAK,qBAAqB;AAAA,EAC5B;AACF;AAEA,IAAOC,iBAAQ;;;AClPR,IAAM,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlC,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOhB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOd,gBAAgB;AAClB;AAWO,IAAM,mBAAN,cAA+B,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1C,YAAY,MAAM,OAAO;AACvB,UAAM,IAAI;AAOV,SAAK,QAAQ;AAAA,EACf;AACF;AA8BA,IAAM,cAAN,cAA0B,eAAO;AAAA;AAAA;AAAA;AAAA,EAI/B,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ,cAAc,QAAQ;AAAA,MACtB,YAAY,QAAQ;AAAA,MACpB,OAAO,QAAQ;AAAA,MACf,aACE,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAAA,IAC9D,CAAC;AAKD,SAAK;AAKL,SAAK;AAKL,SAAK;AAML,SAAK,eACH,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAM5D,SAAK,oBAAoB;AAMzB,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,aAAa;AAC1B,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB,YAAY;AAChC,UAAM,cAAc,KAAK,eAAe;AACxC,QAAI,aAAa;AACf,YAAM,MAAM,kBAAkB,aAAa,YAAY,CAAC;AACxD,mBAAa,YAAY,GAAG;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,QAAQ,YAAY,YAAY,YAAY;AACnD,UAAM,mBAAmB,KAAK,cAAc;AAC5C,QACE,CAAC,oBACD,CAAC,cACD,WAAW,kBAAkB,UAAU,GACvC;AACA,UAAI,kBAAkB;AACpB,qBAAa;AAAA,MACf;AACA,aAAO,KAAK,iBAAiB,QAAQ,YAAY,YAAY,UAAU;AAAA,IACzE;AACA,QAAI,KAAK,mBAAmB;AAC1B,UACE,KAAK,wBAAwB,KAAK,YAAY,KAC9C,WAAW,KAAK,kBAAkB,cAAc,GAAG,UAAU,KAC7D,KAAK,kBAAkB,cAAc,KAAK,cAC1C,OAAO,KAAK,kBAAkB,UAAU,GAAG,MAAM,GACjD;AACA,eAAO,KAAK;AAAA,MACd;AACA,WAAK,kBAAkB,QAAQ;AAC/B,WAAK,oBAAoB;AAAA,IAC3B;AAEA,SAAK,oBAAoB,IAAIC;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAACC,SAAQC,aAAYC,gBACnB,KAAK,iBAAiBF,SAAQC,aAAYC,aAAY,gBAAgB;AAAA,MACxE,KAAK,eAAe;AAAA,IACtB;AACA,SAAK,uBAAuB,KAAK,YAAY;AAE7C,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,iBAAiB,QAAQ,YAAY,YAAY,YAAY;AAC3D,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,OAAO;AACvB,UAAM;AAAA;AAAA,MAAsD,MAAM;AAAA;AAClE,QAAI;AACJ,YAAQ,MAAM,SAAS,GAAG;AAAA,MACxB,KAAK,mBAAW;AACd,aAAK,UAAU;AACf,eAAO,qBAAqB;AAC5B;AAAA,MACF,KAAK,mBAAW;AACd,aAAK,UAAU;AACf,eAAO,qBAAqB;AAC5B;AAAA,MACF,KAAK,mBAAW;AACd,aAAK,UAAU;AACf,eAAO,qBAAqB;AAC5B;AAAA,MACF;AACE;AAAA,IACJ;AACA,QAAI,KAAK,YAAY,IAAI,GAAG;AAC1B,WAAK,cAAc,IAAI,iBAAiB,MAAM,KAAK,CAAC;AAAA,IACtD;AAAA,EACF;AACF;AAQO,SAAS,yBAAyB,OAAO,KAAK;AACF,EAAC,MAAM,SAAS,EAAG,MAAM;AAC5E;AAEA,IAAOH,iBAAQ;;;AC1Of,IAAM,SAAN,cAAqBI,eAAY;AAAA;AAAA;AAAA;AAAA,EAI/B,YAAY,SAAS;AACnB,UAAM,cACJ,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAE5D,UAAwD,oBACpD,QAAQ,sBAAsB,SAC1B,QAAQ,oBACR;AAER,UAAM;AAAA,MACJ,cAAc,QAAQ;AAAA,MACtB,aAAa,QAAQ;AAAA,MACrB,YAAY,IAAc,QAAQ,UAAU;AAAA,IAC9C,CAAC;AAMD,SAAK,OAAO,QAAQ;AAMpB,SAAK,eAAe,QAAQ;AAM5B,SAAK,SAAS,IAAI;AAAA,MAChB,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,sBAAsB,GAAG,CAAC;AAAA,IAC5B;AAMA,SAAK,aAAa,QAAQ,YAAY,QAAQ,YAAY;AAE1D,SAAK,OAAO;AAAA,MACV,kBAAU;AAAA,MACV,KAAK,kBAAkB,KAAK,IAAI;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB,QAAQ,YAAY,YAAY,YAAY;AAC3D,QAAI,WAAW,QAAQ,KAAK,OAAO,UAAU,CAAC,GAAG;AAC/C,aAAO,KAAK;AAAA,IACd;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,KAAK;AACrB,QAAI,KAAK,OAAO,SAAS,KAAK,mBAAW,QAAQ;AAC/C,YAAM,cAAc,KAAK,OAAO,UAAU;AAC1C,YAAM,QAAQ,KAAK,OAAO,SAAS;AACnC,UAAI,YAAY;AAChB,UAAI,KAAK,YAAY;AACnB,qBAAa,KAAK,WAAW,CAAC;AAC9B,sBAAc,KAAK,WAAW,CAAC;AAAA,MACjC,OAAO;AACL,qBAAa,MAAM;AACnB,sBAAc,MAAM;AAAA,MACtB;AACA,YAAM,cAAc,SAAS,WAAW;AACxC,YAAM,eAAe,UAAU,WAAW;AAC1C,YAAM,cAAc,cAAc;AAClC,YAAM,cAAc,eAAe;AACnC,UAAI,cAAc;AAClB,UAAI,eAAe;AACnB,UAAI,cAAc,aAAa;AAC7B,sBAAc,KAAK,MAAM,cAAc,WAAW;AAAA,MACpD,OAAO;AACL,uBAAe,KAAK,MAAM,eAAe,WAAW;AAAA,MACtD;AACA,UAAI,gBAAgB,cAAc,iBAAiB,aAAa;AAC9D,cAAM,UAAU,sBAAsB,aAAa,YAAY;AAC/D,YAAI,CAAC,KAAK,eAAe,GAAG;AAC1B,kBAAQ,wBAAwB;AAAA,QAClC;AACA,cAAM,SAAS,QAAQ;AACvB,gBAAQ;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AACA,aAAK,OAAO,SAAS,MAAM;AAAA,MAC7B;AAAA,IACF;AACA,UAAM,kBAAkB,GAAG;AAAA,EAC7B;AACF;AAEA,IAAO,sBAAQ;", "names": ["sourceState", "Image_default", "Image_default", "extent", "resolution", "pixelRatio", "Image_default"]}