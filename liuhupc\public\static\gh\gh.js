! function (e, t) {
    "object" == typeof exports && "undefined" != typeof module ? t(exports) : "function" == typeof define && define.amd ? define(["exports"], t) : t((e = "undefined" != typeof globalThis ? globalThis : e || self).gh = {})
}(this, function (exports) {
    "use strict";
    var showobj = Object.freeze({
        'animation': false,
        'baseLayerPicker': false,
        'fullscreenButtion': false,
        'vrBotton': false,
        'geocoder': false,
        'homeButton': false,
        'infoBox': false,
        'sceneModePicker': false,
        'selectionIndicator': false,
        'timeline': false,
        'navigationHelpButton': false,
        'navigationInstructionsInitiallyVisible': false,
        'scene3DOnly': false,
        'useDefaultRenderLoop': true,
        'showRenderLoopErrors': false,
        'useBrowserRecommendedResolution': true,
        'automaticallyTrackDataSourceClocks': true,
        'orderIndependentTranslucency': true,
        'projectionPicker': false
    });
    let subdomains = ['0', '1', '2', '3', '4', '5', '6', '7'],
        terrbox = Object.freeze({
            'shouldAnimate': true,
            'skyBox': undefined,
            'skyAtmosphere': undefined,
            'shadows': false,
            'terrainShadows': Cesium.ShadowMode.ENABLED,
            'sceneMode': Cesium.SceneMode.SCENE3D,
            'imageryProvider': new Cesium.UrlTemplateImageryProvider({
                'url': 'https://t{s}.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=9e624bfed46a72f99909f98dbef14c99',
                'subdomains': subdomains,
                'tilingScheme': new Cesium.WebMercatorTilingScheme(),
                'maximumLevel': 18
            })
        }),
        sun = undefined,
        moon = undefined,
        TileCoordinatesImageryLayer = undefined,
        gridImageryLayer = undefined,
        setting = Object.freeze({
            'MAP_3D': Symbol(),
            'MAP_2D': Symbol(),
            'LIGHTING_ENABLE': Symbol(),
            'LIGHTING_DISABLE': Symbol(),
            'GLOBE_ENABLE': Symbol(),
            'GLOBE_DISABLE': Symbol(),
            'SHADOWS_ENABLE': Symbol(),
            'SHADOWS_DISABLE': Symbol(),
            'FOG_ENABLE': Symbol(),
            'FOG_DISABLE': Symbol(),
            'ATMOSPHERE_ENABLE': Symbol(),
            'ATMOSPHERE_DISABLE': Symbol(),
            'GALAXY_ENABLE': Symbol(),
            'GALAXY_DISABLE': Symbol(),
            'GLOBE_TRANSLUCENCY_ENABLE': Symbol(),
            'GLOBE_TRANSLUCENCY_DISABLE': Symbol(),
            'ANIMATE_ENABLE': Symbol(),
            'ANIMATE_DISABLE': Symbol(),
            'TILE_COOR_ENABLE': Symbol(),
            'TILE_COOR_DISABLE': Symbol(),
            'GRID_ENABLE': Symbol(),
            'GRID_DISABLE': Symbol(),
            'T_SHADOWS_ENABLE': 999,
            'T_SHADOWS_DISABLE': 999,
            'UNDERGROUND_ENABLE': Symbol(),
            'UNDERGROUND_DISABLE': Symbol()
        });

    function fogHandler(value) {
        let density = value ? value : 0.0002;
        exports._scene.fog.enabled = true;
        if (Object.prototype.toString.call(density) === '[object Number]') {
            exports._scene.fog.density = density;
            return density
        }
        console.log('error: density must be number')
    }

    function translucencyHandler(translucency) {
        translucency = translucency ? translucency : 0.5;
        exports._globe.translucency.enabled = true;
        if (Object.prototype.toString.call(translucency) === '[object Number]') {
            exports._globe.translucency.frontFaceAlpha = translucency;
            exports._globe.translucency.backFaceAlpha = translucency;
            return translucency
        } else {
            console.log('error: translucency number between 0.0-1.0')
        }

    }

    function tile_coor_enableHandler() {
        let tileCoordinatesImageryProvider = {
            'color': Cesium.Color.fromCssColorString('rgba(0, 0, 0, 1)')
        };
        if (!TileCoordinatesImageryLayer) {
            TileCoordinatesImageryLayer = exports._viewer.imageryLayers.addImageryProvider(new Cesium.TileCoordinatesImageryProvider(tileCoordinatesImageryProvider))
        } else {
            console.log('tile coordinate already exists')
        }

    }

    function tile_coor_disableHandler() {
        if (TileCoordinatesImageryLayer) {
            exports._viewer.imageryLayers.remove(TileCoordinatesImageryLayer, true)
            TileCoordinatesImageryLayer = undefined
        } else {
            console.log('error:no xyz coordinate')
        }
    }

    function grid_enableHandler() {
        let gridImageryProvider = {
            'color': Cesium.Color.fromCssColorString('rgba(0, 0, 0, 1)'),
            'glowColor': Cesium.Color.fromCssColorString('rgba(0, 0, 0, 1)'),
            'glowWidth': 6,
            'backgroundColor': Cesium.Color.fromCssColorString('rgba(0, 0, 0, 0)')
        };
        if (!gridImageryLayer) {
            gridImageryLayer = exports._viewer.imageryLayers.addImageryProvider(new Cesium.GridImageryProvider(gridImageryProvider))
        } else {
            console.log('grid coordinate already exists')
        }

    }

    function grid_disableHandler() {
        if (gridImageryLayer) {
            exports._viewer.imageryLayers.remove(gridImageryLayer, true);
            gridImageryLayer = undefined
        } else {
            console.log('error: no grid coordinate')
        }
    }

    function setHandler(prop) {
        propvalueobj = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
        switch (prop) {
            case setting.MAP_3D:
                exports._scene.mode = Cesium.SceneMode.SCENE3D;
                return true;
            case setting.MAP_2D:
                exports._scene.mode = Cesium.SceneMode.SCENE2D;
                return true;
            case setting.LIGHTING_ENABLE:
                exports._globe.enableLighting = true;
                exports._globe.dynamicAtmosphereLighting = true;
                return true;
            case setting.LIGHTING_DISABLE:
                exports._globe.enableLighting = false;
                return true;
            case setting.GLOBE_ENABLE:
                exports._globe.show = true;
                return true;
            case setting.GLOBE_DISABLE:
                exports._globe.show = false;
                return true;
            case setting.SHADOWS_ENABLE:
                exports._globe.shadows = Cesium.ShadowMode.ENABLED;
                return true;
            case setting.SHADOWS_DISABLE:
                exports._globe.shadows = Cesium.ShadowMode.DISABLED;
                return true;
            case setting.FOG_ENABLE:
                return fogHandler(propvalueobj.density);
            case setting.FOG_DISABLE:
                exports._scene.fog.enabled = false;
                return true;
            case setting.ATMOSPHERE_ENABLE:
                exports._scene.skyAtmosphere.show = true;
                exports._globe.showGroundAtmosphere = true;
                return true;
            case setting.ATMOSPHERE_DISABLE:
                exports._scene.skyAtmosphere.show = false;
                exports._globe.showGroundAtmosphere = false;
                return true;
            case setting.GALAXY_ENABLE:
                exports._scene.sun = sun = new Cesium.Sun();
                exports._scene.moon = moon = new Cesium.Moon();
                return true;
            case setting.GRID_DISABLE:
                exports._scene.sun = sun && sun.destroy();
                exports._scene.moon = moon && moon.destroy();
                return true;
            case setting.GLOBE_TRANSLUCENCY_ENABLE:
                return translucencyHandler(propvalueobj.translucency);
            case setting.GLOBE_TRANSLUCENCY_DISABLE:
                exports._globe.translucency.enabled = false;
                return true;
            case setting.ANIMATE_ENABLE:
                exports._viewer.clock.shouldAnimate = true;
                return true;
            case setting.ANIMATE_DISABLE:
                exports._viewer.clock.shouldAnimate = false;
                return true;
            case setting.TILE_COOR_ENABLE:
                return tile_coor_enableHandler();
            case setting.TILE_COOR_DISABLE:
                return tile_coor_disableHandler();
            case setting.GRID_ENABLE:
                return grid_enableHandler();
            case setting.GRID_DISABLE:
                return grid_disableHandler();
            case setting.UNDERGROUND_ENABLE:
                exports._scene.screenSpaceCameraController.enableCollisionDetection = true;
                return true;
            case setting.UNDERGROUND_DISABLE:
                exports._scene.screenSpaceCameraController.enableCollisionDetection = false;
                return true;
            default:
                console.log('error: wrong setting');
                return false;
        }
    }
    var n, unit8 = new Uint8Array(16);

    function getRandomValuesEnable() {
        if (!n) {
            n = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto) || typeof msCrypto !== 'undefined' && typeof msCrypto.getRandomValues === 'function' && msCrypto.getRandomValues.bind(msCrypto);
            if (!n) throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');
        }
        return n(unit8);
    }
    var reg = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;

    function testuuid(random) {
        return typeof random === 'string' && reg.test(random);
    }
    var number216 = [];
    for (let index = 0; index < 256; ++index) {
        number216.push((index + 256).toString(16).substr(1));
    }

    function stringifiedUUId(randomnum) {
        var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0,
            random = (number216[randomnum[offset + 0]] + number216[randomnum[offset + 1]] + number216[randomnum[offset + 2]] + number216[randomnum[offset + 3]] + '-' + number216[randomnum[offset + 4]] + number216[randomnum[offset + 5]] + '-' + number216[randomnum[offset + 6]] + number216[randomnum[offset + 7]] + '-' + number216[randomnum[offset + 8]] + number216[randomnum[offset + 9]] + '-' + number216[randomnum[offset + 10]] + number216[randomnum[offset + 11]] + number216[randomnum[offset + 12]] + number216[randomnum[offset + 13]] + number216[randomnum[offset + 14]] + number216[randomnum[offset + 15]]).toLowerCase();
        if (!testuuid(random)) throw TypeError('Stringified UUID is invalid');
        return random;
    }

    function getRandom() {
        var randomvalue = getRandomValuesEnable();
        randomvalue[6] = randomvalue[6] & 15 | 64;
        randomvalue[8] = randomvalue[8] & 63 | 128;

        return stringifiedUUId(randomvalue);
    }

    function isNumber(_0x3cba7e, _0x29c950, _0x272abb) {
        if (Object.prototype.toString.call(_0x3cba7e) !== '[object Number]' || Object.prototype.toString.call(_0x29c950) !== '[object Number]' || Object.prototype.toString.call(_0x272abb) !== '[object Number]') {
            console.log('error: lon and lat must be number')
            return;
        }
        else return _0x3cba7e < -0xb4 || _0x3cba7e > 0xb4 || _0x29c950 < -0x5a || _0x29c950 > 0x5a ? (console.log('error:  -180.0  < lon  < 180.0 and  -90.0  < lat  < 90.0'), false) : true;
    }

    function checkOptions(prop) {
        if (Object.prototype.toString.call(prop) !== '[object Object]') {
            console.log('error: options must be object')
            return false
        } else {
            return true
        }

    }



    function getDatasourceid() {
        return getRandom();
    }

    function loadpoijson(url) {
        return new Promise(function (resolve, reject) {
            let httprequest = new XMLHttpRequest();
            httprequest.open('get', url);
            httprequest.send(null);
            httprequest.onload = function () {
                if (httprequest.status === 200) {
                    try {
                        let res = JSON.parse(httprequest.responseText);
                        resolve(res);
                    } catch (err) {
                        console.log('error: file not json')
                        reject(false)
                    }
                } else {
                    console.log('error: network connect error')
                    reject(false);
                }
            };
        });
    }
    var o_fires = {},
        imagery_feature = {},
        maplayer = Object.freeze({
            'RASTER': 'RASTER',
            'VECTOR': 'VECTOR',
            'POI3D': 'POI3D',
            'LABEL3D': 'LABEL3D',
            'OBJ3D': 'OBJ3D',
            'TILES3D': 'TILES3D'
        }),
        layerfordatasourceId = {},
        datasourceForLayers = {};

    function addPrimitive(layname) {
        datasourceForLayers[layname] = new Cesium.PrimitiveCollection();
        exports._scene.primitives.add(datasourceForLayers[layname]);
    }

    function raster(layername) {
        datasourceForLayers[layername] = [];
    }

    function vector(layername) {
        console.log(layername,'00000000000000000000000')
        datasourceForLayers[layername] = [];
    }

    function poi3d(layername) {
        datasourceForLayers[layername] = [];
    }

    function createLayer(options) {
        let layername = options.name,
            layertype = options.type;
        if (!layername || !layertype) {
            console.log('error: no name or type given')
            return
        }
        if (datasourceForLayers.hasOwnProperty(layername) || this.layers.hasOwnProperty(layername)) {
            console.log('error: layer name already exists')
            return;
        }
        switch (layertype) {
            case maplayer.OBJ3D:
                addPrimitive(layername);
                break;
            case maplayer.TILES3D:
                addPrimitive(layername);
                break;
            case maplayer.RASTER:
                raster(layername);
                break;
            case maplayer.VECTOR:
                vector(layername);
                break;
            case maplayer.POI3D:
                poi3d(layername);
                break;
            default:
                console.log('error: layer type wrong')
                return false;
        }
        this.layers[layername] = {
            'type': layertype.description,
            'features': []
        }
        return layername;
    }

    function hideLayer(layername) {
        if (!this.layers[layername]) {
            console.log('error: no layer name')
            return;
        }
        if (Object.prototype.toString.call(datasourceForLayers[layername]) === '[object Array]') {
            for (let index = 0; index < datasourceForLayers[layername].length; index++) {
                datasourceForLayers[layername][index].show = false;
            }
        } else datasourceForLayers[layername].show = false;
        return true;
    }

    function showLayer(layername) {
        if (!this.layers[layername]) {
            console.log('error: no layer name')
            return;
        }
        if (Object.prototype.toString.call(datasourceForLayers[layername]) === '[object Array]') {
            for (let index = 0; index < datasourceForLayers[layername].length; index++) {
                datasourceForLayers[layername][index].show = true;
            }
        } else datasourceForLayers[layername].show = true;
        return true;
    }

    function layerhandler(that) {
        this.create = createLayer.bind(that);
        this.delete = undefined;
        this.show = showLayer.bind(that);
        this.hidden = hideLayer.bind(that);
        this.clear = undefined;
    }
    var t_map = Object.freeze({
            'T_SATELLITE': Symbol('SATELLITE'),
            'T_STREET': Symbol('STREET'),
            'T_LABLE_NAME': Symbol('LABLE_NAME'),
            'T_LABLE_NATURE': Symbol('LABLE_NATURE'),
            'T_SHADING': Symbol('SHADING'),
            'T_BOUNDARY': Symbol('BOUNDARY')
        }),
        feature = Object.freeze({
            'POINT': 'POINT',
            'LINE': 'LINE',
            'POLYGON': 'POLYGON'
        });

    function terrainLoader() {
        let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
        if (!checkOptions(options)) return false;
        var terrain = options.terrain || 'default',
            waterMask = options.waterMask || false,
            vertexNormals = options.vertexNormals || false;
        if (terrain == 'default') return exports._viewer.terrainProvider = Cesium.createWorldTerrain({
            'requestWaterMask': waterMask,
            'requestVertexNormals': vertexNormals
        }), true;
        return false;
    }

    function tiandituHandler(options) {
        if (!checkOptions(options)) return false;
        var layer = options.layer || 'tianditu',
            maptype = options.type || t_map.T_SATELLITE,
            key = options.key || '9e624bfed46a72f99909f98dbef14c99';
        if (!maptype || !key || !layer) {
            console.log('error: no name map type or key')
            return;
        }
        if (datasourceForLayers.hasOwnProperty(layer) || this.layers.hasOwnProperty(layer)) {
            console.log('error: layer name already exists')
            return;
        }
        let prefix = undefined;
        switch (maptype) {
            case t_map.T_SATELLITE:
                prefix = 'img_w';
                break;
            case t_map.T_STREET:
                prefix = 'vec_w';
                break;
            case t_map.T_LABLE_NAME:
                prefix = 'cva_w';
                break;
            case t_map.T_LABLE_NATURE:
                prefix = 'cta_w';
                break;
            case t_map.T_SHADING:
                prefix = 'ter_w';
                break;
            case t_map.T_BOUNDARY:
                prefix = 'ibo_w';
                break;
            default:
                console.log('error:map type wrong');
                return false;
        }
        let urlprefix = 'https://t{s}.tianditu.gov.cn/',
            subdomains = ['0', '1', '2', '3', '4', '5', '6', '7'],
            randomsourceId = getDatasourceid(),
            imageryLayerLength = exports._scene.imageryLayers.length,
            imgMap = new Cesium.UrlTemplateImageryProvider({
                'url': urlprefix + 'DataServer?T=' + prefix + '&x={x}&y={y}&l={z}&tk=' + key,
                'subdomains': subdomains,
                'tilingScheme': new Cesium.WebMercatorTilingScheme(),
                'maximumLevel': 18
            });
        exports._scene.imageryLayers.addImageryProvider(imgMap);
        datasourceForLayers[layer] = exports._scene.imageryLayers;
        datasourceForLayers[randomsourceId] = exports._scene.imageryLayers.get(imageryLayerLength);
        layerfordatasourceId[layer] = layer;
        this.layers[layer] = {
            'type': maptype.description,
            'features': [randomsourceId]
        };
        return randomsourceId;
    }

    function tiles3dHandler(options) {
        if (!checkOptions(options)) return false;
        var url = options.url,
            layername = options.layer;
        if (!url || !layername) {
            console.log('error: no layer name or url')
            return
        }
        if (!datasourceForLayers.hasOwnProperty(layername) || !this.layers.hasOwnProperty(layername)) {
            console.log('error: no given layer')
            return;
        }
        var height = options.height || 20,
            lon = options.lon,
            lat = options.lat,
            x = options.x,
            y = options.y,
            z = options.z,
            scale = options.scale,
            classify = options.classify,
            randomsourceId = getDatasourceid();
        datasourceForLayers[randomsourceId] = undefined
        if (classify) {
            datasourceForLayers[randomsourceId] = new Cesium.Cesium3DTileset({
                url: url,
                classificationType: Cesium.ClassificationType.CESIUM_3D_TILE
            });
            datasourceForLayers[randomsourceId].style = new Cesium.Cesium3DTileStyle({
                color: 'rgba(255, 255, 255, 0.01)'
            })
        } else {
            datasourceForLayers[randomsourceId] = new Cesium.Cesium3DTileset({
                url: url,
                show: true,
                shadows: Cesium.ShadowMode.ENABLED,
                maximumMemoryUsage: 128,
                dynamicScreenSpaceError: true,
                dynamicScreenSpaceErrorDensity: 0.00278,
                dynamicScreenSpaceErrorFactor: 4,
                dynamicScreenSpaceErrorHeightFalloff: 0.25
            });
        }
        datasourceForLayers[layername].add(datasourceForLayers[randomsourceId]);
        layerfordatasourceId[randomsourceId] = layername;
        this.layers[layername].features.push(randomsourceId);
        datasourceForLayers[randomsourceId].readyPromise.then(function (tileset) {
           let center = tileset.boundingSphere,
                cartographic = Cesium.Cartographic.fromCartesian(center.center), //x.y。z笛卡尔转化为经纬度
            surface = Cesium.Cartesian3.fromRadians(cartographic.longitude, cartographic.latitude, 0),
                offset = Cesium.Cartesian3.fromRadians(cartographic.longitude, cartographic.latitude, height),
                translation = Cesium.Cartesian3.subtract(offset, surface, new Cesium.Cartesian3());
            tileset.modelMatrix = Cesium.Matrix4.fromTranslation(translation); //tileset 自身的变形max
            let Matrix4arr = new Array(16).fill(1);
            let rotateX_matrix = Cesium.Matrix4.fromArray(Matrix4arr);
            if (lon && lat && isNumber(lon, lat, height)) {
                let enuposition = Cesium.Cartesian3.fromDegrees(lon, lat, height); //传过来的经纬度转化为下全局坐标系x,y，z笛卡尔坐标
                rotateX_matrix = Cesium.Transforms.eastNorthUpToFixedFrame(enuposition); //建立从局部到世界的坐标矩阵
            }
            console.log(scale,x,y,z,'33333333')
            if (scale && scale !== 1) {

                Cesium.Matrix4.multiply(rotateX_matrix, Cesium.Matrix4.fromUniformScale(scale), rotateX_matrix)
            }
            if (x && x !== 0) {
                //表示绕x周旋转
              
                Cesium.Matrix4.multiply(rotateX_matrix, Cesium.Matrix4.fromRotationTranslation(Cesium.Matrix3.fromRotationX(Cesium.Math.toRadians(x))), rotateX_matrix);

            }
            if (y && y !== 0) {
           
                Cesium.Matrix4.multiply(rotateX_matrix, Cesium.Matrix4.fromRotationTranslation(Cesium.Matrix3.fromRotationY(Cesium.Math.toRadians(y))), rotateX_matrix);


            }
            if (z && z !== 0) {

           
                Cesium.Matrix4.multiply(rotateX_matrix, Cesium.Matrix4.fromRotationTranslation(Cesium.Matrix3.fromRotationZ(Cesium.Math.toRadians(z))), rotateX_matrix);

            }
            if (rotateX_matrix.equals(Cesium.Matrix4.fromArray(Matrix4arr))) {
                console.log('33333333')
            }
            else{
                console.log(rotateX_matrix,Matrix4arr,tileset.root.transform)
                tileset.root.transform = rotateX_matrix
            }


        })

        return randomsourceId;
    }

    function gltfHandler(options) {
        if (!checkOptions(options)) return false;
        var url = options.url;
        console.log(url);
        var model = exports._scene.primitives.add(Cesium.Model.fromGltf({
            url: url,
            show: true,
            scale: 1,
            modelMatrix: Cesium.Transforms.eastNorthUpToFixedFrame(Cesium.Cartesian3.fromDegrees(110.62898254394531, 40.02804946899414, 6))
        }));

        exports._viewer.zoomTo(model);
        return model;
    }

    function geojsonHandler(options) {
        if (!checkOptions(options)) return false;
        var url = options.url,
            layername = options.layer;
        if (!url || !layername) {
            console.log('error:no layer name or url')
            return
        }
        if (!datasourceForLayers.hasOwnProperty(layername) || !this.layers.hasOwnProperty(layername)) {
            console.log('error: no given layer')
            return;
        }
        var featuretype = options.type;
        if (!featuretype) {
            console.log('error:no feature type given')
            return
        }
        var clampToGround = options.clampToGround || false,
            stroke = options.stroke || 'rgba(255, 234, 92, 0.8)',
            strokeWidth = options.strokeWidth || 5,
            glowPower = options.glowPower || 0,
            railways = options.railways || 0,
            fill = options.fill || false,
            outlineWidth = options.outlineWidth || 0,
            outlineColor = options.outlineColor || 'rgba(255, 234, 0, 0.8)',
            extrudedHeight = options.extrudedHeight || 0,
            height = options.height || 0,
            label = options.label || false,
            labelOffset = options.labelOffset || [0, 0],
            labelColor = options.labelColor || 'rgba(255, 255, 255, 1)',
            labelSize = options.labelSize || 0.5,
            labelBackgroundColor = options.labelBackgroundColor,
            showBackground = true;
        if (labelBackgroundColor == undefined) {
            labelBackgroundColor = 'rgba(0, 0, 0, 0.5)';
            showBackground = false
        }
        var labelNear = options.labelNear || 0,
            labelNearScale = options.labelNearScale || 1,
            labelFar = options.labelFar || 0x9c40,
            labelFarScale = options.labelFarScale || 0.75,
            randomsourceId = getDatasourceid(),
            datasourcepromise = Cesium.GeoJsonDataSource.load(url, {
                clampToGround: clampToGround
            });
            datasourcepromise.then(function (dataSource) {
            let entities = dataSource.entities.values;
            if (featuretype === feature.LINE) {
                for (let index = 0; index < entities.length; index++) {
                    var entity = entities[index];
                    entity.polyline.width = strokeWidth;
                    if (railways) {
                        entity.polyline.material = new Cesium.StripeMaterialProperty({
                            orientation: Cesium.StripeOrientation.VERTICAL,
                            repeat: railways
                        });
                    } else {
                        if (glowPower) {
                            entity.polyline.material = new Cesium.PolylineGlowMaterialProperty({
                                color: Cesium.Color.fromCssColorString(stroke),
                                glowPower: glowPower,
                                taperPower: 1
                            })
                        } else {
                            entity.polyline.material = new Cesium.PolylineOutlineMaterialProperty({
                                color: Cesium.Color.fromCssColorString(stroke),
                                outlineColor: Cesium.Color.fromCssColorString(outlineColor)
                            });
                        }
                    }
                }
            }
            if (featuretype === feature.POLYGON)
                for (let index = 0; index < entities.length; index++) {
                    console.log(index)
                    let entity = entities[index];
                    if (label) {
                        // 得到每块多边形的坐标集合
                        var polyPositions = entity.polygon.hierarchy.getValue(Cesium.JulianDate.now()).positions;
                        // 根据坐标集合构造BoundingSphere获取中心点坐标
                        var polyCenter = Cesium.BoundingSphere.fromPoints(polyPositions).center;
                        // 将中心点拉回到地球表面
                        polyCenter = Cesium.Ellipsoid.WGS84.scaleToGeodeticSurface(polyCenter);
                        entity.position = polyCenter;
                        entity.label = {
                            'show': true,
                            'text': entity[label],
                            'fillColor': Cesium.Color.fromCssColorString(labelColor),
                            'font': 'normal 32px MicroSoft YaHei',
                            'scale': labelSize,
                            'horizontalOrigin': Cesium.HorizontalOrigin.LEFT,
                            'verticalOrigin': Cesium.VerticalOrigin.BOTTOM,
                            'showBackground': showBackground,
                            'backgroundColor': Cesium.Color.fromCssColorString(labelBackgroundColor),
                            'backgroundPadding': new Cesium.Cartesian2(20, 20),
                            'pixelOffset': new Cesium.Cartesian2(labelOffset[0], labelOffset[1]),
                            'distanceDisplayCondition': new Cesium.DistanceDisplayCondition(labelNear, labelFar),
                            'scaleByDistance': new Cesium.NearFarScalar(labelNear, labelNearScale, labelFar, labelFarScale)
                        };
                    }
                    entity.polygon.extrudedHeight = extrudedHeight;
                    entity.polygon.height = height;
                    if (fill) {
                        entity.polygon.fill = true;
                        entity.polygon.material = Cesium.Color.fromCssColorString(fill)
                    } else {
                        entity.polygon.fill = false
                    }
                    if (outlineWidth) {
                        entity.polygon.outline = true;
                        entity.polygon.outlineWidth = outlineWidth;
                        entity.polygon.outlineColor = Cesium.Color.fromCssColorString(outlineColor)
                    }
                }
            datasourceForLayers[layername].push(dataSource);
            exports._viewer.dataSources.add(dataSource);
            datasourceForLayers[randomsourceId] = dataSource;
            layerfordatasourceId[randomsourceId] = layername;
        });
        this.layers[layername].features.push(randomsourceId);
        return randomsourceId;
    }

    function poiFromGeojsonHandler(options) {
        let that = this;
        if (!checkOptions(options)) return false;
        var url = options.url,
            layername = options.layer;
        if (!url || !layername) {
            console.log('error: no layer name or url')
            return
        }
        console.log(options.icon)
        if (!datasourceForLayers.hasOwnProperty(layername) || !this.layers.hasOwnProperty(layername)) {
            console.log('error: no given layer')
            return;
        }
        var id = options.id,
            name = options.name,
            height = options.height || 0,
            icon = options.icon,
            near = options.near || 0,
            nearScale = options.nearScale || 1,
            far = options.far || 1000000,
            farScale = options.farScale || 0.2,
            labelHeight = options.labelHeight || -100;

        var labelColor = options.labelColor || 'rgba(162,172,255,1)',
            textColor = options.textColor || 'rgba(162,172,255,1)',
            labelSize = options.labelSize || 1.4,
            labelBackgroundColor = options.labelBackgroundColor || 'rgba(13,22,51,0.8)';

        var datasourceid = getDatasourceid();
        datasourceForLayers[datasourceid] = new Cesium.CustomDataSource(datasourceid);
        loadpoijson(url).then(function (res) {
            let features = res.features;
            for (let index = 0; index < features.length; index++) {
                var feature = features[index];
                if (feature.type !== 'Feature') continue;
                var geometry = feature.geometry;
                if (geometry.type !== 'Point') continue;
                var coordinates = geometry.coordinates,
                    entityname = '@' + layername + '/' + feature.properties[id];
                datasourceForLayers[entityname] = datasourceForLayers[datasourceid].entities.add({
                    'id': feature.properties[id],
                    'lon': coordinates[0],
                    'lat': coordinates[1],
                    'name': feature.properties[name],
                    'type': feature.properties.type,
                    'info': feature.properties.info,
                    'position': Cesium.Cartesian3.fromDegrees(coordinates[0], coordinates[1], height),
                    'billboard': {
                        'image': icon,
                        'scaleByDistance': new Cesium.NearFarScalar(near, nearScale, far, farScale),
                        'pixelOffsetScaleByDistance': new Cesium.NearFarScalar(near, 0, far, 0),
                        'disableDepthTestDistance': Number.POSITIVE_INFINITY,
                        'distanceDisplayCondition': new Cesium.DistanceDisplayCondition(near, far)
                    },
                    'label': {
                        'text': feature.properties[name],
                        'font': '14px SectionHeader',
                        'style': Cesium.LabelStyle.FILL,
                        'scale': labelSize,
                        'textColor': Cesium.Color.fromCssColorString(textColor),
                        'fillColor': Cesium.Color.fromCssColorString(labelColor),
                        'showBackground': true,
                        'backgroundColor': Cesium.Color.fromCssColorString(labelBackgroundColor),
                        'backgroundPadding': new Cesium.Cartesian2(7, 5),
                        'verticalOrigin': Cesium.VerticalOrigin.TOP,
                        'horizontalOrigin': Cesium.HorizontalOrigin.CENTER,
                        'scaleByDistance': new Cesium.NearFarScalar(1000, 1, 200, 1.5),
                        'pixelOffset': new Cesium.Cartesian2(0, labelHeight),
                        'distanceDisplayCondition': new Cesium.DistanceDisplayCondition(near, far)
                    }
                });
                that.layers[layername].features.push(feature.properties[entityname]);
            }
            exports._viewer.dataSources.add(datasourceForLayers[datasourceid]);
            datasourceForLayers[layername].push(datasourceForLayers[datasourceid]);
            layerfordatasourceId[datasourceid] = layername;
        })
        return datasourceid;
    }

    function imageHandler(options) {
        if (!checkOptions(options)) return false;
        var url = options.url,
            layername = options.layer;
        if (!url) {
            console.log('error: no url')
            return;
        }
        if (!datasourceForLayers.hasOwnProperty(layername) || !this.layers.hasOwnProperty(layername)) {
            console.log('error: no given layer')
            return;
        }
        var west = options.west,
            south = options.south,
            east = options.east,
            north = options.north;
        if (!west || !south || !east || !north) {
            console.log('error: no rectangle')
            return
        }
        if (isNumber(west, south) && isNumber(east, north)) {
            if (west < east && south < north) {
                var randomsourceId = getDatasourceid(),
                    length = datasourceForLayers[layername].length;
                datasourceForLayers[layername].addImageryProvider(new Cesium.SingleTileImageryProvider({
                    'url': url,
                    'rectangle': Cesium.Rectangle.fromDegrees(west, south, east, north)
                }));
                datasourceForLayers[randomsourceId] = datasourceForLayers[layername].get(length);
                layerfordatasourceId[randomsourceId] = layername;
                this.layers[layername].features.push(randomsourceId);
                return randomsourceId;
            } else {
                console.log('error: values west < east and sourth < north')
                return;
            }
        } else return false;
    }

    function tmsHandler(options) {
        if (!checkOptions(options)) return false;
        var url = options.url,
            layername = options.layer;
        if (!url) {
            console.log('error: no url')
            return;
        }
        if (!datasourceForLayers.hasOwnProperty(layername) || !this.layers.hasOwnProperty(layername)) {
            console.log('error: no given layer')
            return;
        }
        var randomsourceId = getDatasourceid(),
            imageryLayersLength = exports._scene.imageryLayers.length;
        exports._scene.imageryLayers.addImageryProvider(new Cesium.TileMapServiceImageryProvider({
            'url': url
        }));
        datasourceForLayers[layername].push(exports._scene.imageryLayers.get(imageryLayersLength));
        datasourceForLayers[randomsourceId] = exports._scene.imageryLayers.get(imageryLayersLength);
        layerfordatasourceId[randomsourceId] = layername;
        this.layers[layername].features.push(randomsourceId);
        return randomsourceId;
    }
    function unloadHandler(datasourceId) {
        console.log(datasourceId)
        if (datasourceForLayers[datasourceId]) {
            if (layerfordatasourceId[datasourceId]) {
                if (datasourceForLayers[datasourceId] instanceof Cesium.GeoJsonDataSource || datasourceForLayers[datasourceId] instanceof Cesium.CustomDataSource) {
                    datasourceForLayers[datasourceId].entities.removeAll()
                } else {
                    datasourceForLayers[layerfordatasourceId[datasourceId]].remove(datasourceForLayers[datasourceId])
                }
                this.layers[layerfordatasourceId[datasourceId]].features = this.layers[layerfordatasourceId[datasourceId]].features.filter(function (item) {
                    return item != datasourceId;
                });
                delete datasourceForLayers[datasourceId];
                delete layerfordatasourceId[datasourceId]
                console.log('yyyyyyyyyyy')
            } else {
                delete layerfordatasourceId[datasourceId];
                console.log('ttttttttt')
                console.log('warn: no this data')
            }
        } else {
            console.log('error: wrong id')
        }
    };

    function loaderhandler(that) {
        this.terrain = terrainLoader.bind(that);
        this.tianditu = tiandituHandler.bind(that);
        this.tiles3d = tiles3dHandler.bind(that);
        this.geojson = geojsonHandler.bind(that),
            this.poiFromGeojson = poiFromGeojsonHandler.bind(that);
        this.image = imageHandler.bind(that);
        this.tms = tmsHandler.bind(that);
        this.gltf = gltfHandler.bind(that);
        this.unload = unloadHandler.bind(that);
    }
    var operation = Object.freeze({
        'LEFT_CLICK': Symbol('GET_PAO'),
        'LEFT_DOUBLE_CLICK': Symbol('LEFT_DOUBLE_CLICK'),
        'RIGHT_CLICK': Symbol('RIGHT_CLICK'),
        'MIDDLE_CLICK': Symbol('MIDDLE_CLICK'),
        'MOUSE_MOVE': Symbol('MOUSE_MOVE'),
        'WHEEL': Symbol('WHEEL')
    });

    function getEventType(eventtype) {
        switch (eventtype) {
            case operation.LEFT_CLICK:
                return Cesium.ScreenSpaceEventType.LEFT_CLICK;
            case operation.LEFT_DOUBLE_CLICK:
                return Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK;
            case operation.RIGHT_CLICK:
                return Cesium.ScreenSpaceEventType.RIGHT_CLICK;
            case operation.MIDDLE_CLICK:
                return Cesium.ScreenSpaceEventType.MIDDLE_CLICK;
            case operation.WHEEL:
                return Cesium.ScreenSpaceEventType.WHEEL;
            default:
                console.log('error: operation type wrong');
                return false;
        }
    }

    function clearHandler(options) {
        if (!checkOptions(options)) return false;
        var type = options.type,
            eventtype = getEventType(type);
        if (eventtype === false) return eventtype;
        if (screenSpaceEventHandler.getInputAction(eventtype))
            screenSpaceEventHandler.removeInputAction(eventtype)
        return true;
    }

    function get_position(options, next) {
        if (Object.prototype.toString.call(next) !== '[object Function]') {
            console.log('error: next must be a function')
            return;
        }
        var that = this;
        if (!checkOptions(options)) return false;
        var type = options.type;
        var eventtype = getEventType(type);
        if (eventtype === false) return;
        screenSpaceEventHandler.setInputAction(function (movement) {
            let longandlat = {},
                ellipsoid = exports._globe.ellipsoid,
                click_position = exports._viewer.camera.pickEllipsoid(movement.position, ellipsoid);
            if (click_position) {
                var carto_position = ellipsoid.cartesianToCartographic(click_position);
                longandlat.latitude = Cesium.Math.toDegrees(carto_position.latitude).toFixed(6);
                longandlat.longitude = Cesium.Math.toDegrees(carto_position.longitude).toFixed(6);
                longandlat.elevation = carto_position.height.toFixed(4);
                that.mapinfo.pao = longandlat;
            }
            next(longandlat);
        }, eventtype);
    }

    function get_pao() {
        let position = carema.position
        let cartographic = exports._globe.ellipsoid.cartesianToCartographic(position); //世界坐标转化为经纬度
        return {
            'lon': Cesium.Math.toDegrees(cartographic.longitude),
            'lat': Cesium.Math.toDegrees(cartographic.latitude),
            'height': cartographic.height,
            'heading': Cesium.Math.toDegrees(carema.heading),
            'pitch': Cesium.Math.toDegrees(carema.pitch),
            'roll': Cesium.Math.toDegrees(carema.roll)
        };
    }
    var pickentity = {
        'tiles3d': {
            'feature': undefined,
            'color': undefined
        },
        'billboard': {
            'feature': undefined,
            'scale': undefined
        }
    };

    function changePickEntity() {
        try {
            if (pickentity.tiles3d.feature != undefined) {
                pickentity.tiles3d.feature.color = pickentity.tiles3d.color
            }
            if (pickentity.billboard.feature != undefined) {
                pickentity.billboard.feature.id.billboard.scale = pickentity.billboard.scale
            }

        } catch (err) {
            pickentity = {
                'tiles3d': {
                    'feature': undefined,
                    'color': undefined
                },
                'billboard': {
                    'feature': undefined,
                    'scale': undefined
                }
            };
        }
    }

    function get_info(options, next) {
        if (!checkOptions(options)) return false;
        if (Object.prototype.toString.call(next) !== '[object Function]') {
            console.log('error: next must be a function')
            return;
        }
        var eventtype = options.type,
            cesiumevent = getEventType(eventtype);
        if (cesiumevent === false) {
            console.log('error: no action or event type')
            return;
        }
        screenSpaceEventHandler.setInputAction(function (click) {
            let pickinfo = undefined,
                pickedfeature = exports._scene.pick(click.position);
            if (Cesium.defined(pickedfeature)) {
                if (Cesium.defined(pickedfeature.id) && Cesium.defined(pickedfeature.id.billboard)) {
                    if (pickentity.billboard.feature != undefined) {
                        if (pickedfeature != pickentity.billboard.feature) {
                            changePickEntity();
                            pickentity.billboard.feature = pickedfeature;
                            pickentity.billboard.scale = pickedfeature.id.billboard.scale;
                            pickedfeature.id.billboard.scale = 1.1
                        }
                    } else {
                        pickentity.billboard.feature = pickedfeature;
                        pickentity.billboard.scale = pickedfeature.id.billboard.scale;
                        pickedfeature.id.billboard.scale = 1.1
                    }
                    pickinfo = {
                        'id': pickedfeature.id.id,
                        'name': pickedfeature.id.name,
                        'type': pickedfeature.id.type,
                        'info': pickedfeature.id.info
                    };
                    next(pickinfo);
                } else {
                    if (pickedfeature instanceof Cesium.Cesium3DTileFeature) {
                        console.log('444444444444444444444')
                        if (pickentity.tiles3d.feature != undefined) {
                            if (pickedfeature != pickentity.tiles3d.feature) {
                                changePickEntity();
                                pickentity.tiles3d.feature = pickedfeature;
                                pickentity.tiles3d.color = pickedfeature.color;
                                pickedfeature.color = Cesium.Color.fromAlpha(Cesium.Color.DODGERBLUE, 0.5)
                            }
                        } else {
                            pickentity.tiles3d.feature = pickedfeature;
                            pickentity.tiles3d.color = pickedfeature.color;
                            pickedfeature.color = Cesium.Color.fromAlpha(Cesium.Color.DODGERBLUE, 0.5)
                        }
                        pickinfo = {};
                        var propertyNames = pickedfeature.getPropertyNames();
                        for (let index = 0; index < propertyNames.length; ++index) {
                            pickinfo[propertyNames[index]] = pickedfeature.getProperty(propertyNames[index]);
                        }
                        next(pickinfo);
                    } else changePickEntity(),
                        next(pickinfo);
                }
            } else {
                changePickEntity()
            }
            next(pickinfo);
        }, cesiumevent)
    }

    function zoomHandler(entityname) {
        hprdata = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},
            height = hprdata.height || undefined,
            heading = hprdata.heading || undefined,
            pitch = hprdata.pitch || undefined;
        height === undefined && (height = 750);
        heading === undefined ? heading = Cesium.Math.toRadians(0) : heading = Cesium.Math.toRadians(heading);
        pitch === undefined ? pitch = Cesium.Math.toRadians(-45) : pitch = Cesium.Math.toRadians(pitch);
        exports._viewer.flyTo(datasourceForLayers[entityname], {
            'offset': new Cesium.HeadingPitchRange(heading, pitch, height)
        });
    }

    function setViewHandler(options) {
        if (!checkOptions(options)) return false;
        var prologue = options.prologue || false,
            direct = options.direct || false,
            lon = options.lon,
            lat = options.lat,
            height = options.height,
            heading = options.heading || 0,
            pitch = options.pitch || 0,
            roll = options.roll || 0,
            flyDuration = options.flyDuration || 3,
            rotateDuration = options.rotateDuration || 7.2;
        rotateDuration = rotateDuration * 1000;//变成毫秒
        var rotation = 3600 / rotateDuration,
            currrotation = 113,
            rotatetimer = 0;
        if (prologue) {
            carema.setView({
                'destination': new Cesium.Cartesian3.fromDegrees(113, 30, 18000000) //设置视角
            });
            var timer = setInterval(function () {
                currrotation = currrotation + rotation;
                if (currrotation >= 180)
                currrotation = -180;
                carema.setView({
                    'destination': Cesium.Cartesian3.fromDegrees(currrotation, 30, 18000000)
                });
                rotatetimer = rotatetimer + 10;
                if (rotatetimer >= rotateDuration) {

                    clearInterval(timer);
                    if(lon != undefined && lat != undefined && height != undefined){
                        
                    direct ? carema.flyTo({
                        'destination': Cesium.Cartesian3.fromDegrees(lon, lat, 50000),
                        'duration': flyDuration * 0.4,
                        'complete': () => {
                            carema.flyTo({
                                'destination': Cesium.Cartesian3.fromDegrees(lon, lat, height),
                                'orientation': {
                                    'heading': Cesium.Math.toRadians(heading),
                                    'pitch': Cesium.Math.toRadians(pitch),
                                    'roll': Cesium.Math.toRadians(roll)
                                },
                                'duration': flyDuration * 0.6
                            });
                        }
                    }) : carema.flyTo({
                        'destination': Cesium.Cartesian3.fromDegrees(lon, lat, height),
                        'orientation': {
                            'heading': Cesium.Math.toRadians(heading),
                            'pitch': Cesium.Math.toRadians(pitch),
                            'roll': Cesium.Math.toRadians(roll)
                        },
                        'duration': flyDuration
                    })
                }
                }
            }, 10);
       
        } else{ carema.flyTo({
            'destination': Cesium.Cartesian3.fromDegrees(lon, lat, height),
            'orientation': {
                'heading': Cesium.Math.toRadians(heading),
                'pitch': Cesium.Math.toRadians(pitch),
                'roll': Cesium.Math.toRadians(roll)
            },
            'duration': flyDuration
        })
    }
    }
    function operatorhandler(that) {
        this.get_position = get_position.bind(that);
        this.get_pao = get_pao.bind(that);
        this.get_info = get_info.bind(that);
        this.clear = clearHandler.bind(that);
        this.set_view = setViewHandler.bind(that);
        this.zoom_to = zoomHandler.bind(that);
    }

    function waterHandler(options) {
        if (!checkOptions(options)) return false;
        var layername = options.layer;
        console.log(layername)
        if (!layername) {
            console.log('error: no layer name given')
            return;
        }

        if (!datasourceForLayers[layername]) {
            console.log('error: no layer named:', layername);
            return;
        }
        var geojson = options.geojson,
            array = options.array,
            height = options.height || 200,
            transparency = options.transparency || 0.4,
            randomsourceId = getDatasourceid(),
            ellipsoidSurfaceAppearance = new Cesium.EllipsoidSurfaceAppearance({
                'material': new Cesium.Material({
                    'fabric': {
                        'type': 'Water',
                        'uniforms': {
                            'normalMap':  './static/Assets/Textures/waterNormals.jpg',
                            'frequency': 500,
                            'animationSpeed': 0.01,
                            'amplitude': 4
                        }
                    }
                }),
                'aboveGround': true,
                'fragmentShaderSource': effecttypes.water_mask(transparency)
            }),
            lonlats = [];
        if (array) {
            lonlats = array;
            datasourceForLayers[randomsourceId] = new Cesium.Primitive({
                'show': true,
                'allowPicking': false,
                'geometryInstances': new Cesium.GeometryInstance({
                    'geometry': new Cesium.PolygonGeometry({
                        'polygonHierarchy': new Cesium.PolygonHierarchy(Cesium.Cartesian3.fromDegreesArrayHeights(lonlats)),
                        'extrudedHeight': height
                    })
                }),
                'appearance': ellipsoidSurfaceAppearance,
                'interleave': true,
                'compressVertices': false
            });
            console.log( datasourceForLayers[layername],datasourceForLayers[randomsourceId])
         
            datasourceForLayers[layername].add(datasourceForLayers[randomsourceId]);
            layerfordatasourceId[randomsourceId] = layername;
            this.layers[layername].features.push(randomsourceId);
            return randomsourceId;
        } else {
            if (geojson) {
                var promise = Cesium.GeoJsonDataSource.load(geojson);

                promise.then((dataSource) => {
                    var entities = dataSource.entities.values;
                    for (let index = 0; index < entities.length; index++) {
                        var entity = entities[index],
                            positions = entity.polygon.hierarchy.getValue().positions;
                        for (let index2 = 0; index2 < positions.length; index2++) {
                            var lonlat = exports._globe.ellipsoid.cartesianToCartographic(positions[index2]),
                                latitude = parseFloat(Cesium.Math.toDegrees(lonlat.latitude).toFixed(6)),
                                longitude = parseFloat(Cesium.Math.toDegrees(lonlat.longitude).toFixed(6)),
                                height = parseFloat((exports._viewer.camera.positionCartographic.height / 100000).toFixed(2));
                            lonlats.push(longitude);
                            lonlats.push(latitude);
                            lonlats.push(height);
                        }
                    }
                    datasourceForLayers[randomsourceId] = new Cesium.Primitive({
                        'show': true,
                        'allowPicking': false,
                        'geometryInstances': new Cesium.GeometryInstance({
                            'geometry': new Cesium.PolygonGeometry({
                                'polygonHierarchy': new Cesium.PolygonHierarchy(Cesium.Cartesian3.fromDegreesArrayHeights(lonlats)),
                                'extrudedHeight': height
                            })
                        }),
                        'appearance': ellipsoidSurfaceAppearance,
                        'interleave': true,
                        'compressVertices': false
                    });
                    datasourceForLayers[layername].add(datasourceForLayers[randomsourceId]);
                    layerfordatasourceId[randomsourceId] = layername;
                    this.layers[layername].features.push(randomsourceId);
                    return randomsourceId;
                });
            } else {

                console.log('error: geojson or  array not given')
                return false
            }
        }
    }

    function isRation(scale) {
        if (Object.prototype.toString.call(scale) !== '[object Number]') {
            console.log('error: ration must be number');
            return false
        }
        return true
    }

    function getRotation(rotation) {
        var drotation = rotation * 2 * Cesium.Math.PI / 360;
        return drotation;
    }

    function fireHandler(options) {
        if (!checkOptions(options)) return false;
        var layername = options.layer;
        if (!layername || !datasourceForLayers[layername]) {
            console.log('error: no layer name given')
            return;
        }
        var lon = options.lon,
            lat = options.lat,
            height = options.height || 8,
            scale = options.scale || 1;
        if (!isNumber(lon, lat, height) || !isRation(scale)) {
            console.log('error: create fire fail')
            return;
        }
        var randomsourceId = getDatasourceid(),
            position = new Cesium.Cartesian3.fromDegrees(lon, lat, height),
            newposition = new Cesium.Cartesian3(0, 0, 0),
            matrix = Cesium.Matrix4.fromTranslation(position, new Cesium.Matrix4()),
            newmatrix = Cesium.Matrix4.fromTranslation(newposition, new Cesium.Matrix4()),
            modelMatrix = Cesium.Matrix4.multiplyTransformation(matrix, newmatrix, new Cesium.Matrix4());
        datasourceForLayers[randomsourceId] = new Cesium.ParticleSystem({
            'show': true,
            'emitter': new Cesium.ConeEmitter(getRotation(30)),
            'modelMatrix': modelMatrix,
            'emissionRate': 10,
            'bursts': [new Cesium.ParticleBurst({
                'time': 5,
                'minimum': 10,
                'maximum': 100
            }), new Cesium.ParticleBurst({
                'time': 10,
                'minimum': 50,
                'maximum': 100
            }), new Cesium.ParticleBurst({
                'time': 15,
                'minimum': 200,
                'maximum': 300
            })],
            'startScale': 1 * scale,
            'endScale': 4 * scale,
            'startColor': Cesium.Color.RED.withAlpha(0.7),
            'endColor': Cesium.Color.YELLOW.withAlpha(0.3),
            'image': './static/effect_fire.png',
            'minimumImageSize': new Cesium.Cartesian2(5, 5),
            'maximumImageSize': new Cesium.Cartesian2(10, 10),
            'minimumSpeed': 1,
            'maximumSpeed': 2
        });
        datasourceForLayers[layername].add(datasourceForLayers[randomsourceId]);
        this.layers[layername].features.push(randomsourceId);
        layerfordatasourceId[randomsourceId] = layername;
        return randomsourceId;
    }

    function flyLineHandler(options) {
        if (!checkOptions(options)) return false;
        var layername = options.layer;
        if (!datasourceForLayers.hasOwnProperty(layername) || !this.layers.hasOwnProperty(layername)) {
            console.log('error: no given layer');
            return;
        }
        var startLon = options.startLon,
            startLat = options.startLat,
            endLon = options.endLon,
            endLat = options.endLat,
            height = options.height || 500000,
            width = options.width || 2,
            randomsourceId = getDatasourceid(),
            red = options.red;
        red === undefined ? red = '0.0' : red = red.toFixed(1);
        var green = options.green;
        green === undefined ? green = '255.0' : green = green.toFixed(1);
        var blue = options.blue;
        blue === undefined ? blue = '255.0' : blue = blue.toFixed(1);
        var geometryInstance = new Cesium.GeometryInstance({
            'geometry': new Cesium.PolylineGeometry({
                'positions': getPositions(startLon, startLat, endLon, endLat, height),
                'width': width
            }),
            'id': randomsourceId
        });
        datasourceForLayers[randomsourceId] = new Cesium.Primitive({
            'geometryInstances': [geometryInstance],
            'appearance': getAppearance(),
            'releaseGeometryInstances': false,
            'compressVertices': false
        });
        datasourceForLayers[layername].add(datasourceForLayers[randomsourceId]);
        layerfordatasourceId[randomsourceId] = layername;
        this.layers[layername].features.push(randomsourceId);
        return randomsourceId;

        function getPositions(startLon, startLat, endLon, endLat, height) {
            var _0x40763d = _0xd225d7(startLon, startLat, endLon, endLat, height),
                _0x3e757a = [];
            for (var _0x49292c in _0x40763d) {
                _0x3e757a.push(_0x40763d[_0x49292c][0]), _0x3e757a.push(_0x40763d[_0x49292c][1]), _0x3e757a.push(_0x40763d[_0x49292c][2]);
            }
            return Cesium.Cartesian3.fromDegreesArrayHeights(_0x3e757a);
        }

        function getAppearance() {
            let material = new Cesium.Material.fromType('Color');
            material.uniforms.color = Cesium.Color.ORANGE;
            var appearance = new Cesium.PolylineMaterialAppearance({
                'material': material,
                'translucent': true,
                'vertexShaderSource': effecttypes.flyLine_vertexShaderSource(),
                'fragmentShaderSource': effecttypes.flyLine_fragmentShaderSource(red, green, blue)
            });
            return appearance;
        }

        function _0xd225d7(startLon, startLat, endLon, endLat, height) {
            var startarr = [startLat, 0],
                midarr = [(endLat + startLat) / 2, height],
                endarr = [endLat, 0],
                _0x31a07c = _0x2d5b79(startarr, midarr, endarr),
                _0x3b27de = [];
            for (var _0x37b91e in _0x31a07c) {
                var _0x3f82d2 = (endLon - startLon) * (_0x31a07c[_0x37b91e][0] - startLat) / (endLat - startLat) + startLon;
                _0x3b27de.push([_0x3f82d2, _0x31a07c[_0x37b91e][0], _0x31a07c[_0x37b91e][1]]);
            }
            return _0x3b27de;
        }

        function _0x2d5b79(startarr, midarr, endarr) {
            var d2positions = [{
                    'x': startarr[0],
                    'y': startarr[1]
                }, {
                    'x': midarr[0],
                    'y': midarr[1]
                }, {
                    'x': endarr[0],
                    'y': endarr[1]
                }],
                d3positions = getD3Position(d2positions, 100);
            return d3positions;
        }

        function getD3Position(d2positions, pointnum) {
            var arr = [];
            for (let index = 0; index < pointnum; index++) {
                var position = parsePosition(d2positions, index / pointnum);
                arr.push([position.x, position.y]);
            }
            return arr;
        }

        function parsePosition(_0x24538c, _0x2d1121) {
            var _0x10ca60 = _0x24538c.length,
                _0x5994b7 = 0,
                _0x1d5202 = 0,
                _0x176028 = function _0x57687c(_0x1768d2, _0x4c1510) {
                    var _0x21922a = 1,
                        _0x586959 = 1;
                    while (_0x4c1510 > 0) {
                        _0x21922a *= _0x1768d2, _0x586959 *= _0x4c1510, _0x1768d2--, _0x4c1510--;
                    }
                    return _0x21922a / _0x586959;
                };
            for (var _0x447abb = 0; _0x447abb < _0x10ca60; _0x447abb++) {
                var _0x4c3614 = _0x24538c[_0x447abb];
                _0x5994b7 += _0x4c3614.x * window.Math.pow(1 - _0x2d1121, _0x10ca60 - 1 - _0x447abb) * window.Math.pow(_0x2d1121, _0x447abb) * _0x176028(_0x10ca60 - 1, _0x447abb), _0x1d5202 += _0x4c3614.y * window.Math.pow(1 - _0x2d1121, _0x10ca60 - 1 - _0x447abb) * window.Math.pow(_0x2d1121, _0x447abb) * _0x176028(_0x10ca60 - 1, _0x447abb);
            }
            return {
                'x': _0x5994b7,
                'y': _0x1d5202
            };
        }
    }
    var effecttypes = {
        water_mask: function (alpha) {
            return 'varying vec3 v_positionMC; \n             varying vec3 v_positionEC; \n               varying vec2 v_st; \n               void main() \n               { \n                   czm_materialInput materialInput; \n                   vec3 normalEC = normalize(czm_normal3D * czm_geodeticSurfaceNormal(v_positionMC, vec3(0.0), vec3(1.0))); \n                   #ifdef FACE_FORWARD \n                   normalEC = faceforward(normalEC, vec3(0.0, 0.0, 1.0), -normalEC); \n                   #endif \n                   materialInput.s = v_st.s; \n                   materialInput.st = v_st; \n                   materialInput.str = vec3(v_st, 0.0); \n                   materialInput.normalEC = normalEC; \n                   materialInput.tangentToEyeMatrix = czm_eastNorthUpToEyeCoordinates(v_positionMC, materialInput.normalEC); \n                   vec3 positionToEyeEC = -v_positionEC; \n                   materialInput.positionToEyeEC = positionToEyeEC; \n                   czm_material material = czm_getMaterial(materialInput); \n                   #ifdef FLAT \n                   gl_FragColor = vec4(material.diffuse + material.emission, material.alpha); \n                   #else \n                   gl_FragColor = czm_phong(normalize(positionToEyeEC), material, czm_lightDirectionEC); \n                   gl_FragColor.a = ' + alpha + ';\n                    #endif\n                }\n'
        },
        flyLine_fragmentShaderSource: function (r, g, b) {
            return 'varying vec2 v_st;\n                varying float v_width;\n                varying float v_polylineAngle;\n                varying vec4 v_positionEC;\n                varying vec3 v_normalEC;\n                void main()\n                {\n                    vec2 st = v_st;\n                    float xx = fract(st.s - czm_frameNumber/60.0);\n                    float r = ' + r + ';\n                    float g = ' + g + '; \n                   float b = ' + b + '; \n                   float a = xx; \n                   gl_FragColor = vec4(r,g,b,a); \n               }\n';
        },
        flyLine_vertexShaderSource: function () {
            return '#define CLIP_POLYLINE\n                void clipLineSegmentToNearPlane(\n                    vec3 p0,\n                    vec3 p1,\n                    out vec4 positionWC,\n                    out bool clipped,\n                    out bool culledByNearPlane,\n                    out vec4 clippedPositionEC)\n                {\n                    culledByNearPlane = false;\n                    clipped = false;\n                    vec3 p0ToP1 = p1 - p0;\n                    float magnitude = length(p0ToP1);\n                    vec3 direction = normalize(p0ToP1);\n                    float endPoint0Distance =  czm_currentFrustum.x + p0.z;\n                    float denominator = -direction.z;\n                    if (endPoint0Distance > 0.0 && abs(denominator) < czm_epsilon7)\n                    {\n                        culledByNearPlane = true;\n                    }\n                    else if (endPoint0Distance > 0.0)\n                    {\n                        float t = endPoint0Distance / denominator;\n                        if (t < 0.0 || t > magnitude)\n                        {\n                            culledByNearPlane = true;\n                        }\n                        else\n                        {\n                            p0 = p0 + t * direction;\n                            p0.z = min(p0.z, -czm_currentFrustum.x);\n                            clipped = true;\n                        }\n                    }\n                    clippedPositionEC = vec4(p0, 1.0);\n                    positionWC = czm_eyeToWindowCoordinates(clippedPositionEC);\n                }\n                vec4 getPolylineWindowCoordinatesEC(vec4 positionEC, vec4 prevEC, vec4 nextEC, float expandDirection, float width, bool usePrevious, out float angle)\n                {\n                    #ifdef POLYLINE_DASH\n                    vec4 positionWindow = czm_eyeToWindowCoordinates(positionEC);\n                    vec4 previousWindow = czm_eyeToWindowCoordinates(prevEC);\n                    vec4 nextWindow = czm_eyeToWindowCoordinates(nextEC);\n                    vec2 lineDir;\n                    if (usePrevious) {\n                        lineDir = normalize(positionWindow.xy - previousWindow.xy);\n                    }\n                    else {\n                        lineDir = normalize(nextWindow.xy - positionWindow.xy);\n                    }\n                    angle = atan(lineDir.x, lineDir.y) - 1.570796327;\n                    angle = floor(angle / czm_piOverFour + 0.5) * czm_piOverFour;\n                    #endif\n                    vec4 clippedPrevWC, clippedPrevEC;\n                    bool prevSegmentClipped, prevSegmentCulled;\n                    clipLineSegmentToNearPlane(prevEC.xyz, positionEC.xyz, clippedPrevWC, prevSegmentClipped, prevSegmentCulled, clippedPrevEC);\n                    vec4 clippedNextWC, clippedNextEC;\n                    bool nextSegmentClipped, nextSegmentCulled;\n                    clipLineSegmentToNearPlane(nextEC.xyz, positionEC.xyz, clippedNextWC, nextSegmentClipped, nextSegmentCulled, clippedNextEC);\n                    bool segmentClipped, segmentCulled;\n                    vec4 clippedPositionWC, clippedPositionEC;\n                    clipLineSegmentToNearPlane(positionEC.xyz, usePrevious ? prevEC.xyz : nextEC.xyz, clippedPositionWC, segmentClipped, segmentCulled, clippedPositionEC);\n                    if (segmentCulled)\n                    {\n                        return vec4(0.0, 0.0, 0.0, 1.0);\n                    }\n                    vec2 directionToPrevWC = normalize(clippedPrevWC.xy - clippedPositionWC.xy);\n                    vec2 directionToNextWC = normalize(clippedNextWC.xy - clippedPositionWC.xy);\n                    if (prevSegmentCulled)\n                    {\n                        directionToPrevWC = -directionToNextWC;\n                    }\n                    else if (nextSegmentCulled)\n                    {\n                        directionToNextWC = -directionToPrevWC;\n                    }\n                    vec2 thisSegmentForwardWC, otherSegmentForwardWC;\n                    if (usePrevious)\n                    {\n                        thisSegmentForwardWC = -directionToPrevWC;\n                        otherSegmentForwardWC = directionToNextWC;\n                    }\n                    else\n                    {\n                        thisSegmentForwardWC = directionToNextWC;\n                        otherSegmentForwardWC =  -directionToPrevWC;\n                    }\n                    vec2 thisSegmentLeftWC = vec2(-thisSegmentForwardWC.y, thisSegmentForwardWC.x);\n                    vec2 leftWC = thisSegmentLeftWC;\n                    float expandWidth = width * 0.5;\n                    if (!czm_equalsEpsilon(prevEC.xyz - positionEC.xyz, vec3(0.0), czm_epsilon1) && !czm_equalsEpsilon(nextEC.xyz - positionEC.xyz, vec3(0.0), czm_epsilon1))\n                    {\n                        vec2 otherSegmentLeftWC = vec2(-otherSegmentForwardWC.y, otherSegmentForwardWC.x);\n                        vec2 leftSumWC = thisSegmentLeftWC + otherSegmentLeftWC;\n                        float leftSumLength = length(leftSumWC);\n                        leftWC = leftSumLength < czm_epsilon6 ? thisSegmentLeftWC : (leftSumWC / leftSumLength);\n                        vec2 u = -thisSegmentForwardWC;\n                        vec2 v = leftWC;\n                        float sinAngle = abs(u.x * v.y - u.y * v.x);\n                        expandWidth = clamp(expandWidth / sinAngle, 0.0, width * 2.0);\n                    }\n                    vec2 offset = leftWC * expandDirection * expandWidth * czm_pixelRatio;\n                    return vec4(clippedPositionWC.xy + offset, -clippedPositionWC.z, 1.0) * (czm_projection * clippedPositionEC).w;\n                }\n                vec4 getPolylineWindowCoordinates(vec4 position, vec4 previous, vec4 next, float expandDirection, float width, bool usePrevious, out float angle)\n                {\n                    vec4 positionEC = czm_modelViewRelativeToEye * position;\n                    vec4 prevEC = czm_modelViewRelativeToEye * previous;\n                    vec4 nextEC = czm_modelViewRelativeToEye * next;\n                    return getPolylineWindowCoordinatesEC(positionEC, prevEC, nextEC, expandDirection, width, usePrevious, angle);\n                }\n                attribute vec3 position3DHigh;\n                attribute vec3 position3DLow;\n                attribute vec3 prevPosition3DHigh;\n                attribute vec3 prevPosition3DLow;\n                attribute vec3 nextPosition3DHigh;\n                attribute vec3 nextPosition3DLow;\n                attribute vec2 expandAndWidth;\n                attribute vec2 st;\n                attribute float batchId;\n                varying float v_width;\n                varying vec2 v_st;\n                varying float v_polylineAngle;\n                varying vec4 v_positionEC;\n                varying vec3 v_normalEC;\n                void main()\n                {\n                float expandDir = expandAndWidth.x;\n                float width = abs(expandAndWidth.y) + 0.5;\n                bool usePrev = expandAndWidth.y < 0.0;\n                vec4 p = czm_computePosition();\n                vec4 prev = czm_computePrevPosition();\n                vec4 next = czm_computeNextPosition();\n                float angle;\n                vec4 positionWC = getPolylineWindowCoordinates(p, prev, next, expandDir, width, usePrev, angle);\n                gl_Position = czm_viewportOrthographic * positionWC;\n                v_width = width;\n                v_st.s = st.s;\n                v_st.t = st.t;\n                // v_st.t = czm_writeNonPerspective(st.t, gl_Position.w);\n                v_polylineAngle = angle;\n                vec4 eyePosition = czm_modelViewRelativeToEye * p;\n                v_positionEC =  czm_inverseModelView * eyePosition;\n                //v_normalEC = czm_normal * normal;\n                }\n';
        }
    };

    function effectHandler(that) {
        this.water = waterHandler.bind(that);
        this.fire = fireHandler.bind(that);
        this.flyLine = flyLineHandler.bind(that)
    }


    window.CESIUM_BASE_URL = '/';
    exports._viewer = undefined;
    exports._scene = undefined;
    exports._globe = undefined;
    var carema = undefined,
        screenSpaceEventHandler = undefined,
        options = Object.assign({}, showobj, terrbox);
    Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJmNWEyYzg0OC02NjQ3LTRkNDUtOWJmYy0yMzVhOWMyODUyOTYiLCJpZCI6MTMyNjMsImlhdCI6MTYyNjE0NDMxMH0.4Pl52S18EdnkNKbE8ucpI9-2qinrgSTGD6OXcoBt1Mk';

    function map() {
        this.layers = {};
        this.mapinfo = {};
        let loader = new loaderhandler(this),
            operator = new operatorhandler(this),
            layer = new layerhandler(this),
        effects = new effectHandler(this);
            this._loader = loader;
        this._operator = operator;
        this._layer = layer;
        this._effects = effects

    }

    Object.defineProperties(map.prototype, {
        loader: {
            get: function () {
                return this._loader;
            }
        },
        operator: {
            get: function () {
                return this._operator;
            }
        },

        layer: {
            get: function () {
                return this._layer;
            }
        },
        effects: {
            get: function () {
                return this._effects;
            }
        },
    })
    map.prototype.bind = function (container) {
        console.log('container', container)
        if (container && document.getElementById(container)) {
            console.log('1')
            exports._viewer = new Cesium.Viewer(container,options)
            exports._viewer._cesiumWidget._creditContainer.style.display = 'none'
            exports._scene = exports._viewer.scene;
            exports._scene.postProcessStages.fxaa.enabled = true;
            exports._globe = exports._scene.globe;
            exports._globe.depthTestAgainstTerrain = true;
            console.log('5')
            carema = exports._viewer.camera;
            screenSpaceEventHandler = new Cesium.ScreenSpaceEventHandler(exports._scene.canvas)
        } else {
            console.log('error: no element named' + container)
        }
    }


    map.prototype.set = setHandler;
    var type = {
        'SETTING': setting,
        'MAP': t_map,
        'FEATURE': feature,
        'LAYER': maplayer,
        'OPERATION': operation
    };
    exports.Map = map;
    exports.TYPE = type;
    exports.imagery_feature = imagery_feature;
    exports.o_fires = o_fires;
    Object.defineProperty(exports, "__esModule", {
        value: true
    })
})