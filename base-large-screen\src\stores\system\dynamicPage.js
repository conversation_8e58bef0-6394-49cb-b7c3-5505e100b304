import { defineStore } from "pinia";
import { ApiConfig } from "@/dynamicPage/config";

const state = {};
for (const key in ApiConfig) {
  if (Object.hasOwnProperty.call(ApiConfig, key)) {
    state[key] = {};
  }
}

export const useDynamicStore = defineStore("dynamic", {
  state: () => {
    return state;
  },
  actions: {
    async getData(query, storeKeys) {
      const requests = [];
      for (const key of storeKeys) {
        if (ApiConfig[key]) {
          requests.push(ApiConfig[key](query));
        }
      }
      const results = await Promise.allSettled(requests);
      for (let index = 0; index < results.length; index++) {
        const result = results[index];
        if (result.status === "fulfilled") {
          const stateKey = storeKeys[index];
          this[stateKey] = result.value;
        }
      }
    },
  },
});
