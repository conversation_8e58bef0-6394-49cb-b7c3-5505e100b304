<template>
  <LayoutBox :parts="parts" :focus="true"></LayoutBox>
  <MapDialog name="project">
    <div class="project-dialog">
      <div class="title">{{ dialogStore.dialogContent.title }}</div>
      <div class="introduce">{{ dialogStore.dialogContent.introduce }}</div>
      <img class="poster" :src="dialogStore.dialogContent.poster" alt="poster" />
      <div class="plan-text" style="margin-top: 20px;margin-left: 50px;">项目进度</div>
      <div class="plan-box">
        <template v-for="(item, index) in dialogStore.dialogContent.plans">
          <div class="plan-item">
            <img v-if="index < dialogStore.dialogContent.stage" class="icon" src="../../assets/images/plan_complete.png"
              alt="icon" />
            <img v-else-if="index == dialogStore.dialogContent.stage" class="icon" src="../../assets/images/plan_progress.png"
              alt="icon" />
            <img v-else class="icon" src="../../assets/images/plan_future.png" alt="icon" />
            <div class="plan-text">{{ item }}</div>
          </div>
          <div v-if="index + 1 < dialogStore.dialogContent.plans.length" class="line" :style="lineStyle(index)"></div>
        </template>
      </div>
      <img class="place-icon" src="../../assets/images/place_icon.png" alt="poster" />
      <img class="time-icon" src="../../assets/images/time_icon.png" alt="poster" />
    </div>
  </MapDialog>
</template>
<script setup>
import MapButton from './MapButton.vue'
import Logo from './Logo.vue'
import RightOne from './RightOne.vue'
import RightTwo from './RightTwo.vue'
import RightThree from './RightThree.vue'
import { useMapDialogStore } from "../../stores/modules/mapDialog";
import MapDialog from "../../components/common/MapDialog.vue"

const dialogStore = useMapDialogStore();

const parts = [
  {
    component: MapButton,
    positionStyle: {
      top: "124px",
      left: "63px"
    },
    animation: {
      type: "LeftSlide",
      startTime: 0
    }
  },
  {
    component: Logo,
    positionStyle: {
      top: "833px",
      left: "63px"
    },
    animation: {
      type: "LeftSlide",
      startTime: 0.5
    }
  },
  {
    component: RightOne,
    positionStyle: {
      top: "95px",
      right: "23px"
    },
    animation: {
      type: "RightSlide",
      startTime: 0
    }
  },
  {
    component: RightTwo,
    positionStyle: {
      top: "409px",
      right: "23px"
    },
    animation: {
      type: "RightSlide",
      startTime: 0.5
    }
  },
  {
    component: RightThree,
    positionStyle: {
      top: "787px",
      right: "23px"
    },
    animation: {
      type: "RightSlide",
      startTime: 1
    }
  },
]

function lineStyle(index) {
  if (index + 1 == dialogStore.dialogContent.stage) {
    return {
      background: "linear-gradient(90deg, #6862CE, #B3AEFF)"
    }
  } else if (index == dialogStore.dialogContent.stage) {
    return {
      background: "linear-gradient(90deg, #B3AEFF, #7C728F)"
    }
  } else if (index < dialogStore.dialogContent.stage) {
    return {
      background: "#6862CE"
    }
  } else {
    return {
      background: "#7C728F"
    }
  }

}
</script>
<style scoped lang="scss">
@font-face {
  font-family: YouSheBiaoTiHei;
  src: url('../../assets/fonts/YouSheBiaoTiHei-2.ttf')
}

.project-dialog {
  width: 472px;
  height: 553px;
  background: rgba(17, 28, 15, 0.53);
  border-bottom: 2px solid #69FFA0;
  border-top: 2px solid #63CDCF;
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  .title {
    font-size: 30px;
    font-family: YouSheBiaoTiHei;
    font-weight: 400;
    color: #FFFFFF;
    align-self: center;
  }

  .introduce {
    font-size: 14px;
    line-height: 17px;
    font-family: Source Han Sans SC;
    font-weight: 500;
    color: #FCFFFD;
    -webkit-text-stroke: 0.3px #5EFF99;
    width: 393px;
    height: 136px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 8;
    -webkit-box-orient: vertical;
    margin-left: 50px;
  }

  .poster {
    width: 393px;
    height: 198px;
    object-fit: cover;
    margin-top: 19px;
    margin-left: 50px;
  }

  .plan-text {
    font-size: 14px;
    font-family: Source Han Sans SC;
    font-weight: 500;
    font-style: italic;
    color: #FEFFFF;
    -webkit-text-stroke: 0.5px #5EFF99;
    white-space: nowrap;
  }

  .plan-box {
    height: 80px;
    width: 393px;
    padding: 0px 10px;
    display: flex;
    align-items: center;
    overflow-x: scroll;
    overflow-y: hidden;
    margin-left: 50px;
  }

  .plan-item {
    height: 80px;
    width: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .icon {
      width: 50px;
      height: 50px;
    }
  }

  .line {
    width: 67px;
    height: 3px;
    flex-shrink: 0;
  }

  .place-icon {
    width: 22px;
    height: 22px;
    positionStyle: absolute;
    top: 47px;
    left: 23px;
  }

  .time-icon {
    width: 22px;
    height: 22px;
    positionStyle: absolute;
    top: 421px;
    left: 23px;
  }
}
</style>