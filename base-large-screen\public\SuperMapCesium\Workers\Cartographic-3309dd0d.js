define(["exports","./Check-7b2a090c","./when-b60132fc","./Math-119be1a3"],(function(e,n,t,a){"use strict";function i(e,n,a){this.x=t.defaultValue(e,0),this.y=t.defaultValue(n,0),this.z=t.defaultValue(a,0)}i.fromSpherical=function(e,n){t.defined(n)||(n=new i);var a=e.clock,r=e.cone,u=t.defaultValue(e.magnitude,1),o=u*Math.sin(r);return n.x=o*Math.cos(a),n.y=o*Math.sin(a),n.z=u*Math.cos(r),n},i.fromElements=function(e,n,a,r){return t.defined(r)?(r.x=e,r.y=n,r.z=a,r):new i(e,n,a)},i.clone=function(e,n){if(t.defined(e))return t.defined(n)?(n.x=e.x,n.y=e.y,n.z=e.z,n):new i(e.x,e.y,e.z)},i.fromCartesian4=i.clone,i.packedLength=3,i.pack=function(e,n,a){return a=t.defaultValue(a,0),n[a++]=e.x,n[a++]=e.y,n[a]=e.z,n},i.unpack=function(e,n,a){return n=t.defaultValue(n,0),t.defined(a)||(a=new i),a.x=e[n++],a.y=e[n++],a.z=e[n],a},i.packArray=function(e,n){var a=e.length;t.defined(n)?n.length=3*a:n=new Array(3*a);for(var r=0;r<a;++r)i.pack(e[r],n,3*r);return n},i.unpackArray=function(e,n){var a=e.length;t.defined(n)?n.length=a/3:n=new Array(a/3);for(var r=0;r<a;r+=3){var u=r/3;n[u]=i.unpack(e,r,n[u])}return n},i.fromArray=i.unpack,i.maximumComponent=function(e){return Math.max(e.x,e.y,e.z)},i.minimumComponent=function(e){return Math.min(e.x,e.y,e.z)},i.minimumByComponent=function(e,n,t){return t.x=Math.min(e.x,n.x),t.y=Math.min(e.y,n.y),t.z=Math.min(e.z,n.z),t},i.maximumByComponent=function(e,n,t){return t.x=Math.max(e.x,n.x),t.y=Math.max(e.y,n.y),t.z=Math.max(e.z,n.z),t},i.magnitudeSquared=function(e){return e.x*e.x+e.y*e.y+e.z*e.z},i.magnitude=function(e){return Math.sqrt(i.magnitudeSquared(e))};var r=new i;i.distance=function(e,n){return i.subtract(e,n,r),i.magnitude(r)},i.distanceSquared=function(e,n){return i.subtract(e,n,r),i.magnitudeSquared(r)},i.normalize=function(e,n){var t=i.magnitude(e);return n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n},i.dot=function(e,n){return e.x*n.x+e.y*n.y+e.z*n.z},i.multiplyComponents=function(e,n,t){return t.x=e.x*n.x,t.y=e.y*n.y,t.z=e.z*n.z,t},i.divideComponents=function(e,n,t){return t.x=e.x/n.x,t.y=e.y/n.y,t.z=e.z/n.z,t},i.add=function(e,n,t){return t.x=e.x+n.x,t.y=e.y+n.y,t.z=e.z+n.z,t},i.subtract=function(e,n,t){return t.x=e.x-n.x,t.y=e.y-n.y,t.z=e.z-n.z,t},i.multiplyByScalar=function(e,n,t){return t.x=e.x*n,t.y=e.y*n,t.z=e.z*n,t},i.divideByScalar=function(e,n,t){return t.x=e.x/n,t.y=e.y/n,t.z=e.z/n,t},i.negate=function(e,n){return n.x=-e.x,n.y=-e.y,n.z=-e.z,n},i.abs=function(e,n){return n.x=Math.abs(e.x),n.y=Math.abs(e.y),n.z=Math.abs(e.z),n};var u=new i;i.lerp=function(e,n,t,a){return i.multiplyByScalar(n,t,u),a=i.multiplyByScalar(e,1-t,a),i.add(u,a,a)};var o=new i,d=new i;i.angleBetween=function(e,n){i.normalize(e,o),i.normalize(n,d);var t=i.dot(o,d),a=i.magnitude(i.cross(o,d,o));return Math.atan2(a,t)};var s=new i;i.mostOrthogonalAxis=function(e,n){var t=i.normalize(e,s);return i.abs(t,t),n=t.x<=t.y?t.x<=t.z?i.clone(i.UNIT_X,n):i.clone(i.UNIT_Z,n):t.y<=t.z?i.clone(i.UNIT_Y,n):i.clone(i.UNIT_Z,n)},i.projectVector=function(e,n,t){var a=i.dot(e,n)/i.dot(n,n);return i.multiplyByScalar(n,a,t)},i.equals=function(e,n){return e===n||t.defined(e)&&t.defined(n)&&e.x===n.x&&e.y===n.y&&e.z===n.z},i.equalsArray=function(e,n,t){return e.x===n[t]&&e.y===n[t+1]&&e.z===n[t+2]},i.equalsEpsilon=function(e,n,i,r){return e===n||t.defined(e)&&t.defined(n)&&a.CesiumMath.equalsEpsilon(e.x,n.x,i,r)&&a.CesiumMath.equalsEpsilon(e.y,n.y,i,r)&&a.CesiumMath.equalsEpsilon(e.z,n.z,i,r)},i.cross=function(e,n,t){var a=e.x,i=e.y,r=e.z,u=n.x,o=n.y,d=n.z,s=i*d-r*o,f=r*u-a*d,l=a*o-i*u;return t.x=s,t.y=f,t.z=l,t},i.midpoint=function(e,n,t){return t.x=.5*(e.x+n.x),t.y=.5*(e.y+n.y),t.z=.5*(e.z+n.z),t},i.fromDegrees=function(e,n,t,r,u){return e=a.CesiumMath.toRadians(e),n=a.CesiumMath.toRadians(n),i.fromRadians(e,n,t,r,u)};var f=new i,l=new i,c=new i(40680631590769,40680631590769,40408299984661.445),h=new i(40680631590769,40680631590769,40680631590769);i.fromRadians=function(e,n,r,u,o){r=t.defaultValue(r,0);var d=t.defined(u)?u.radiiSquared:h;a.CesiumMath.equalsEpsilon(a.CesiumMath.Radius,6356752.314245179,a.CesiumMath.EPSILON10)&&(d=t.defined(u)?u.radiiSquared:c);var s=Math.cos(n);f.x=s*Math.cos(e),f.y=s*Math.sin(e),f.z=Math.sin(n),f=i.normalize(f,f),i.multiplyComponents(d,f,l);var y=Math.sqrt(i.dot(f,l));return l=i.divideByScalar(l,y,l),f=i.multiplyByScalar(f,r,f),t.defined(o)||(o=new i),i.add(l,f,o)},i.fromDegreesArray=function(e,n,a){var r=e.length;t.defined(a)?a.length=r/2:a=new Array(r/2);for(var u=0;u<r;u+=2){var o=e[u],d=e[u+1],s=u/2;a[s]=i.fromDegrees(o,d,0,n,a[s])}return a},i.fromRadiansArray=function(e,n,a){var r=e.length;t.defined(a)?a.length=r/2:a=new Array(r/2);for(var u=0;u<r;u+=2){var o=e[u],d=e[u+1],s=u/2;a[s]=i.fromRadians(o,d,0,n,a[s])}return a},i.fromDegreesArrayHeights=function(e,n,a){var r=e.length;t.defined(a)?a.length=r/3:a=new Array(r/3);for(var u=0;u<r;u+=3){var o=e[u],d=e[u+1],s=e[u+2],f=u/3;a[f]=i.fromDegrees(o,d,s,n,a[f])}return a},i.fromRadiansArrayHeights=function(e,n,a){var r=e.length;t.defined(a)?a.length=r/3:a=new Array(r/3);for(var u=0;u<r;u+=3){var o=e[u],d=e[u+1],s=e[u+2],f=u/3;a[f]=i.fromRadians(o,d,s,n,a[f])}return a},i.ZERO=Object.freeze(new i(0,0,0)),i.UNIT_X=Object.freeze(new i(1,0,0)),i.UNIT_Y=Object.freeze(new i(0,1,0)),i.UNIT_Z=Object.freeze(new i(0,0,1)),i.UNIT_XYZ=Object.freeze(new i(1,1,1)),i.prototype.clone=function(e){return i.clone(this,e)},i.prototype.equals=function(e){return i.equals(this,e)},i.prototype.equalsEpsilon=function(e,n,t){return i.equalsEpsilon(this,e,n,t)},i.prototype.toString=function(){return"("+this.x+", "+this.y+", "+this.z+")"},i.globalOffset=new i(0,0,0);var y=new i,m=new i;function z(e,n,r,u,o){var d=e.x,s=e.y,f=e.z,l=n.x,c=n.y,h=n.z,z=d*d*l*l,x=s*s*c*c,g=f*f*h*h,p=z+x+g,M=Math.sqrt(1/p),v=i.multiplyByScalar(e,M,y);if(p<u)return isFinite(M)?i.clone(v,o):void 0;var w=r.x,C=r.y,q=r.z,R=m;R.x=v.x*w*2,R.y=v.y*C*2,R.z=v.z*q*2;var S,b,E,O,A,k,B,I=(1-M)*i.magnitude(e)/(.5*i.magnitude(R)),N=0;do{N=(S=z*(A=(b=1/(1+(I-=N)*w))*b)+x*(k=(E=1/(1+I*C))*E)+g*(B=(O=1/(1+I*q))*O)-1)/(-2*(z*(A*b)*w+x*(k*E)*C+g*(B*O)*q))}while(Math.abs(S)>a.CesiumMath.EPSILON12);return t.defined(o)?(o.x=d*b,o.y=s*E,o.z=f*O,o):new i(d*b,s*E,f*O)}function x(e,n,a){this.longitude=t.defaultValue(e,0),this.latitude=t.defaultValue(n,0),this.height=t.defaultValue(a,0)}x.fromRadians=function(e,n,a,i){return a=t.defaultValue(a,0),t.defined(i)?(i.longitude=e,i.latitude=n,i.height=a,i):new x(e,n,a)},x.fromDegrees=function(e,n,t,i){return e=a.CesiumMath.toRadians(e),n=a.CesiumMath.toRadians(n),x.fromRadians(e,n,t,i)};var g=new i,p=new i,M=new i,v=new i(1/6378137,1/6378137,1/6356752.314245179),w=new i(1/6378137,1/6378137,1/6378137),C=new i(1/40680631590769,1/40680631590769,1/40408299984661.445),q=new i(1/40680631590769,1/40680631590769,1/40680631590769),R=a.CesiumMath.EPSILON1;x.fromCartesian=function(e,n,r){var u=t.defined(n)?n.oneOverRadii:w,o=t.defined(n)?n.oneOverRadiiSquared:q,d=t.defined(n)?n._centerToleranceSquared:R;a.CesiumMath.equalsEpsilon(a.CesiumMath.Radius,6356752.314245179,a.CesiumMath.EPSILON10)&&(u=t.defined(n)?n.oneOverRadii:v,o=t.defined(n)?n.oneOverRadiiSquared:C);var s=z(e,u,o,d,p);if(t.defined(s)){var f=i.multiplyComponents(s,o,g);f=i.normalize(f,f);var l=i.subtract(e,s,M),c=Math.atan2(f.y,f.x),h=Math.asin(f.z),y=a.CesiumMath.sign(i.dot(l,e))*i.magnitude(l);return t.defined(r)?(r.longitude=c,r.latitude=h,r.height=y,r):new x(c,h,y)}},x.toCartesian=function(e,n,t){return i.fromRadians(e.longitude,e.latitude,e.height,n,t)},x.sphericalDistance=function(e,t,i,r){if(n.Check.defined("longitudeA",e),n.Check.defined("longitudeB",i),n.Check.defined("latitudeA",t),n.Check.defined("latitudeB",r),e===i&&t===r)return 0;var u=a.CesiumMath.toRadians(t),o=a.CesiumMath.toRadians(r),d=a.CesiumMath.toRadians(e),s=a.CesiumMath.toRadians(i),f=d*d+u*u,l=s*s+o*o,c=(f+l-((d-s)*(d-s)+(u-o)*(u-o)))/(2*Math.sqrt(f)*Math.sqrt(l));return c=a.CesiumMath.clamp(c,-1,1),Math.acos(c)*a.CesiumMath.Radius},x.clone=function(e,n){if(t.defined(e))return t.defined(n)?(n.longitude=e.longitude,n.latitude=e.latitude,n.height=e.height,n):new x(e.longitude,e.latitude,e.height)},x.equals=function(e,n){return e===n||t.defined(e)&&t.defined(n)&&e.longitude===n.longitude&&e.latitude===n.latitude&&e.height===n.height},x.equalsEpsilon=function(e,n,a){return e===n||t.defined(e)&&t.defined(n)&&Math.abs(e.longitude-n.longitude)<=a&&Math.abs(e.latitude-n.latitude)<=a&&Math.abs(e.height-n.height)<=a},x.ZERO=Object.freeze(new x(0,0,0)),x.prototype.clone=function(e){return x.clone(this,e)},x.prototype.equals=function(e){return x.equals(this,e)},x.prototype.equalsEpsilon=function(e,n){return x.equalsEpsilon(this,e,n)},x.prototype.toString=function(){return"("+this.longitude+", "+this.latitude+", "+this.height+")"},e.Cartesian3=i,e.Cartographic=x,e.scaleToGeodeticSurface=z}));
