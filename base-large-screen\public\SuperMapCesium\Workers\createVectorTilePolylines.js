define(["./AttributeCompression-90851096","./Cartographic-3309dd0d","./Cartesian2-47311507","./IndexDatatype-8a5eead4","./Math-119be1a3","./createTaskProcessorWorker","./Check-7b2a090c","./when-b60132fc","./WebGLConstants-4ae0db90"],(function(a,e,r,t,n,i,s,u,c){"use strict";var o=32767,f=new e.Cartographic,p=new e.Cartesian3;var C=new r.Rectangle,d=new r.Ellipsoid,b=new e.Cartesian3,l={min:void 0,max:void 0};var h=new e.Cartesian3,w=new e.Cartesian3,y=new e.Cartesian3,k=new e.Cartesian3,v=new e.Cartesian3;return i((function(i,s){var u=new Uint16Array(i.positions),c=new Uint16Array(i.widths),g=new Uint32Array(i.counts),A=new Uint16Array(i.batchIds);!function(a){a=new Float64Array(a);var t=0;l.min=a[t++],l.max=a[t++],r.Rectangle.unpack(a,t,C),t+=r.Rectangle.packedLength,r.Ellipsoid.unpack(a,t,d),t+=r.Ellipsoid.packedLength,e.Cartesian3.unpack(a,t,b)}(i.packedBuffer);var m,x=d,E=b,D=function(r,t,i,s,u){var c=r.length/3,C=r.subarray(0,c),d=r.subarray(c,2*c),b=r.subarray(2*c,3*c);a.AttributeCompression.zigZagDeltaDecode(C,d,b);for(var l=new Float32Array(r.length),h=0;h<c;++h){var w=C[h],y=d[h],k=b[h],v=n.CesiumMath.lerp(t.west,t.east,w/o),g=n.CesiumMath.lerp(t.south,t.north,y/o),A=n.CesiumMath.lerp(i,s,k/o),m=e.Cartographic.fromRadians(v,g,A,f),x=u.cartographicToCartesian(m,p);e.Cartesian3.pack(x,l,3*h)}return l}(u,C,l.min,l.max,x),I=D.length/3,T=4*I-4,U=new Float32Array(3*T),F=new Float32Array(3*T),N=new Float32Array(3*T),R=new Float32Array(2*T),M=new Uint16Array(T),P=0,L=0,S=0,_=0,G=g.length;for(m=0;m<G;++m){for(var W=g[m],B=c[m],z=A[m],H=0;H<W;++H){var O;if(0===H){var Y=e.Cartesian3.unpack(D,3*_,h),Z=e.Cartesian3.unpack(D,3*(_+1),w);O=e.Cartesian3.subtract(Y,Z,y),e.Cartesian3.add(Y,O,O)}else O=e.Cartesian3.unpack(D,3*(_+H-1),y);var j,q=e.Cartesian3.unpack(D,3*(_+H),k);if(H===W-1){var J=e.Cartesian3.unpack(D,3*(_+W-1),h),K=e.Cartesian3.unpack(D,3*(_+W-2),w);j=e.Cartesian3.subtract(J,K,v),e.Cartesian3.add(J,j,j)}else j=e.Cartesian3.unpack(D,3*(_+H+1),v);e.Cartesian3.subtract(O,E,O),e.Cartesian3.subtract(q,E,q),e.Cartesian3.subtract(j,E,j);for(var Q=H===W-1?2:4,V=0===H?2:0;V<Q;++V){e.Cartesian3.pack(q,U,P),e.Cartesian3.pack(O,F,P),e.Cartesian3.pack(j,N,P),P+=3;var X=V-2<0?-1:1;R[L++]=V%2*2-1,R[L++]=X*B,M[S++]=z}}_+=W}var $=t.IndexDatatype.createTypedArray(T,6*I-6),aa=0,ea=0;for(G=I-1,m=0;m<G;++m)$[ea++]=aa,$[ea++]=aa+2,$[ea++]=aa+1,$[ea++]=aa+1,$[ea++]=aa+2,$[ea++]=aa+3,aa+=4;return s.push(U.buffer,F.buffer,N.buffer),s.push(R.buffer,M.buffer,$.buffer),{indexDatatype:2===$.BYTES_PER_ELEMENT?t.IndexDatatype.UNSIGNED_SHORT:t.IndexDatatype.UNSIGNED_INT,currentPositions:U.buffer,previousPositions:F.buffer,nextPositions:N.buffer,expandAndWidth:R.buffer,batchIds:M.buffer,indices:$.buffer}}))}));
