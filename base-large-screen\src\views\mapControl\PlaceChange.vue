<template>
  <div>
    <el-select :model-value="value" @change="handleChange" placeholder="Select" :teleported="false">
      <el-option v-for="item in mapStore.placeList" :key="item.value" :label="item.label" :value="item.value"
        size="small" />
    </el-select>
  </div>
</template>
<script setup>
import { onMounted, ref, computed } from "vue";
import { useRouter, useRoute } from 'vue-router'
import { useMapStore } from "../../stores/modules/map";

const router = useRouter();
const route = useRoute();

const value = computed(() => {
  if (route.query && route.query.place) {
    return route.query.place
  }
})

const mapStore = useMapStore();

function handleChange(value) {
  router.replace({
    query: {
      ...route.query,
      place: value
    }
  })
}

</script>
