import fencePng from "../../../assets/images/fence.png";
const defaultColor = Cesium.Color.DEEPSKYBLUE;
const defaultSpeed = 2;

function TrailWallMaterialProperty(options) {
  options = Cesium.defaultValue(options, Cesium.defaultValue.EMPTY_OBJECT);

  this._definitionChanged = new Cesium.Event();
  this._color = undefined;
  this._colorSubscription = undefined;
  this._speed = undefined;
  this._speedSubscription = undefined;

  this.color = options.color;
  this.speed = options.speed;
}

Object.defineProperties(TrailWallMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return Cesium.Property.isConstant(this._color) && Cesium.Property.isConstant(this._speed);
    },
  },
  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    },
  },
  color: Cesium.createPropertyDescriptor("color"),
  speed: Cesium.createPropertyDescriptor("speed"),
});

TrailWallMaterialProperty.prototype.getType = function (time) {
  return "TrailWall";
};

TrailWallMaterialProperty.prototype.getValue = function (time, result) {
  if (!Cesium.defined(result)) {
    result = {};
  }
  result.color = Cesium.Property.getValueOrClonedDefault(this._color, time, defaultColor, result.color);
  result.speed = Cesium.Property.getValueOrClonedDefault(this._speed, time, defaultSpeed, result.speed);
  return result;
};

TrailWallMaterialProperty.prototype.equals = function (other) {
  return (
    this === other || //
    (other instanceof TrailWallMaterialProperty && //
      Cesium.Property.equals(this._color, other._color) && //
      Cesium.Property.equals(this._speed, other._speed))
  );
};

Cesium.TrailWallMaterialProperty = TrailWallMaterialProperty;

const type = "TrailWall";

const source = `
uniform sampler2D image;
uniform float speed;
uniform vec4 color;

czm_material czm_getMaterial(czm_materialInput materialInput){
  czm_material material = czm_getDefaultMaterial(materialInput);
  vec2 st = materialInput.st;
  float time = fract(czm_frameNumber * speed / 1000.0);
  vec4 colorImage = texture2D(image, vec2(fract(st.t - time), st.t));
  if(color.a == 0.0){
   material.alpha = colorImage.a;
   material.diffuse = colorImage.rgb;
  }else{
   material.alpha = colorImage.a * color.a;
   material.diffuse = max(color.rgb * material.alpha * 3.0, color.rgb);
  }
  return material;
}
`;

Cesium.Material._materialCache.addMaterial(type, {
  fabric: {
    type,
    uniforms: {
      color: defaultColor,
      image: fencePng,
      speed: defaultSpeed,
    },
    source,
  },
  translucent: function (material) {
    return true;
  },
});
