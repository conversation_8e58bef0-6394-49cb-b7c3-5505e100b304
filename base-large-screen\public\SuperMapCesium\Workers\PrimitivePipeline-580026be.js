define(["exports","./buildModuleUrl-8958744c","./ComponentDatatype-c140a87d","./when-b60132fc","./Check-7b2a090c","./Cartesian2-47311507","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./GeometryPipeline-44c6c124","./IndexDatatype-8a5eead4","./FeatureDetection-806b12f0","./WebMercatorProjection-01b1b5e7"],(function(e,t,r,n,i,o,a,s,d,p,u,c){"use strict";function f(e,t,r){e=n.defaultValue(e,0),t=n.defaultValue(t,0),r=n.defaultValue(r,0),this.value=new Float32Array([e,t,r])}function m(e,t){var n=e.attributes,i=n.position,o=i.values.length/i.componentsPerAttribute;n.batchId=new a.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:1,values:new Float32Array(o)});for(var s=n.batchId.values,d=0;d<o;++d)s[d]=t}function l(e){var i,o,a=e.instances,s=e.projection,p=e.elementIndexUintSupported,c=e.scene3DOnly,f=e.vertexCacheOptimize,l=e.compressVertices,h=e.modelMatrix,g=a.length;for(i=0;i<g;++i)if(n.defined(a[i].geometry)){a[i].geometry.primitiveType;break}if(function(e,t,r){var i,o=!r,a=e.length;if(!o&&a>1){var s=e[0].modelMatrix;for(i=1;i<a;++i)if(!u.Matrix4.equals(s,e[i].modelMatrix)){o=!0;break}}if(o)for(i=0;i<a;++i)n.defined(e[i].geometry)&&d.GeometryPipeline.transformToWorldCoordinates(e[i]);else u.Matrix4.multiplyTransformation(t,e[0].modelMatrix,t)}(a,h,c),!c)for(i=0;i<g;++i)n.defined(a[i].geometry)&&d.GeometryPipeline.splitLongitude(a[i]);if(function(e){for(var t=e.length,r=0;r<t;++r){var i=e[r];n.defined(i.geometry)?m(i.geometry,r):n.defined(i.westHemisphereGeometry)&&n.defined(i.eastHemisphereGeometry)&&(m(i.westHemisphereGeometry,r),m(i.eastHemisphereGeometry,r))}}(a),f)for(i=0;i<g;++i){var y=a[i];n.defined(y.geometry)?(d.GeometryPipeline.reorderForPostVertexCache(y.geometry),d.GeometryPipeline.reorderForPreVertexCache(y.geometry)):n.defined(y.westHemisphereGeometry)&&n.defined(y.eastHemisphereGeometry)&&(d.GeometryPipeline.reorderForPostVertexCache(y.westHemisphereGeometry),d.GeometryPipeline.reorderForPreVertexCache(y.westHemisphereGeometry),d.GeometryPipeline.reorderForPostVertexCache(y.eastHemisphereGeometry),d.GeometryPipeline.reorderForPreVertexCache(y.eastHemisphereGeometry))}var b=d.GeometryPipeline.combineInstances(a);for(g=b.length,i=0;i<g;++i){var v,G=(o=b[i]).attributes;if(c)for(v in G)G.hasOwnProperty(v)&&G[v].componentDatatype===r.ComponentDatatype.DOUBLE&&d.GeometryPipeline.encodeAttribute(o,v,v+"3DHigh",v+"3DLow");else for(v in G)if(G.hasOwnProperty(v)&&G[v].componentDatatype===r.ComponentDatatype.DOUBLE){var x=v+"3D",S=v+"2D";d.GeometryPipeline.projectTo2D(o,v,x,S,s),n.defined(o.boundingSphere)&&"position"===v&&(o.boundingSphereCV=t.BoundingSphere.fromVertices(o.attributes.position2D.values)),d.GeometryPipeline.encodeAttribute(o,x,x+"High",x+"Low"),d.GeometryPipeline.encodeAttribute(o,S,S+"High",S+"Low")}l&&d.GeometryPipeline.compressVertices(o)}if(!p){var P=[];for(g=b.length,i=0;i<g;++i)o=b[i],P=P.concat(d.GeometryPipeline.fitToUnsignedShortIndices(o));b=P}return b}function h(e,t,r,i){var o,a,s,d=i.length-1;if(d>=0){var p=i[d];o=p.offset+p.count,a=r[s=p.index].indices.length}else o=0,a=r[s=0].indices.length;for(var u=e.length,c=0;c<u;++c){var f=e[c][t];if(n.defined(f)){var m=f.indices.length;o+m>a&&(o=0,a=r[++s].indices.length),i.push({index:s,offset:o,count:m}),o+=m}}}Object.defineProperties(f.prototype,{componentDatatype:{get:function(){return r.ComponentDatatype.FLOAT}},componentsPerAttribute:{get:function(){return 3}},normalize:{get:function(){return!1}}}),f.fromCartesian3=function(e){return new f(e.x,e.y,e.z)},f.toValue=function(e,t){return n.defined(t)||(t=new Float32Array([e.x,e.y,e.z])),t[0]=e.x,t[1]=e.y,t[2]=e.z,t};var g={};function y(e,t){var r=e.attributes;for(var i in r)if(r.hasOwnProperty(i)){var o=r[i];n.defined(o)&&n.defined(o.values)&&t.push(o.values.buffer)}n.defined(e.indices)&&t.push(e.indices.buffer)}function b(e,t){var r=e.length,i=new Float64Array(1+19*r),o=0;i[o++]=r;for(var a=0;a<r;a++){var s=e[a];if(u.Matrix4.pack(s.modelMatrix,i,o),o+=u.Matrix4.packedLength,n.defined(s.attributes)&&n.defined(s.attributes.offset)){var d=s.attributes.offset.value;i[o]=d[0],i[o+1]=d[1],i[o+2]=d[2]}o+=3}return t.push(i.buffer),i}function v(e){var r=e.length,i=1+(t.BoundingSphere.packedLength+1)*r,o=new Float32Array(i),a=0;o[a++]=r;for(var s=0;s<r;++s){var d=e[s];n.defined(d)?(o[a++]=1,t.BoundingSphere.pack(e[s],o,a)):o[a++]=0,a+=t.BoundingSphere.packedLength}return o}function G(e){for(var r=new Array(e[0]),n=0,i=1;i<e.length;)1===e[i++]&&(r[n]=t.BoundingSphere.unpack(e,i)),++n,i+=t.BoundingSphere.packedLength;return r}g.combineGeometry=function(e){var r,i,o,a,s=e.instances,p=s.length,u=!1;p>0&&((r=l(e)).length>0&&(i=d.GeometryPipeline.createAttributeLocations(r[0]),e.createPickOffsets&&(o=function(e,t){var r=[];return h(e,"geometry",t,r),h(e,"westHemisphereGeometry",t,r),h(e,"eastHemisphereGeometry",t,r),r}(s,r))),n.defined(s[0].attributes)&&n.defined(s[0].attributes.offset)&&(a=new Array(p),u=!0));for(var c=new Array(p),f=new Array(p),m=0;m<p;++m){var g=s[m],y=g.geometry;n.defined(y)&&(c[m]=y.boundingSphere,f[m]=y.boundingSphereCV,u&&(a[m]=g.geometry.offsetAttribute));var b=g.eastHemisphereGeometry,v=g.westHemisphereGeometry;n.defined(b)&&n.defined(v)&&(n.defined(b.boundingSphere)&&n.defined(v.boundingSphere)&&(c[m]=t.BoundingSphere.union(b.boundingSphere,v.boundingSphere)),n.defined(b.boundingSphereCV)&&n.defined(v.boundingSphereCV)&&(f[m]=t.BoundingSphere.union(b.boundingSphereCV,v.boundingSphereCV)))}return{geometries:r,modelMatrix:e.modelMatrix,attributeLocations:i,pickOffsets:o,offsetInstanceExtend:a,boundingSpheres:c,boundingSpheresCV:f}},g.packCreateGeometryResults=function(e,r){var i=new Float64Array(function(e){for(var r=1,i=e.length,o=0;o<i;o++){var a=e[o];if(++r,n.defined(a)){var s=a.attributes;for(var d in r+=7+2*t.BoundingSphere.packedLength+(n.defined(a.indices)?a.indices.length:0),s)s.hasOwnProperty(d)&&n.defined(s[d])&&(r+=6+s[d].values.length)}}return r}(e)),o=[],a={},s=e.length,d=0;i[d++]=s;for(var p=0;p<s;p++){var u=e[p],c=n.defined(u);if(i[d++]=c?1:0,c){i[d++]=u.primitiveType,i[d++]=u.geometryType,i[d++]=n.defaultValue(u.offsetAttribute,-1);var f=n.defined(u.boundingSphere)?1:0;i[d++]=f,f&&t.BoundingSphere.pack(u.boundingSphere,i,d),d+=t.BoundingSphere.packedLength;var m=n.defined(u.boundingSphereCV)?1:0;i[d++]=m,m&&t.BoundingSphere.pack(u.boundingSphereCV,i,d),d+=t.BoundingSphere.packedLength;var l=u.attributes,h=[];for(var g in l)l.hasOwnProperty(g)&&n.defined(l[g])&&(h.push(g),n.defined(a[g])||(a[g]=o.length,o.push(g)));i[d++]=h.length;for(var y=0;y<h.length;y++){var b=h[y],v=l[b];i[d++]=a[b],i[d++]=v.componentDatatype,i[d++]=v.componentsPerAttribute,i[d++]=v.normalize?1:0,i[d++]=v.isInstanceAttribute?1:0,i[d++]=v.values.length,i.set(v.values,d),d+=v.values.length}var G=n.defined(u.indices)?u.indices.length:0;i[d++]=G,G>0&&(i.set(u.indices,d),d+=G)}}return r.push(i.buffer),{stringTable:o,packedData:i}},g.unpackCreateGeometryResults=function(e){for(var n,i=e.stringTable,o=e.packedData,d=new Array(o[0]),u=0,c=1;c<o.length;){if(1===o[c++]){var f,m,l,h,g,y=o[c++],b=o[c++],v=o[c++];-1===v&&(v=void 0),1===o[c++]&&(f=t.BoundingSphere.unpack(o,c)),c+=t.BoundingSphere.packedLength,1===o[c++]&&(m=t.BoundingSphere.unpack(o,c)),c+=t.BoundingSphere.packedLength;var G,x=new s.GeometryAttributes,S=o[c++];for(n=0;n<S;n++){var P=i[o[c++]],k=o[c++];g=o[c++];var C=0!==o[c++],w=0!==o[c++];l=o[c++],h=r.ComponentDatatype.createTypedArray(k,l);for(var A=0;A<l;A++)h[A]=o[c++];x[P]=new a.GeometryAttribute({componentDatatype:k,componentsPerAttribute:g,normalize:C,values:h}),w&&(x[P].isInstanceAttribute=!0)}if((l=o[c++])>0){var V=h.length/g;for(G=p.IndexDatatype.createTypedArray(V,l),n=0;n<l;n++)G[n]=o[c++]}d[u++]=new a.Geometry({primitiveType:y,geometryType:b,boundingSphere:f,boundingSphereCV:m,indices:G,attributes:x,offsetAttribute:v})}else d[u++]=void 0}return d},g.packCombineGeometryParameters=function(e,r){for(var n=e.createGeometryResults,i=n.length,o=0;o<i;o++)r.push(n[o].packedData.buffer);return{createGeometryResults:e.createGeometryResults,packedInstances:b(e.instances,r),ellipsoid:e.ellipsoid,isGeographic:e.projection instanceof t.GeographicProjection,elementIndexUintSupported:e.elementIndexUintSupported,scene3DOnly:e.scene3DOnly,vertexCacheOptimize:e.vertexCacheOptimize,compressVertices:e.compressVertices,modelMatrix:e.modelMatrix,createPickOffsets:e.createPickOffsets}},g.unpackCombineGeometryParameters=function(e){for(var r=function(e){for(var t=e,r=new Array(t[0]),i=0,o=1;o<t.length;){var a,s=u.Matrix4.unpack(t,o);o+=u.Matrix4.packedLength,n.defined(t[o])&&(a={offset:new f(t[o],t[o+1],t[o+2])}),o+=3,r[i++]={modelMatrix:s,attributes:a}}return r}(e.packedInstances),i=e.createGeometryResults,a=i.length,s=0,d=0;d<a;d++)for(var p=g.unpackCreateGeometryResults(i[d]),m=p.length,l=0;l<m;l++){var h=p[l];r[s].geometry=h,++s}var y=o.Ellipsoid.clone(e.ellipsoid);return{instances:r,ellipsoid:y,projection:e.isGeographic?new t.GeographicProjection(y):new c.WebMercatorProjection(y),elementIndexUintSupported:e.elementIndexUintSupported,scene3DOnly:e.scene3DOnly,vertexCacheOptimize:e.vertexCacheOptimize,compressVertices:e.compressVertices,modelMatrix:u.Matrix4.clone(e.modelMatrix),createPickOffsets:e.createPickOffsets}},g.packCombineGeometryResults=function(e,t){n.defined(e.geometries)&&function(e,t){for(var r=e.length,n=0;n<r;++n)y(e[n],t)}(e.geometries,t);var r=v(e.boundingSpheres),i=v(e.boundingSpheresCV);return t.push(r.buffer,i.buffer),{geometries:e.geometries,attributeLocations:e.attributeLocations,modelMatrix:e.modelMatrix,pickOffsets:e.pickOffsets,offsetInstanceExtend:e.offsetInstanceExtend,boundingSpheres:r,boundingSpheresCV:i}},g.unpackCombineGeometryResults=function(e){return{geometries:e.geometries,attributeLocations:e.attributeLocations,modelMatrix:e.modelMatrix,pickOffsets:e.pickOffsets,offsetInstanceExtend:e.offsetInstanceExtend,boundingSpheres:G(e.boundingSpheres),boundingSpheresCV:G(e.boundingSpheresCV)}},e.PrimitivePipeline=g}));
