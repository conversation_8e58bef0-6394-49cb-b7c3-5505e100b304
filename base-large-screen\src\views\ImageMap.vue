<template>
  <OlMap base="IMAGE" :image-extent="imageExtent" :view-extent="viewExtent" :image="mapPng" />
</template>
<script setup>
import OlMap from "../components/olMap/index.vue";
import mapPng from '../assets/images/map.png';

const imageExtent = [0, 0, 966, 834];
//mapObj.getView().calculateExtent(mapObj.getSize());
const viewExtent = [-958, -430, 1916, 1186];

</script>
<style scoped>
@font-face {
  font-family: digital;
  src: url('../assets/fonts/digital.ttf')
}
</style>