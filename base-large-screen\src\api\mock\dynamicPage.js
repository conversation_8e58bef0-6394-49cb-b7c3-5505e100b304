export default {
  url: /\/api\/dynamicPage\/.+/,
  method: "get",
  response: () => {
    return {
      title: "动态大屏示例",
      pages: [
        {
          name: "测试一",
          routePath: "/ceshi1",
          layout: "adapt",
          pageSize: { width: 1920, height: 1080 },
          layers: {
            firstLayer: [{ key: "/src/shandongViews/Background.vue", style: { top: "0px", left: "0px" }, animation: {} }],
            thirdLayer: [
              { key: "/src/shandongViews/BlockOne.vue", style: { top: "115px", left: "30px" }, animation: { type: "LeftSlide", startTime: 0 } },
              {
                key: "/src/shandongViews/BlockTwo.vue",
                style: { top: "78px", right: "30px", height: "429px", width: "343px" },
                animation: { type: "RightSlide", startTime: 0 },
              },
              {
                key: "/src/shandongViews/BlockTwo.vue",
                style: { top: "540px", right: "30px", width: "343px", height: "422px" },
                animation: { type: "RightSlide", startTime: 0.5 },
                focus: true,
              },
              {
                key: "/src/shandongViews/BlockOne.vue",
                style: { top: "596px", left: "30px" },
                animation: { type: "LeftSlide", startTime: 0.5 },
                focus: true,
              },
            ],
            fourthLayer: [{ key: "/src/shandongViews/DynamicHeader.vue", style: { top: "0px", left: "0px" }, animation: {} }],
          },
        },
        {
          name: "测试二",
          routePath: "/ceshi2",
          layout: "adapt",
          pageSize: { width: 1920, height: 1080 },
          layers: {
            firstLayer: [{ key: "/src/shandongViews/Background.vue", style: { top: "0px", left: "0px" }, animation: {} }],
            thirdLayer: [
              {
                key: "/src/shandongViews/BlockOne.vue",
                style: { height: "316px", width: "400px", top: "94px", left: "30px" },
                animation: { type: "LeftSlide", startTime: 0 },
              },
              {
                key: "/src/shandongViews/BlockTwo.vue",
                style: { top: "78px", right: "30px", height: "429px", width: "343px" },
                animation: { type: "RightSlide", startTime: 0 },
              },
              {
                key: "/src/shandongViews/BlockTwo.vue",
                style: { top: "540px", right: "30px", width: "343px", height: "422px" },
                animation: { type: "RightSlide", startTime: 0.5 },
                focus: true,
              },
              {
                key: "/src/shandongViews/BlockOne.vue",
                style: { height: "316px", width: "400px", top: "424px", left: "30px" },
                animation: { type: "LeftSlide", startTime: 0.5 },
                focus: true,
              },
              {
                key: "/src/shandongViews/BlockOne.vue",
                style: { width: "400px", height: "316px", top: "750px", left: "30px" },
                animation: { type: "LeftSlide", startTime: 1 },
                focus: true,
              },
            ],
            fourthLayer: [{ key: "/src/shandongViews/DynamicHeader.vue", style: { top: "0px", left: "0px" }, animation: {} }],
          },
        },
        {
          name: "测试三",
          routePath: "/ceshi3",
          layout: "default",
          pageSize: { width: 1920, height: 1080 },
          layers: {
            firstLayer: [{ key: "/src/shandongViews/Background.vue", style: { top: "0px", left: "0px", backgroundColor: "#a5a5a5" }, animation: {} }],
            thirdLayer: [
              { key: "/src/shandongViews/BlockOne.vue", style: { top: "115px", left: "30px" }, animation: { type: "LeftSlide", startTime: 0 } },
              {
                key: "/src/shandongViews/BlockOne.vue",
                style: { top: "596px", left: "30px" },
                animation: { type: "LeftSlide", startTime: 0.5 },
                focus: true,
              },
              { key: "/src/shandongViews/BlockThree.vue", style: { top: "136px", right: "30px" }, animation: { type: "RightSlide", startTime: 0 } },
              { key: "/src/shandongViews/BlockThree.vue", style: { top: "593px", right: "30px" }, animation: { type: "RightSlide", startTime: 0.5 } },
              { key: "/src/shandongViews/BlockTwo.vue", style: { top: "108px", height: "393px" }, animation: { type: "TopSlide", startTime: 1 } },
              { key: "/src/shandongViews/BlockTwo.vue", style: { height: "412px", top: "569px" }, animation: { type: "TopSlide", startTime: 1.5 } },
            ],
            fourthLayer: [{ key: "/src/shandongViews/DynamicHeader.vue", style: { top: "0px", left: "0px" }, animation: {} }],
          },
        },
        {
          name: "测试四",
          routePath: "/ceshi4",
          layout: "default",
          pageSize: { width: 1920, height: 1080 },
          layers: {
            firstLayer: [{ key: "/src/shandongViews/Background.vue", style: { top: "0px", left: "0px", backgroundColor: "#a5a5a5" }, animation: {} }],
            thirdLayer: [
              { key: "/src/shandongViews/BlockOne.vue", style: { top: "115px", left: "30px" }, animation: { type: "LeftSlide", startTime: 0 } },
              {
                key: "/src/shandongViews/BlockOne.vue",
                style: { top: "596px", left: "30px" },
                animation: { type: "LeftSlide", startTime: 0.5 },
                focus: true,
              },
              { key: "/src/shandongViews/BlockThree.vue", style: { top: "136px", right: "30px" }, animation: { type: "RightSlide", startTime: 0 } },
              { key: "/src/shandongViews/BlockThree.vue", style: { top: "593px", right: "30px" }, animation: { type: "RightSlide", startTime: 0.5 } },
              { key: "/src/shandongViews/BlockTwo.vue", style: { top: "282px" }, animation: { type: "TopSlide", startTime: 1 } },
            ],
            fourthLayer: [{ key: "/src/shandongViews/DynamicHeader.vue", style: { top: "0px", left: "0px" }, animation: {} }],
          },
        },
        {
          name: "测试五",
          routePath: "/ceshi5",
          layout: "adapt",
          pageSize: { width: 1920, height: 1080 },
          layers: {
            firstLayer: [{ key: "/src/shandongViews/Background.vue", style: { top: "0px", left: "0px", backgroundColor: "#a5a5a5" }, animation: {} }],
            thirdLayer: [
              { key: "/src/shandongViews/BlockOne.vue", style: { top: "115px", left: "30px" }, animation: { type: "LeftSlide", startTime: 0 } },
              {
                key: "/src/shandongViews/BlockOne.vue",
                style: { top: "596px", left: "30px" },
                animation: { type: "LeftSlide", startTime: 0.5 },
                focus: true,
              },
              { key: "/src/shandongViews/BlockThree.vue", style: { top: "136px", right: "30px" }, animation: { type: "RightSlide", startTime: 0 } },
              { key: "/src/shandongViews/BlockThree.vue", style: { top: "593px", right: "30px" }, animation: { type: "RightSlide", startTime: 0.5 } },
              {
                key: "/src/shandongViews/BlockTwo.vue",
                style: { width: "calc(100% - 1000px)", top: "345px", left: "549px" },
                animation: { type: "TopSlide", startTime: 1 },
              },
            ],
            fourthLayer: [{ key: "/src/shandongViews/DynamicHeader.vue", style: { top: "0px", left: "0px" }, animation: {} }],
          },
        },
      ],
    };
  },
};
