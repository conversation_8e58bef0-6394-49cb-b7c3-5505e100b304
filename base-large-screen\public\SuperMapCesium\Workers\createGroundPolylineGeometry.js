define(["./buildModuleUrl-8958744c","./Cartesian2-47311507","./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./Math-119be1a3","./ArcType-29cf2197","./arrayRemoveDuplicates-d2f048c5","./ComponentDatatype-c140a87d","./EllipsoidGeodesic-0f19ac62","./EllipsoidRhumbLine-ed1a6bf4","./EncodedCartesian3-f1396b05","./GeometryAttribute-06a41648","./IntersectionTests-a793ed08","./FeatureDetection-806b12f0","./Plane-a3d8b3d2","./WebMercatorProjection-01b1b5e7","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian4-3ca25aab"],(function(e,a,t,i,n,r,s,o,l,u,c,h,C,d,p,g,f,m,v,w,y){"use strict";function _(t){t=n.defaultValue(t,{}),this._ellipsoid=n.defaultValue(t.ellipsoid,a.Ellipsoid.WGS84),this._rectangle=n.defaultValue(t.rectangle,a.Rectangle.MAX_VALUE),this._projection=new e.GeographicProjection(this._ellipsoid),this._numberOfLevelZeroTilesX=n.defaultValue(t.numberOfLevelZeroTilesX,2),this._numberOfLevelZeroTilesY=n.defaultValue(t.numberOfLevelZeroTilesY,1),this._customDPI=t.customDPI,this._scaleDenominators=t.scaleDenominators,this._tileWidth=n.defaultValue(t.tileWidth,256),this._tileHeight=n.defaultValue(t.tileHeight,256),this._beginLevel=n.defaultValue(t.beginLevel,0)}Object.defineProperties(_.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},rectangle:{get:function(){return this._rectangle}},projection:{get:function(){return this._projection}},beginLevel:{get:function(){return this._beginLevel}}}),_.prototype.getNumberOfXTilesAtLevel=function(e){if(n.defined(this._customDPI)&&n.defined(this._scaleDenominators)){var a=this.calculateResolution(e),t=this._tileWidth*a.x;return Math.ceil(this._rectangle.width/t)}return this._numberOfLevelZeroTilesX<<e-this._beginLevel},_.prototype.getNumberOfYTilesAtLevel=function(e){if(n.defined(this._customDPI)&&n.defined(this._scaleDenominators)){var a=this.calculateResolution(e),t=this._tileHeight*a.y;return Math.ceil(this._rectangle.height/t)}return this._numberOfLevelZeroTilesY<<e-this._beginLevel},_.prototype.rectangleToNativeRectangle=function(e,t){var i=r.CesiumMath.toDegrees(e.west),s=r.CesiumMath.toDegrees(e.south),o=r.CesiumMath.toDegrees(e.east),l=r.CesiumMath.toDegrees(e.north);return n.defined(t)?(t.west=i,t.south=s,t.east=o,t.north=l,t):new a.Rectangle(i,s,o,l)},_.prototype.tileXYToNativeRectangle=function(e,a,t,i){var n=this.tileXYToRectangle(e,a,t,i);return n.west=r.CesiumMath.toDegrees(n.west),n.south=r.CesiumMath.toDegrees(n.south),n.east=r.CesiumMath.toDegrees(n.east),n.north=r.CesiumMath.toDegrees(n.north),n},_.prototype.tileXYToRectangle=function(e,t,i,r){var s=this._rectangle;if(n.defined(this._customDPI)&&n.defined(this._scaleDenominators)){var o=this.calculateResolution(i),l=s.west+e*this._tileWidth*o.x,u=s.west+(e+1)*this._tileWidth*o.x,c=s.north-t*this._tileHeight*o.y,h=s.north-(t+1)*this._tileHeight*o.y;return n.defined(r)?(r.west=l,r.south=h,r.east=u,r.north=c,r):new a.Rectangle(l,h,u,c)}var C=this.getNumberOfXTilesAtLevel(i),d=this.getNumberOfYTilesAtLevel(i),p=s.width/C,g=(l=e*p+s.west,u=(e+1)*p+s.west,s.height/d);c=s.north-t*g,h=s.north-(t+1)*g;return n.defined(r)||(r=new a.Rectangle(l,h,u,c)),r.west=l,r.south=h,r.east=u,r.north=c,r},_.prototype.positionToTileXY=function(e,t,i){var s=this._rectangle;if(a.Rectangle.contains(s,e)){var o=this.getNumberOfXTilesAtLevel(t),l=this.getNumberOfYTilesAtLevel(t),u=s.width/o,c=s.height/l;if(n.defined(this._customDPI)&&n.defined(this._scaleDenominators)){var h=this.calculateResolution(t);u=this._tileWidth*h.x,c=this._tileHeight*h.y}var C=e.longitude;s.east<s.west&&(C+=r.CesiumMath.TWO_PI);var d=(C-s.west)/u|0;d>=o&&(d=o-1);var p=(s.north-e.latitude)/c|0;return p>=l&&(p=l-1),n.defined(i)?(i.x=d,i.y=p,i):new a.Cartesian2(d,p)}},_.prototype.calculateResolution=function(e){var t=.0254*this._scaleDenominators[e-this._beginLevel]/this._customDPI.x,i=.0254*this._scaleDenominators[e-this._beginLevel]/this._customDPI.y,n=a.Ellipsoid.WGS84.maximumRadius;return new a.Cartesian2(t/n,i/n)};var T=new t.Cartesian3,M=new t.Cartesian3,E=new t.Cartographic,b=new t.Cartesian3,P=new t.Cartesian3,O=new e.BoundingSphere,I=new _,L=[new t.Cartographic,new t.Cartographic,new t.Cartographic,new t.Cartographic],D=new a.Cartesian2,S={};function k(e){t.Cartographic.fromRadians(e.east,e.north,0,L[0]),t.Cartographic.fromRadians(e.west,e.north,0,L[1]),t.Cartographic.fromRadians(e.east,e.south,0,L[2]),t.Cartographic.fromRadians(e.west,e.south,0,L[3]);var a,i=0,n=0,r=0,s=0,o=S._terrainHeightsMaxLevel;for(a=0;a<=o;++a){for(var l=!1,u=0;u<4;++u){var c=L[u];if(I.positionToTileXY(c,a,D),0===u)r=D.x,s=D.y;else if(r!==D.x||s!==D.y){l=!0;break}}if(l)break;i=r,n=s}if(0!==a)return{x:i,y:n,level:a>o?o:a-1}}S.initialize=function(){var a=S._initPromise;return n.defined(a)||(a=e.Resource.fetchJson(e.buildModuleUrl("Assets/approximateTerrainHeights.json")).then((function(e){S._terrainHeights=e})),S._initPromise=a),a},S.getMinimumMaximumHeights=function(e,i){i=n.defaultValue(i,a.Ellipsoid.WGS84);var r=k(e),s=S._defaultMinTerrainHeight,o=S._defaultMaxTerrainHeight;if(n.defined(r)){var l=r.level+"-"+r.x+"-"+r.y,u=S._terrainHeights[l];n.defined(u)&&(s=u[0],o=u[1]),i.cartographicToCartesian(a.Rectangle.northeast(e,E),T),i.cartographicToCartesian(a.Rectangle.southwest(e,E),M),t.Cartesian3.midpoint(M,T,b);var c=i.scaleToGeodeticSurface(b,P);if(n.defined(c)){var h=t.Cartesian3.distance(b,c);s=Math.min(s,-h)}else s=S._defaultMinTerrainHeight}return{minimumTerrainHeight:s=Math.max(S._defaultMinTerrainHeight,s),maximumTerrainHeight:o}},S.getBoundingSphere=function(t,i){i=n.defaultValue(i,a.Ellipsoid.WGS84);var r=k(t),s=S._defaultMaxTerrainHeight;if(n.defined(r)){var o=r.level+"-"+r.x+"-"+r.y,l=S._terrainHeights[o];n.defined(l)&&(s=l[1])}var u=e.BoundingSphere.fromRectangle3D(t,i,0);return e.BoundingSphere.fromRectangle3D(t,i,s,O),e.BoundingSphere.union(u,O,u)},S._terrainHeightsMaxLevel=6,S._defaultMaxTerrainHeight=9e3,S._defaultMinTerrainHeight=-1e5,S._terrainHeights=void 0,S._initPromise=void 0,Object.defineProperties(S,{initialized:{get:function(){return n.defined(S._terrainHeights)}}});var A=[e.GeographicProjection,f.WebMercatorProjection],x=A.length,N=Math.cos(r.CesiumMath.toRadians(30)),R=Math.cos(r.CesiumMath.toRadians(150));function H(e){var t=(e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT)).positions;this.width=n.defaultValue(e.width,1),this._positions=t,this.granularity=n.defaultValue(e.granularity,9999),this.loop=n.defaultValue(e.loop,!1),this.arcType=n.defaultValue(e.arcType,s.ArcType.GEODESIC),this._ellipsoid=n.defaultValue(e.ellipsoid,a.Ellipsoid.WGS84),this._projectionIndex=0,this._workerName="createGroundPolylineGeometry",this._scene3DOnly=!1}Object.defineProperties(H.prototype,{packedLength:{get:function(){return 1+3*this._positions.length+1+1+1+a.Ellipsoid.packedLength+1+1}}}),H.setProjectionAndEllipsoid=function(e,a){for(var t=0,i=0;i<x;i++)if(a instanceof A[i]){t=i;break}e._projectionIndex=t,e._ellipsoid=a.ellipsoid};var z=new t.Cartesian3,V=new t.Cartesian3,j=new t.Cartesian3;function G(e,a,i,n,r){var s=U(n,e,0,z),o=U(n,e,i,V),l=U(n,a,0,j),u=Z(o,s,V),c=Z(l,s,j);return t.Cartesian3.cross(c,u,r),t.Cartesian3.normalize(r,r)}var B=new t.Cartographic,W=new t.Cartesian3,F=new t.Cartesian3,Y=new t.Cartesian3;function X(e,a,i,n,r,o,l,h,C,d,p){if(0!==r){var g;o===s.ArcType.GEODESIC?g=new u.EllipsoidGeodesic(e,a,l):o===s.ArcType.RHUMB&&(g=new c.EllipsoidRhumbLine(e,a,l));var f=g.surfaceDistance;if(!(f<r))for(var m=G(e,a,n,l,Y),v=Math.ceil(f/r),w=f/v,y=w,_=v-1,T=h.length,M=0;M<_;M++){var E=g.interpolateUsingSurfaceDistance(y,B),b=U(l,E,i,W),P=U(l,E,n,F);t.Cartesian3.pack(m,h,T),t.Cartesian3.pack(b,C,T),t.Cartesian3.pack(P,d,T),p.push(E.latitude),p.push(E.longitude),T+=3,y+=w}}}var q=new t.Cartographic;function U(e,a,i,n){return t.Cartographic.clone(a,q),q.height=i,t.Cartographic.toCartesian(q,e,n)}function Z(e,a,i){return t.Cartesian3.subtract(e,a,i),t.Cartesian3.normalize(i,i),i}H.pack=function(e,i,r){var s=n.defaultValue(r,0),o=e._positions,l=o.length;i[s++]=l;for(var u=0;u<l;++u){var c=o[u];t.Cartesian3.pack(c,i,s),s+=3}return i[s++]=e.granularity,i[s++]=e.loop?1:0,i[s++]=e.arcType,a.Ellipsoid.pack(e._ellipsoid,i,s),s+=a.Ellipsoid.packedLength,i[s++]=e._projectionIndex,i[s++]=e._scene3DOnly?1:0,i},H.unpack=function(e,i,r){for(var s=n.defaultValue(i,0),o=e[s++],l=new Array(o),u=0;u<o;u++)l[u]=t.Cartesian3.unpack(e,s),s+=3;var c=e[s++],h=1===e[s++],C=e[s++],d=a.Ellipsoid.unpack(e,s);s+=a.Ellipsoid.packedLength;var p=e[s++],g=1===e[s++];if(!n.defined(r)){var f=new H({positions:l,granularity:c,loop:h,arcType:C,ellipsoid:d});return f._projectionIndex=p,f._scene3DOnly=g,f}return r._positions=l,r.granularity=c,r.loop=h,r.arcType=C,r._ellipsoid=d,r._projectionIndex=p,r._scene3DOnly=g,r};var Q=new t.Cartesian3,J=new t.Cartesian3,K=new t.Cartesian3,$=new t.Cartesian3,ee=new g.Plane(t.Cartesian3.UNIT_X,0),ae=new t.Cartesian3;function te(e,a,i,n,s){var o=Z(i,a,ae),l=Z(e,a,Q),u=Z(n,a,J),c=t.Cartesian3.cross(o,l,$);c=t.Cartesian3.normalize(c,c);var h=g.Plane.fromPointNormal(a,c,ee),C=g.Plane.getPointDistance(h,n);if(r.CesiumMath.equalsEpsilon(C,0,r.CesiumMath.EPSILON7))return t.Cartesian3.clone(c,s),s;s=t.Cartesian3.add(u,l,s),s=t.Cartesian3.normalize(s,s);var d=t.Cartesian3.cross(o,s,K);return t.Cartesian3.normalize(d,d),t.Cartesian3.cross(d,o,s),t.Cartesian3.normalize(s,s),t.Cartesian3.dot(u,d)<0&&(s=t.Cartesian3.negate(s,s)),s}var ie=g.Plane.fromPointNormal(t.Cartesian3.ZERO,t.Cartesian3.UNIT_Y),ne=new t.Cartesian3,re=new t.Cartesian3,se=new t.Cartesian3,oe=new t.Cartesian3,le=new t.Cartesian3,ue=new t.Cartesian3,ce=new t.Cartographic,he=new t.Cartographic,Ce=new t.Cartographic;H.createGeometry=function(i){var u,p,g,f,m,v,w=!i._scene3DOnly,y=i.loop,_=i._ellipsoid,T=i.granularity,M=i.arcType,E=new A[i._projectionIndex](_),b=1e3,P=i._positions,O=P.length;2===O&&(y=!1);var I,L,D,k=new c.EllipsoidRhumbLine(void 0,void 0,_),x=[P[0]];for(p=0;p<O-1;p++)g=P[p],f=P[p+1],I=d.IntersectionTests.lineSegmentPlane(g,f,ie,ue),!n.defined(I)||t.Cartesian3.equalsEpsilon(I,g,r.CesiumMath.EPSILON7)||t.Cartesian3.equalsEpsilon(I,f,r.CesiumMath.EPSILON7)||(i.arcType===s.ArcType.GEODESIC?x.push(t.Cartesian3.clone(I)):i.arcType===s.ArcType.RHUMB&&(D=_.cartesianToCartographic(I,ce).longitude,m=_.cartesianToCartographic(g,ce),v=_.cartesianToCartographic(f,he),k.setEndPoints(m,v),L=k.findIntersectionWithLongitude(D,Ce),I=_.cartographicToCartesian(L,ue),!n.defined(I)||t.Cartesian3.equalsEpsilon(I,g,r.CesiumMath.EPSILON7)||t.Cartesian3.equalsEpsilon(I,f,r.CesiumMath.EPSILON7)||x.push(t.Cartesian3.clone(I)))),x.push(f);y&&(g=P[O-1],f=P[0],I=d.IntersectionTests.lineSegmentPlane(g,f,ie,ue),!n.defined(I)||t.Cartesian3.equalsEpsilon(I,g,r.CesiumMath.EPSILON7)||t.Cartesian3.equalsEpsilon(I,f,r.CesiumMath.EPSILON7)||(i.arcType===s.ArcType.GEODESIC?x.push(t.Cartesian3.clone(I)):i.arcType===s.ArcType.RHUMB&&(D=_.cartesianToCartographic(I,ce).longitude,m=_.cartesianToCartographic(g,ce),v=_.cartesianToCartographic(f,he),k.setEndPoints(m,v),L=k.findIntersectionWithLongitude(D,Ce),I=_.cartographicToCartesian(L,ue),!n.defined(I)||t.Cartesian3.equalsEpsilon(I,g,r.CesiumMath.EPSILON7)||t.Cartesian3.equalsEpsilon(I,f,r.CesiumMath.EPSILON7)||x.push(t.Cartesian3.clone(I)))));var R=x.length,H=new Array(R);for(p=0;p<R;p++){var z=t.Cartographic.fromCartesian(x[p],_);z.height=0,H[p]=z}if(!((R=(H=o.arrayRemoveDuplicates(H,t.Cartographic.equalsEpsilon)).length)<2)){var V=[],j=[],B=[],W=[],F=ne,Y=re,q=se,Q=oe,J=le,K=H[0],$=H[1];for(F=U(_,H[R-1],0,F),Q=U(_,$,0,Q),Y=U(_,K,0,Y),q=U(_,K,b,q),J=y?te(F,Y,q,Q,J):G(K,$,b,_,J),t.Cartesian3.pack(J,j,0),t.Cartesian3.pack(Y,B,0),t.Cartesian3.pack(q,W,0),V.push(K.latitude),V.push(K.longitude),X(K,$,0,b,T,M,_,j,B,W,V),p=1;p<R-1;++p){F=t.Cartesian3.clone(Y,F),Y=t.Cartesian3.clone(Q,Y);var ee=H[p];U(_,ee,b,q),U(_,H[p+1],0,Q),te(F,Y,q,Q,J),u=j.length,t.Cartesian3.pack(J,j,u),t.Cartesian3.pack(Y,B,u),t.Cartesian3.pack(q,W,u),V.push(ee.latitude),V.push(ee.longitude),X(H[p],H[p+1],0,b,T,M,_,j,B,W,V)}var ae=H[R-1],de=H[R-2];if(Y=U(_,ae,0,Y),q=U(_,ae,b,q),y){var pe=H[0];J=te(F=U(_,de,0,F),Y,q,Q=U(_,pe,0,Q),J)}else J=G(de,ae,b,_,J);if(u=j.length,t.Cartesian3.pack(J,j,u),t.Cartesian3.pack(Y,B,u),t.Cartesian3.pack(q,W,u),V.push(ae.latitude),V.push(ae.longitude),y){for(X(ae,K,0,b,T,M,_,j,B,W,V),u=j.length,p=0;p<3;++p)j[u+p]=j[p],B[u+p]=B[p],W[u+p]=W[p];V.push(K.latitude),V.push(K.longitude)}return function(i,n,s,o,u,c,d){var p,g,f,m,v,w,y=n._ellipsoid,_=s.length/3-1,T=8*_,M=4*T,E=36*_,b=T>65535?new Uint32Array(E):new Uint16Array(E),P=new Float64Array(3*T),O=new Float32Array(M),I=new Float32Array(M),L=new Float32Array(M),D=new Float32Array(M),k=new Float32Array(M);d&&(f=new Float32Array(M),m=new Float32Array(M),v=new Float32Array(M),w=new Float32Array(2*T));var A=c.length/2,x=0,R=Oe;R.height=0;var H=Ie;H.height=0;var z=Le,V=De;if(d)for(g=0,p=1;p<A;p++)R.latitude=c[g],R.longitude=c[g+1],H.latitude=c[g+2],H.longitude=c[g+3],z=n.project(R,z),V=n.project(H,V),x+=t.Cartesian3.distance(z,V),g+=2;var j=o.length/3;V=t.Cartesian3.unpack(o,0,V);var G,B=0;for(g=3,p=1;p<j;p++)z=t.Cartesian3.clone(V,z),V=t.Cartesian3.unpack(o,g,V),B+=t.Cartesian3.distance(z,V),g+=3;g=3;var W=0,F=0,Y=0,X=0,q=!1,U=t.Cartesian3.unpack(s,0,ke),Q=t.Cartesian3.unpack(o,0,De),J=t.Cartesian3.unpack(u,0,xe);if(i){fe(J,t.Cartesian3.unpack(s,s.length-6,Se),U,Q)&&(J=t.Cartesian3.negate(J,J))}var K=0,$=0,ee=0;for(p=0;p<_;p++){var ae,te,ie,ne,re=t.Cartesian3.clone(U,Se),se=t.Cartesian3.clone(Q,Le),oe=t.Cartesian3.clone(J,Ae);if(q&&(oe=t.Cartesian3.negate(oe,oe)),U=t.Cartesian3.unpack(s,g,ke),Q=t.Cartesian3.unpack(o,g,De),q=fe(J=t.Cartesian3.unpack(u,g,xe),re,U,Q),R.latitude=c[W],R.longitude=c[W+1],H.latitude=c[W+2],H.longitude=c[W+3],d){var le=Pe(R,H);ae=n.project(R,Ge);var ue=Z(te=n.project(H,Be),ae,$e);ue.y=Math.abs(ue.y),ie=We,ne=Fe,0===le||t.Cartesian3.dot(ue,t.Cartesian3.UNIT_Y)>N?(ie=ye(n,R,oe,ae,We),ne=ye(n,H,J,te,Fe)):1===le?(ne=ye(n,H,J,te,Fe),ie.x=0,ie.y=r.CesiumMath.sign(R.longitude-Math.abs(H.longitude)),ie.z=0):(ie=ye(n,R,oe,ae,We),ne.x=0,ne.y=r.CesiumMath.sign(R.longitude-H.longitude),ne.z=0)}var ce=t.Cartesian3.distance(se,Q),he=h.EncodedCartesian3.fromCartesian(re,Je),Ce=t.Cartesian3.subtract(U,re,Ye),de=t.Cartesian3.normalize(Ce,Ue),pe=t.Cartesian3.subtract(se,re,Xe);pe=t.Cartesian3.normalize(pe,pe);var ge=t.Cartesian3.cross(de,pe,Ue);ge=t.Cartesian3.normalize(ge,ge);var me=t.Cartesian3.cross(pe,oe,Ze);me=t.Cartesian3.normalize(me,me);var ve=t.Cartesian3.subtract(Q,U,qe);ve=t.Cartesian3.normalize(ve,ve);var we=t.Cartesian3.cross(J,ve,Qe);we=t.Cartesian3.normalize(we,we);var _e,Te,Ee,sa=ce/B,oa=K/B,la=0,ua=0,ca=0;if(d){la=t.Cartesian3.distance(ae,te),_e=h.EncodedCartesian3.fromCartesian(ae,Ke),Te=t.Cartesian3.subtract(te,ae,$e);var ha=(Ee=t.Cartesian3.normalize(Te,ea)).x;Ee.x=Ee.y,Ee.y=-ha,ua=la/x,ca=$/x}for(G=0;G<8;G++){var Ca=X+4*G,da=F+2*G,pa=Ca+3,ga=G<4?1:-1,fa=2===G||3===G||6===G||7===G?1:-1;t.Cartesian3.pack(he.high,O,Ca),O[pa]=Ce.x,t.Cartesian3.pack(he.low,I,Ca),I[pa]=Ce.y,t.Cartesian3.pack(me,L,Ca),L[pa]=Ce.z,t.Cartesian3.pack(we,D,Ca),D[pa]=sa*ga,t.Cartesian3.pack(ge,k,Ca);var ma=oa*fa;0===ma&&fa<0&&(ma=Number.POSITIVE_INFINITY),k[pa]=ma,d&&(f[Ca]=_e.high.x,f[Ca+1]=_e.high.y,f[Ca+2]=_e.low.x,f[Ca+3]=_e.low.y,v[Ca]=-ie.y,v[Ca+1]=ie.x,v[Ca+2]=ne.y,v[Ca+3]=-ne.x,m[Ca]=Te.x,m[Ca+1]=Te.y,m[Ca+2]=Ee.x,m[Ca+3]=Ee.y,w[da]=ua*ga,0===(ma=ca*fa)&&fa<0&&(ma=Number.POSITIVE_INFINITY),w[da+1]=ma)}var va=Ve,wa=je,ya=He,_a=ze,Ta=a.Rectangle.fromCartographicArray(Ne,Re),Ma=S.getMinimumMaximumHeights(Ta,y),Ea=Ma.minimumTerrainHeight,ba=Ma.maximumTerrainHeight;ee+=Ea,ee+=ba,Me(re,se,Ea,ba,va,ya),Me(U,Q,Ea,ba,wa,_a);var Pa=t.Cartesian3.multiplyByScalar(ge,r.CesiumMath.EPSILON5,aa);t.Cartesian3.add(va,Pa,va),t.Cartesian3.add(wa,Pa,wa),t.Cartesian3.add(ya,Pa,ya),t.Cartesian3.add(_a,Pa,_a),be(va,wa),be(ya,_a),t.Cartesian3.pack(va,P,Y),t.Cartesian3.pack(wa,P,Y+3),t.Cartesian3.pack(_a,P,Y+6),t.Cartesian3.pack(ya,P,Y+9),Pa=t.Cartesian3.multiplyByScalar(ge,-2*r.CesiumMath.EPSILON5,aa),t.Cartesian3.add(va,Pa,va),t.Cartesian3.add(wa,Pa,wa),t.Cartesian3.add(ya,Pa,ya),t.Cartesian3.add(_a,Pa,_a),be(va,wa),be(ya,_a),t.Cartesian3.pack(va,P,Y+12),t.Cartesian3.pack(wa,P,Y+15),t.Cartesian3.pack(_a,P,Y+18),t.Cartesian3.pack(ya,P,Y+21),W+=2,g+=3,F+=16,Y+=24,X+=32,K+=ce,$+=la}g=0;var Oa=0;for(p=0;p<_;p++){for(G=0;G<na;G++)b[g+G]=ia[G]+Oa;Oa+=8,g+=na}var Ia=ta;e.BoundingSphere.fromVertices(s,t.Cartesian3.ZERO,3,Ia[0]),e.BoundingSphere.fromVertices(o,t.Cartesian3.ZERO,3,Ia[1]);var La=e.BoundingSphere.fromBoundingSpheres(Ia);La.radius+=ee/(2*_);var Da={position:new C.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,normalize:!1,values:P}),startHiAndForwardOffsetX:ra(O),startLoAndForwardOffsetY:ra(I),startNormalAndForwardOffsetZ:ra(L),endNormalAndTextureCoordinateNormalizationX:ra(D),rightNormalAndTextureCoordinateNormalizationY:ra(k)};d&&(Da.startHiLo2D=ra(f),Da.offsetAndRight2D=ra(m),Da.startEndNormals2D=ra(v),Da.texcoordNormalization2D=new C.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:2,normalize:!1,values:w}));return new C.Geometry({attributes:Da,indices:b,boundingSphere:La})}(y,E,B,W,j,V,w)}};var de=new t.Cartesian3,pe=new p.Matrix3,ge=new C.Quaternion;function fe(e,a,i,n){var s=Z(i,a,de),o=t.Cartesian3.dot(s,e);if(o>N||o<R){var l=Z(n,i,ae),u=o<R?r.CesiumMath.PI_OVER_TWO:-r.CesiumMath.PI_OVER_TWO,c=C.Quaternion.fromAxisAngle(l,u,ge),h=p.Matrix3.fromQuaternion(c,pe);return p.Matrix3.multiplyByVector(h,e,e),!0}return!1}var me=new t.Cartographic,ve=new t.Cartesian3,we=new t.Cartesian3;function ye(e,a,i,n,s){var o=t.Cartographic.toCartesian(a,e._ellipsoid,ve),l=t.Cartesian3.add(o,i,we),u=!1,c=e._ellipsoid,h=c.cartesianToCartographic(l,me);Math.abs(a.longitude-h.longitude)>r.CesiumMath.PI_OVER_TWO&&(u=!0,l=t.Cartesian3.subtract(o,i,we),h=c.cartesianToCartographic(l,me)),h.height=0;var C=e.project(h,s);return(s=t.Cartesian3.subtract(C,n,s)).z=0,s=t.Cartesian3.normalize(s,s),u&&t.Cartesian3.negate(s,s),s}var _e=new t.Cartesian3,Te=new t.Cartesian3;function Me(e,a,i,n,r,s){var o=t.Cartesian3.subtract(a,e,_e);t.Cartesian3.normalize(o,o);var l=i-0,u=t.Cartesian3.multiplyByScalar(o,l,Te);t.Cartesian3.add(e,u,r);var c=n-1e3;u=t.Cartesian3.multiplyByScalar(o,c,Te),t.Cartesian3.add(a,u,s)}var Ee=new t.Cartesian3;function be(e,a){var i=g.Plane.getPointDistance(ie,e),n=g.Plane.getPointDistance(ie,a),s=Ee;r.CesiumMath.equalsEpsilon(i,0,r.CesiumMath.EPSILON2)?(s=Z(a,e,s),t.Cartesian3.multiplyByScalar(s,r.CesiumMath.EPSILON2,s),t.Cartesian3.add(e,s,e)):r.CesiumMath.equalsEpsilon(n,0,r.CesiumMath.EPSILON2)&&(s=Z(e,a,s),t.Cartesian3.multiplyByScalar(s,r.CesiumMath.EPSILON2,s),t.Cartesian3.add(a,s,a))}function Pe(e,a){var t=Math.abs(e.longitude),i=Math.abs(a.longitude);if(r.CesiumMath.equalsEpsilon(t,r.CesiumMath.PI,r.CesiumMath.EPSILON11)){var n=r.CesiumMath.sign(a.longitude);return e.longitude=n*(t-r.CesiumMath.EPSILON11),1}if(r.CesiumMath.equalsEpsilon(i,r.CesiumMath.PI,r.CesiumMath.EPSILON11)){var s=r.CesiumMath.sign(e.longitude);return a.longitude=s*(i-r.CesiumMath.EPSILON11),2}return 0}var Oe=new t.Cartographic,Ie=new t.Cartographic,Le=new t.Cartesian3,De=new t.Cartesian3,Se=new t.Cartesian3,ke=new t.Cartesian3,Ae=new t.Cartesian3,xe=new t.Cartesian3,Ne=[Oe,Ie],Re=new a.Rectangle,He=new t.Cartesian3,ze=new t.Cartesian3,Ve=new t.Cartesian3,je=new t.Cartesian3,Ge=new t.Cartesian3,Be=new t.Cartesian3,We=new t.Cartesian3,Fe=new t.Cartesian3,Ye=new t.Cartesian3,Xe=new t.Cartesian3,qe=new t.Cartesian3,Ue=new t.Cartesian3,Ze=new t.Cartesian3,Qe=new t.Cartesian3,Je=new h.EncodedCartesian3,Ke=new h.EncodedCartesian3,$e=new t.Cartesian3,ea=new t.Cartesian3,aa=new t.Cartesian3,ta=[new e.BoundingSphere,new e.BoundingSphere],ia=[0,2,1,0,3,2,0,7,3,0,4,7,0,5,4,0,1,5,5,7,4,5,6,7,5,2,6,5,1,2,3,6,2,3,7,6],na=ia.length;function ra(e){return new C.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:4,normalize:!1,values:e})}return H._projectNormal=ye,function(e,a){return S.initialize().then((function(){return n.defined(a)&&(e=H.unpack(e,a)),H.createGeometry(e)}))}}));
