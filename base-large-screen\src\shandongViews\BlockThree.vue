<template>
  <div class="box">
    <PartTitle :title="props.title" :icon="icon" :position="props.position" />
    <p>{{ store.shandong_ceshi1.type }}</p>
    <p>{{ store.shandong_ceshi1.province }}</p>
  </div>
</template>
<script setup>
import PartTitle from "@/components/common/PartTitle.vue"
import icon from "@/assets/images/store.svg"
import { useDynamicStore } from "@/stores/system/dynamicPage";

const store = useDynamicStore()

const props = defineProps({
  position: {
    type: Number,
    default: 0,
  },
  title: {
    type: String,
    default: '测试模块三',
  },
})

</script>
<style scoped>
.box {
  width: 400px;
  height: 400px;
  background-color: #e3f0aa;
}
</style>
