define(["./PrimitivePipeline-580026be","./createTaskProcessorWorker","./buildModuleUrl-8958744c","./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./Math-119be1a3","./Cartesian2-47311507","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./GeometryPipeline-44c6c124","./AttributeCompression-90851096","./EncodedCartesian3-f1396b05","./IndexDatatype-8a5eead4","./IntersectionTests-a793ed08","./Plane-a3d8b3d2","./WebMercatorProjection-01b1b5e7"],(function(e,t,i,r,a,n,o,c,b,s,m,d,u,P,p,l,C,f,y,G,v,k,h){"use strict";return t((function(t,i){var r=e.PrimitivePipeline.unpackCombineGeometryParameters(t),a=e.PrimitivePipeline.combineGeometry(r);return e.PrimitivePipeline.packCombineGeometryResults(a,i)}))}));
