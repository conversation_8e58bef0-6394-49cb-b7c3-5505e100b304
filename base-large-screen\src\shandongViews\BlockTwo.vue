<template>
  <div class="box">
    <PartTitle :title="props.title" :icon="icon" :position="props.position" />
    <p>{{ store.shandong_ceshi2.province }}</p>
    <p>{{ store.shandong_ceshi2.count }}</p>
  </div>
</template>
<script setup>
import PartTitle from "@/components/common/PartTitle.vue"
import icon from "@/assets/images/store.svg"
import { useDynamicStore } from "@/stores/system/dynamicPage";

const store = useDynamicStore()

const props = defineProps({
  position: {
    type: Number,
    default: 0,
  },
  title: {
    type: String,
    default: '测试模块二',
  },
})

</script>
<style scoped>
.box {
  width: 400px;
  height: 500px;
  background-color: #aaf0e0;
}
</style>
