/**
 * @private
 */
const ExpressionNodeType = {
  VARIABLE: 0,
  UNARY: 1,
  BINARY: 2,
  TERNARY: 3,
  COND<PERSON>IONAL: 4,
  MEMBER: 5,
  FUNCTION_CALL: 6,
  ARRAY: 7,
  REG<PERSON>: 8,
  VA<PERSON><PERSON>LE_IN_STRING: 9,
  <PERSON><PERSON><PERSON><PERSON>_NULL: 10,
  <PERSON><PERSON><PERSON><PERSON>_BOOLEAN: 11,
  <PERSON>IT<PERSON>AL_NUMBER: 12,
  LITERAL_STRING: 13,
  LITERAL_COLOR: 14,
  L<PERSON><PERSON>AL_VECTOR: 15,
  LITERAL_REGEX: 16,
  LITERAL_UNDEFINED: 17,
  BUILTIN_VARIABLE: 18,
};
export default Object.freeze(ExpressionNodeType);
