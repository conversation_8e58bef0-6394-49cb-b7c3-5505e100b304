define(["exports","./Cartesian2-47311507","./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./FeatureDetection-806b12f0","./Math-119be1a3","./Cartesian4-3ca25aab","./buildModuleUrl-8958744c","./RuntimeError-4a5c8994"],(function(e,t,n,a,r,i,s,o,u,l){"use strict";var d=Object.freeze({NONE:0,TRIANGLES:1,LINES:2,POLYLINES:3});function c(e,t,n,a){this[0]=r.defaultValue(e,0),this[1]=r.defaultValue(n,0),this[2]=r.defaultValue(t,0),this[3]=r.defaultValue(a,0)}c.packedLength=4,c.pack=function(e,t,n){return n=r.defaultValue(n,0),t[n++]=e[0],t[n++]=e[1],t[n++]=e[2],t[n++]=e[3],t},c.unpack=function(e,t,n){return t=r.defaultValue(t,0),r.defined(n)||(n=new c),n[0]=e[t++],n[1]=e[t++],n[2]=e[t++],n[3]=e[t++],n},c.clone=function(e,t){if(r.defined(e))return r.defined(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t):new c(e[0],e[2],e[1],e[3])},c.fromArray=function(e,t,n){return t=r.defaultValue(t,0),r.defined(n)||(n=new c),n[0]=e[t],n[1]=e[t+1],n[2]=e[t+2],n[3]=e[t+3],n},c.fromColumnMajorArray=function(e,t){return c.clone(e,t)},c.fromRowMajorArray=function(e,t){return r.defined(t)?(t[0]=e[0],t[1]=e[2],t[2]=e[1],t[3]=e[3],t):new c(e[0],e[1],e[2],e[3])},c.fromScale=function(e,t){return r.defined(t)?(t[0]=e.x,t[1]=0,t[2]=0,t[3]=e.y,t):new c(e.x,0,0,e.y)},c.fromUniformScale=function(e,t){return r.defined(t)?(t[0]=e,t[1]=0,t[2]=0,t[3]=e,t):new c(e,0,0,e)},c.fromRotation=function(e,t){var n=Math.cos(e),a=Math.sin(e);return r.defined(t)?(t[0]=n,t[1]=a,t[2]=-a,t[3]=n,t):new c(n,-a,a,n)},c.toArray=function(e,t){return r.defined(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t):[e[0],e[1],e[2],e[3]]},c.getElementIndex=function(e,t){return 2*e+t},c.getColumn=function(e,t,n){var a=2*t,r=e[a],i=e[a+1];return n.x=r,n.y=i,n},c.setColumn=function(e,t,n,a){var r=2*t;return(a=c.clone(e,a))[r]=n.x,a[r+1]=n.y,a},c.getRow=function(e,t,n){var a=e[t],r=e[t+2];return n.x=a,n.y=r,n},c.setRow=function(e,t,n,a){return(a=c.clone(e,a))[t]=n.x,a[t+2]=n.y,a};var f=new t.Cartesian2;c.getScale=function(e,n){return n.x=t.Cartesian2.magnitude(t.Cartesian2.fromElements(e[0],e[1],f)),n.y=t.Cartesian2.magnitude(t.Cartesian2.fromElements(e[2],e[3],f)),n};var h=new t.Cartesian2;function m(e,t,n,a){this.x=r.defaultValue(e,0),this.y=r.defaultValue(t,0),this.z=r.defaultValue(n,0),this.w=r.defaultValue(a,0)}c.getMaximumScale=function(e){return c.getScale(e,h),t.Cartesian2.maximumComponent(h)},c.multiply=function(e,t,n){var a=e[0]*t[0]+e[2]*t[1],r=e[0]*t[2]+e[2]*t[3],i=e[1]*t[0]+e[3]*t[1],s=e[1]*t[2]+e[3]*t[3];return n[0]=a,n[1]=i,n[2]=r,n[3]=s,n},c.add=function(e,t,n){return n[0]=e[0]+t[0],n[1]=e[1]+t[1],n[2]=e[2]+t[2],n[3]=e[3]+t[3],n},c.subtract=function(e,t,n){return n[0]=e[0]-t[0],n[1]=e[1]-t[1],n[2]=e[2]-t[2],n[3]=e[3]-t[3],n},c.multiplyByVector=function(e,t,n){var a=e[0]*t.x+e[2]*t.y,r=e[1]*t.x+e[3]*t.y;return n.x=a,n.y=r,n},c.multiplyByScalar=function(e,t,n){return n[0]=e[0]*t,n[1]=e[1]*t,n[2]=e[2]*t,n[3]=e[3]*t,n},c.multiplyByScale=function(e,t,n){return n[0]=e[0]*t.x,n[1]=e[1]*t.x,n[2]=e[2]*t.y,n[3]=e[3]*t.y,n},c.negate=function(e,t){return t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t[3]=-e[3],t},c.transpose=function(e,t){var n=e[0],a=e[2],r=e[1],i=e[3];return t[0]=n,t[1]=a,t[2]=r,t[3]=i,t},c.abs=function(e,t){return t[0]=Math.abs(e[0]),t[1]=Math.abs(e[1]),t[2]=Math.abs(e[2]),t[3]=Math.abs(e[3]),t},c.equals=function(e,t){return e===t||r.defined(e)&&r.defined(t)&&e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]&&e[3]===t[3]},c.equalsArray=function(e,t,n){return e[0]===t[n]&&e[1]===t[n+1]&&e[2]===t[n+2]&&e[3]===t[n+3]},c.equalsEpsilon=function(e,t,n){return e===t||r.defined(e)&&r.defined(t)&&Math.abs(e[0]-t[0])<=n&&Math.abs(e[1]-t[1])<=n&&Math.abs(e[2]-t[2])<=n&&Math.abs(e[3]-t[3])<=n},c.IDENTITY=Object.freeze(new c(1,0,0,1)),c.ZERO=Object.freeze(new c(0,0,0,0)),c.COLUMN0ROW0=0,c.COLUMN0ROW1=1,c.COLUMN1ROW0=2,c.COLUMN1ROW1=3,Object.defineProperties(c.prototype,{length:{get:function(){return c.packedLength}}}),c.prototype.clone=function(e){return c.clone(this,e)},c.prototype.equals=function(e){return c.equals(this,e)},c.prototype.equalsEpsilon=function(e,t){return c.equalsEpsilon(this,e,t)},c.prototype.toString=function(){return"("+this[0]+", "+this[2]+")\n("+this[1]+", "+this[3]+")"};var p=new n.Cartesian3;m.fromAxisAngle=function(e,t,a){var i=t/2,s=Math.sin(i),o=(p=n.Cartesian3.normalize(e,p)).x*s,u=p.y*s,l=p.z*s,d=Math.cos(i);return r.defined(a)?(a.x=o,a.y=u,a.z=l,a.w=d,a):new m(o,u,l,d)};var y=[1,2,0],w=new Array(3);m.fromRotationMatrix=function(e,t){var n,a,s,o,u,l=e[i.Matrix3.COLUMN0ROW0],d=e[i.Matrix3.COLUMN1ROW1],c=e[i.Matrix3.COLUMN2ROW2],f=l+d+c;if(f>0)u=.5*(n=Math.sqrt(f+1)),n=.5/n,a=(e[i.Matrix3.COLUMN1ROW2]-e[i.Matrix3.COLUMN2ROW1])*n,s=(e[i.Matrix3.COLUMN2ROW0]-e[i.Matrix3.COLUMN0ROW2])*n,o=(e[i.Matrix3.COLUMN0ROW1]-e[i.Matrix3.COLUMN1ROW0])*n;else{var h=0;d>l&&(h=1),c>l&&c>d&&(h=2);var p=y[h],C=y[p];n=Math.sqrt(e[i.Matrix3.getElementIndex(h,h)]-e[i.Matrix3.getElementIndex(p,p)]-e[i.Matrix3.getElementIndex(C,C)]+1);var x=w;x[h]=.5*n,n=.5/n,u=(e[i.Matrix3.getElementIndex(C,p)]-e[i.Matrix3.getElementIndex(p,C)])*n,x[p]=(e[i.Matrix3.getElementIndex(p,h)]+e[i.Matrix3.getElementIndex(h,p)])*n,x[C]=(e[i.Matrix3.getElementIndex(C,h)]+e[i.Matrix3.getElementIndex(h,C)])*n,a=-x[0],s=-x[1],o=-x[2]}return r.defined(t)?(t.x=a,t.y=s,t.z=o,t.w=u,t):new m(a,s,o,u)};var C=new m,x=new m,M=new m,_=new m;m.fromHeadingPitchRoll=function(e,t){return _=m.fromAxisAngle(n.Cartesian3.UNIT_X,e.roll,C),M=m.fromAxisAngle(n.Cartesian3.UNIT_Y,-e.pitch,t),t=m.multiply(M,_,M),x=m.fromAxisAngle(n.Cartesian3.UNIT_Z,-e.heading,C),m.multiply(x,t,t)};var E=new n.Cartesian3,O=new n.Cartesian3,v=new m,S=new m,T=new m;m.packedLength=4,m.pack=function(e,t,n){return n=r.defaultValue(n,0),t[n++]=e.x,t[n++]=e.y,t[n++]=e.z,t[n]=e.w,t},m.unpack=function(e,t,n){return t=r.defaultValue(t,0),r.defined(n)||(n=new m),n.x=e[t],n.y=e[t+1],n.z=e[t+2],n.w=e[t+3],n},m.packedInterpolationLength=3,m.convertPackedArrayForInterpolation=function(e,t,n,a){m.unpack(e,4*n,T),m.conjugate(T,T);for(var r=0,i=n-t+1;r<i;r++){var s=3*r;m.unpack(e,4*(t+r),v),m.multiply(v,T,v),v.w<0&&m.negate(v,v),m.computeAxis(v,E);var o=m.computeAngle(v);a[s]=E.x*o,a[s+1]=E.y*o,a[s+2]=E.z*o}},m.unpackInterpolationResult=function(e,t,a,i,s){r.defined(s)||(s=new m),n.Cartesian3.fromArray(e,0,O);var o=n.Cartesian3.magnitude(O);return m.unpack(t,4*i,S),0===o?m.clone(m.IDENTITY,v):m.fromAxisAngle(O,o,v),m.multiply(v,S,s)},m.clone=function(e,t){if(r.defined(e))return r.defined(t)?(t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t):new m(e.x,e.y,e.z,e.w)},m.conjugate=function(e,t){return t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=e.w,t},m.magnitudeSquared=function(e){return e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w},m.magnitude=function(e){return Math.sqrt(m.magnitudeSquared(e))},m.normalize=function(e,t){var n=1/m.magnitude(e),a=e.x*n,r=e.y*n,i=e.z*n,s=e.w*n;return t.x=a,t.y=r,t.z=i,t.w=s,t},m.inverse=function(e,t){var n=m.magnitudeSquared(e);return t=m.conjugate(e,t),m.multiplyByScalar(t,1/n,t)},m.add=function(e,t,n){return n.x=e.x+t.x,n.y=e.y+t.y,n.z=e.z+t.z,n.w=e.w+t.w,n},m.subtract=function(e,t,n){return n.x=e.x-t.x,n.y=e.y-t.y,n.z=e.z-t.z,n.w=e.w-t.w,n},m.negate=function(e,t){return t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=-e.w,t},m.dot=function(e,t){return e.x*t.x+e.y*t.y+e.z*t.z+e.w*t.w},m.multiply=function(e,t,n){var a=e.x,r=e.y,i=e.z,s=e.w,o=t.x,u=t.y,l=t.z,d=t.w,c=s*o+a*d+r*l-i*u,f=s*u-a*l+r*d+i*o,h=s*l+a*u-r*o+i*d,m=s*d-a*o-r*u-i*l;return n.x=c,n.y=f,n.z=h,n.w=m,n},m.multiplyByVec=function(e,t,a){var r=new n.Cartesian3,i=new n.Cartesian3,s=new n.Cartesian3(e.x,e.y,e.z);r=n.Cartesian3.cross(s,t,r),i=n.Cartesian3.cross(s,r,i);var o=new n.Cartesian3;o=n.Cartesian3.multiplyByScalar(r,2*e.w,o);var u=new n.Cartesian3;return u=n.Cartesian3.multiplyByScalar(r,2,u),a=n.Cartesian3.add(t,o,a),a=n.Cartesian3.add(a,u,a)},m.multiplyByScalar=function(e,t,n){return n.x=e.x*t,n.y=e.y*t,n.z=e.z*t,n.w=e.w*t,n},m.divideByScalar=function(e,t,n){return n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n.w=e.w/t,n},m.computeAxis=function(e,t){var n=e.w;if(Math.abs(n-1)<s.CesiumMath.EPSILON6)return t.x=t.y=t.z=0,t;var a=1/Math.sqrt(1-n*n);return t.x=e.x*a,t.y=e.y*a,t.z=e.z*a,t},m.computeAngle=function(e){return Math.abs(e.w-1)<s.CesiumMath.EPSILON6?0:2*Math.acos(e.w)};var g=new m;m.lerp=function(e,t,n,a){return g=m.multiplyByScalar(t,n,g),a=m.multiplyByScalar(e,1-n,a),m.add(g,a,a)};var D=new m,P=new m,N=new m;m.slerp=function(e,t,n,a){var r=m.dot(e,t),i=t;if(r<0&&(r=-r,i=D=m.negate(t,D)),1-r<s.CesiumMath.EPSILON6)return m.lerp(e,i,n,a);var o=Math.acos(r);return P=m.multiplyByScalar(e,Math.sin((1-n)*o),P),N=m.multiplyByScalar(i,Math.sin(n*o),N),a=m.add(P,N,a),m.multiplyByScalar(a,1/Math.sin(o),a)},m.log=function(e,t){var a=s.CesiumMath.acosClamped(e.w),r=0;return 0!==a&&(r=a/Math.sin(a)),n.Cartesian3.multiplyByScalar(e,r,t)},m.exp=function(e,t){var a=n.Cartesian3.magnitude(e),r=0;return 0!==a&&(r=Math.sin(a)/a),t.x=e.x*r,t.y=e.y*r,t.z=e.z*r,t.w=Math.cos(a),t};var I=new n.Cartesian3,R=new n.Cartesian3,A=new m,U=new m;m.computeInnerQuadrangle=function(e,t,a,r){var i=m.conjugate(t,A);m.multiply(i,a,U);var s=m.log(U,I);m.multiply(i,e,U);var o=m.log(U,R);return n.Cartesian3.add(s,o,s),n.Cartesian3.multiplyByScalar(s,.25,s),n.Cartesian3.negate(s,s),m.exp(s,A),m.multiply(t,A,r)},m.squad=function(e,t,n,a,r,i){var s=m.slerp(e,t,r,A),o=m.slerp(n,a,r,U);return m.slerp(s,o,2*r*(1-r),i)};for(var b=new m,z=1.9011074535173003,F=i.FeatureDetection.supportsTypedArrays()?new Float32Array(8):[],V=i.FeatureDetection.supportsTypedArrays()?new Float32Array(8):[],W=i.FeatureDetection.supportsTypedArrays()?new Float32Array(8):[],q=i.FeatureDetection.supportsTypedArrays()?new Float32Array(8):[],L=0;L<7;++L){var k=L+1,Y=2*k+1;F[L]=1/(k*Y),V[L]=k/Y}function B(e,t,n){for(var a,r,i=0,s=e.length-1;i<=s;)if((r=n(e[a=~~((i+s)/2)],t))<0)i=a+1;else{if(!(r>0))return a;s=a-1}return~(s+1)}function j(e,t,n,a,r){this.xPoleWander=e,this.yPoleWander=t,this.xPoleOffset=n,this.yPoleOffset=a,this.ut1MinusUtc=r}
/**
    @license
    sprintf.js from the php.js project - https://github.com/kvz/phpjs
    Directly from https://github.com/kvz/phpjs/blob/master/functions/strings/sprintf.js

    php.js is copyright 2012 Kevin van Zonneveld.

    Portions copyright Brett Zamir (http://brett-zamir.me), Kevin van Zonneveld
    (http://kevin.vanzonneveld.net), Onno Marsman, Theriault, Michael White
    (http://getsprink.com), Waldo Malqui Silva, Paulo Freitas, Jack, Jonas
    Raoni Soares Silva (http://www.jsfromhell.com), Philip Peterson, Legaev
    Andrey, Ates Goral (http://magnetiq.com), Alex, Ratheous, Martijn Wieringa,
    Rafa? Kukawski (http://blog.kukawski.pl), lmeyrick
    (https://sourceforge.net/projects/bcmath-js/), Nate, Philippe Baumann,
    Enrique Gonzalez, Webtoolkit.info (http://www.webtoolkit.info/), Carlos R.
    L. Rodrigues (http://www.jsfromhell.com), Ash Searle
    (http://hexmen.com/blog/), Jani Hartikainen, travc, Ole Vrijenhoek,
    Erkekjetter, Michael Grier, Rafa? Kukawski (http://kukawski.pl), Johnny
    Mast (http://www.phpvrouwen.nl), T.Wild, d3x,
    http://stackoverflow.com/questions/57803/how-to-convert-decimal-to-hex-in-javascript,
    Rafa? Kukawski (http://blog.kukawski.pl/), stag019, pilus, WebDevHobo
    (http://webdevhobo.blogspot.com/), marrtins, GeekFG
    (http://geekfg.blogspot.com), Andrea Giammarchi
    (http://webreflection.blogspot.com), Arpad Ray (mailto:<EMAIL>),
    gorthaur, Paul Smith, Tim de Koning (http://www.kingsquare.nl), Joris, Oleg
    Eremeev, Steve Hilder, majak, gettimeofday, KELAN, Josh Fraser
    (http://onlineaspect.com/2007/06/08/auto-detect-a-time-zone-with-javascript/),
    Marc Palau, Martin
    (http://www.erlenwiese.de/), Breaking Par Consulting Inc
    (http://www.breakingpar.com/bkp/home.nsf/0/87256B280015193F87256CFB006C45F7),
    Chris, Mirek Slugen, saulius, Alfonso Jimenez
    (http://www.alfonsojimenez.com), Diplom@t (http://difane.com/), felix,
    Mailfaker (http://www.weedem.fr/), Tyler Akins (http://rumkin.com), Caio
    Ariede (http://caioariede.com), Robin, Kankrelune
    (http://www.webfaktory.info/), Karol Kowalski, Imgen Tata
    (http://www.myipdf.com/), mdsjack (http://www.mdsjack.bo.it), Dreamer,
    Felix Geisendoerfer (http://www.debuggable.com/felix), Lars Fischer, AJ,
    David, Aman Gupta, Michael White, Public Domain
    (http://www.json.org/json2.js), Steven Levithan
    (http://blog.stevenlevithan.com), Sakimori, Pellentesque Malesuada,
    Thunder.m, Dj (http://phpjs.org/functions/htmlentities:425#comment_134018),
    Steve Clay, David James, Francois, class_exists, nobbler, T. Wild, Itsacon
    (http://www.itsacon.net/), date, Ole Vrijenhoek (http://www.nervous.nl/),
    Fox, Raphael (Ao RUDLER), Marco, noname, Mateusz "loonquawl" Zalega, Frank
    Forte, Arno, ger, mktime, john (http://www.jd-tech.net), Nick Kolosov
    (http://sammy.ru), marc andreu, Scott Cariss, Douglas Crockford
    (http://javascript.crockford.com), madipta, Slawomir Kaniecki,
    ReverseSyntax, Nathan, Alex Wilson, kenneth, Bayron Guevara, Adam Wallner
    (http://web2.bitbaro.hu/), paulo kuong, jmweb, Lincoln Ramsay, djmix,
    Pyerre, Jon Hohle, Thiago Mata (http://thiagomata.blog.com), lmeyrick
    (https://sourceforge.net/projects/bcmath-js/this.), Linuxworld, duncan,
    Gilbert, Sanjoy Roy, Shingo, sankai, Oskar Larsson H?gfeldt
    (http://oskar-lh.name/), Denny Wardhana, 0m3r, Everlasto, Subhasis Deb,
    josh, jd, Pier Paolo Ramon (http://www.mastersoup.com/), P, merabi, Soren
    Hansen, Eugene Bulkin (http://doubleaw.com/), Der Simon
    (http://innerdom.sourceforge.net/), echo is bad, Ozh, XoraX
    (http://www.xorax.info), EdorFaus, JB, J A R, Marc Jansen, Francesco, LH,
    Stoyan Kyosev (http://www.svest.org/), nord_ua, omid
    (http://phpjs.org/functions/380:380#comment_137122), Brad Touesnard, MeEtc
    (http://yass.meetcweb.com), Peter-Paul Koch
    (http://www.quirksmode.org/js/beat.html), Olivier Louvignes
    (http://mg-crea.com/), T0bsn, Tim Wiel, Bryan Elliott, Jalal Berrami,
    Martin, JT, David Randall, Thomas Beaucourt (http://www.webapp.fr), taith,
    vlado houba, Pierre-Luc Paour, Kristof Coomans (SCK-CEN Belgian Nucleair
    Research Centre), Martin Pool, Kirk Strobeck, Rick Waldron, Brant Messenger
    (http://www.brantmessenger.com/), Devan Penner-Woelk, Saulo Vallory, Wagner
    B. Soares, Artur Tchernychev, Valentina De Rosa, Jason Wong
    (http://carrot.org/), Christoph, Daniel Esteban, strftime, Mick@el, rezna,
    Simon Willison (http://simonwillison.net), Anton Ongson, Gabriel Paderni,
    Marco van Oort, penutbutterjelly, Philipp Lenssen, Bjorn Roesbeke
    (http://www.bjornroesbeke.be/), Bug?, Eric Nagel, Tomasz Wesolowski,
    Evertjan Garretsen, Bobby Drake, Blues (http://tech.bluesmoon.info/), Luke
    Godfrey, Pul, uestla, Alan C, Ulrich, Rafal Kukawski, Yves Sucaet,
    sowberry, Norman "zEh" Fuchs, hitwork, Zahlii, johnrembo, Nick Callen,
    Steven Levithan (stevenlevithan.com), ejsanders, Scott Baker, Brian Tafoya
    (http://www.premasolutions.com/), Philippe Jausions
    (http://pear.php.net/user/jausions), Aidan Lister
    (http://aidanlister.com/), Rob, e-mike, HKM, ChaosNo1, metjay, strcasecmp,
    strcmp, Taras Bogach, jpfle, Alexander Ermolaev
    (http://snippets.dzone.com/user/AlexanderErmolaev), DxGx, kilops, Orlando,
    dptr1988, Le Torbi, James (http://www.james-bell.co.uk/), Pedro Tainha
    (http://www.pedrotainha.com), James, Arnout Kazemier
    (http://www.3rd-Eden.com), Chris McMacken, gabriel paderni, Yannoo,
    FGFEmperor, baris ozdil, Tod Gentille, Greg Frazier, jakes, 3D-GRAF, Allan
    Jensen (http://www.winternet.no), Howard Yeend, Benjamin Lupton, davook,
    daniel airton wermann (http://wermann.com.br), Atli T¨®r, Maximusya, Ryan
    W Tenney (http://ryan.10e.us), Alexander M Beedie, fearphage
    (http://http/my.opera.com/fearphage/), Nathan Sepulveda, Victor, Matteo,
    Billy, stensi, Cord, Manish, T.J. Leahy, Riddler
    (http://www.frontierwebdev.com/), Rafa? Kukawski, FremyCompany, Matt
    Bradley, Tim de Koning, Luis Salazar (http://www.freaky-media.com/), Diogo
    Resende, Rival, Andrej Pavlovic, Garagoth, Le Torbi
    (http://www.letorbi.de/), Dino, Josep Sanz (http://www.ws3.es/), rem,
    Russell Walker (http://www.nbill.co.uk/), Jamie Beck
    (http://www.terabit.ca/), setcookie, Michael, YUI Library:
    http://developer.yahoo.com/yui/docs/YAHOO.util.DateLocale.html, Blues at
    http://hacks.bluesmoon.info/strftime/strftime.js, Ben
    (http://benblume.co.uk/), DtTvB
    (http://dt.in.th/2008-09-16.string-length-in-bytes.html), Andreas, William,
    meo, incidence, Cagri Ekin, Amirouche, Amir Habibi
    (http://www.residence-mixte.com/), Luke Smith (http://lucassmith.name),
    Kheang Hok Chin (http://www.distantia.ca/), Jay Klehr, Lorenzo Pisani,
    Tony, Yen-Wei Liu, Greenseed, mk.keck, Leslie Hoare, dude, booeyOH, Ben
    Bryan

    Licensed under the MIT (MIT-LICENSE.txt) license.

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the
    "Software"), to deal in the Software without restriction, including
    without limitation the rights to use, copy, modify, merge, publish,
    distribute, sublicense, and/or sell copies of the Software, and to
    permit persons to whom the Software is furnished to do so, subject to
    the following conditions:

    The above copyright notice and this permission notice shall be included
    in all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
    OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
    IN NO EVENT SHALL KEVIN VAN ZONNEVELD BE LIABLE FOR ANY CLAIM, DAMAGES
    OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
    ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
    OTHER DEALINGS IN THE SOFTWARE.
    */function G(){var e=/%%|%(\d+\$)?([-+\'#0 ]*)(\*\d+\$|\*|\d+)?(\.(\*\d+\$|\*|\d+))?([scboxXuideEfFgG])/g,t=arguments,n=0,a=t[n++],r=function(e,t,n,a){n||(n=" ");var r=e.length>=t?"":Array(1+t-e.length>>>0).join(n);return a?e+r:r+e},i=function(e,t,n,a,i,s){var o=a-e.length;return o>0&&(e=n||!i?r(e,a,s,n):e.slice(0,t.length)+r("",o,"0",!0)+e.slice(t.length)),e},s=function(e,t,n,a,s,o,u){var l=e>>>0;return e=(n=n&&l&&{2:"0b",8:"0",16:"0x"}[t]||"")+r(l.toString(t),o||0,"0",!1),i(e,n,a,s,u)},o=function(e,t,n,a,r,s){return null!=a&&(e=e.slice(0,a)),i(e,"",t,n,r,s)},u=function(e,a,u,l,d,c,f){var h,m,p,y,w;if("%%"==e)return"%";for(var C=!1,x="",M=!1,_=!1,E=" ",O=u.length,v=0;u&&v<O;v++)switch(u.charAt(v)){case" ":x=" ";break;case"+":x="+";break;case"-":C=!0;break;case"'":E=u.charAt(v+1);break;case"0":M=!0;break;case"#":_=!0}if((l=l?"*"==l?+t[n++]:"*"==l.charAt(0)?+t[l.slice(1,-1)]:+l:0)<0&&(l=-l,C=!0),!isFinite(l))throw new Error("sprintf: (minimum-)width must be finite");switch(c=c?"*"==c?+t[n++]:"*"==c.charAt(0)?+t[c.slice(1,-1)]:+c:"fFeE".indexOf(f)>-1?6:"d"==f?0:void 0,w=a?t[a.slice(0,-1)]:t[n++],f){case"s":return o(String(w),C,l,c,M,E);case"c":return o(String.fromCharCode(+w),C,l,c,M);case"b":return s(w,2,_,C,l,c,M);case"o":return s(w,8,_,C,l,c,M);case"x":return s(w,16,_,C,l,c,M);case"X":return s(w,16,_,C,l,c,M).toUpperCase();case"u":return s(w,10,_,C,l,c,M);case"i":case"d":return h=+w||0,w=(m=(h=Math.round(h-h%1))<0?"-":x)+r(String(Math.abs(h)),c,"0",!1),i(w,m,C,l,M);case"e":case"E":case"f":case"F":case"g":case"G":return m=(h=+w)<0?"-":x,p=["toExponential","toFixed","toPrecision"]["efg".indexOf(f.toLowerCase())],y=["toString","toUpperCase"]["eEfFgG".indexOf(f)%2],w=m+Math.abs(h)[p](c),i(w,m,C,l,M)[y]();default:return e}};return a.replace(e,u)}function Z(e,t,n,a,r,i,s,o){this.year=e,this.month=t,this.day=n,this.hour=a,this.minute=r,this.second=i,this.millisecond=s,this.isLeapSecond=o}function X(e){return e%4==0&&e%100!=0||e%400==0}function J(e,t){this.julianDate=e,this.offset=t}F[7]=z/136,V[7]=8*z/17,m.fastSlerp=function(e,t,n,a){var r,i=m.dot(e,t);i>=0?r=1:(r=-1,i=-i);for(var s=i-1,o=1-n,u=n*n,l=o*o,d=7;d>=0;--d)W[d]=(F[d]*u-V[d])*s,q[d]=(F[d]*l-V[d])*s;var c=r*n*(1+W[0]*(1+W[1]*(1+W[2]*(1+W[3]*(1+W[4]*(1+W[5]*(1+W[6]*(1+W[7])))))))),f=o*(1+q[0]*(1+q[1]*(1+q[2]*(1+q[3]*(1+q[4]*(1+q[5]*(1+q[6]*(1+q[7])))))))),h=m.multiplyByScalar(e,f,b);return m.multiplyByScalar(t,c,a),m.add(h,a,a)},m.fastSquad=function(e,t,n,a,r,i){var s=m.fastSlerp(e,t,r,A),o=m.fastSlerp(n,a,r,U);return m.fastSlerp(s,o,2*r*(1-r),i)},m.equals=function(e,t){return e===t||r.defined(e)&&r.defined(t)&&e.x===t.x&&e.y===t.y&&e.z===t.z&&e.w===t.w},m.equalsEpsilon=function(e,t,n){return e===t||r.defined(e)&&r.defined(t)&&Math.abs(e.x-t.x)<=n&&Math.abs(e.y-t.y)<=n&&Math.abs(e.z-t.z)<=n&&Math.abs(e.w-t.w)<=n},m.ZERO=Object.freeze(new m(0,0,0,0)),m.IDENTITY=Object.freeze(new m(0,0,0,1)),m.prototype.clone=function(e){return m.clone(this,e)},m.prototype.equals=function(e){return m.equals(this,e)},m.prototype.equalsEpsilon=function(e,t){return m.equalsEpsilon(this,e,t)},m.prototype.toString=function(){return"("+this.x+", "+this.y+", "+this.z+", "+this.w+")"};var H=Object.freeze({SECONDS_PER_MILLISECOND:.001,SECONDS_PER_MINUTE:60,MINUTES_PER_HOUR:60,HOURS_PER_DAY:24,SECONDS_PER_HOUR:3600,MINUTES_PER_DAY:1440,SECONDS_PER_DAY:86400,DAYS_PER_JULIAN_CENTURY:36525,PICOSECOND:1e-9,MODIFIED_JULIAN_DATE_DIFFERENCE:2400000.5}),$=Object.freeze({UTC:0,TAI:1}),Q=new Z,K=[31,28,31,30,31,30,31,31,30,31,30,31];function ee(e,t){return pe.compare(e.julianDate,t.julianDate)}var te=new J;function ne(e){te.julianDate=e;var t=pe.leapSeconds,n=B(t,te,ee);n<0&&(n=~n),n>=t.length&&(n=t.length-1);var a=t[n].offset;n>0&&(pe.secondsDifference(t[n].julianDate,e)>a&&(a=t[--n].offset));pe.addSeconds(e,a,e)}function ae(e,t){te.julianDate=e;var n=pe.leapSeconds,a=B(n,te,ee);if(a<0&&(a=~a),0===a)return pe.addSeconds(e,-n[0].offset,t);if(a>=n.length)return pe.addSeconds(e,-n[a-1].offset,t);var r=pe.secondsDifference(n[a].julianDate,e);return 0===r?pe.addSeconds(e,-n[a].offset,t):r<=1?void 0:pe.addSeconds(e,-n[--a].offset,t)}function re(e,t,n){var a=t/H.SECONDS_PER_DAY|0;return e+=a,(t-=H.SECONDS_PER_DAY*a)<0&&(e--,t+=H.SECONDS_PER_DAY),n.dayNumber=e,n.secondsOfDay=t,n}function ie(e,t,n,a,r,i,s){var o=(t-14)/12|0,u=e+4800+o,l=(1461*u/4|0)+(367*(t-2-12*o)/12|0)-(3*((u+100)/100|0)/4|0)+n-32075;(a-=12)<0&&(a+=24);var d=i+(a*H.SECONDS_PER_HOUR+r*H.SECONDS_PER_MINUTE+s*H.SECONDS_PER_MILLISECOND);return d>=43200&&(l-=1),[l,d]}var se=/^(\d{4})$/,oe=/^(\d{4})-(\d{2})$/,ue=/^(\d{4})-?(\d{3})$/,le=/^(\d{4})-?W(\d{2})-?(\d{1})?$/,de=/^(\d{4})-?(\d{2})-?(\d{2})$/,ce=/([Z+\-])?(\d{2})?:?(\d{2})?$/,fe=/^(\d{2})(\.\d+)?/.source+ce.source,he=/^(\d{2}):?(\d{2})(\.\d+)?/.source+ce.source,me=/^(\d{2}):?(\d{2}):?(\d{2})(\.\d+)?/.source+ce.source;function pe(e,t,n){this.dayNumber=void 0,this.secondsOfDay=void 0,e=r.defaultValue(e,0),t=r.defaultValue(t,0),n=r.defaultValue(n,$.UTC);var a=0|e;re(a,t+=(e-a)*H.SECONDS_PER_DAY,this),n===$.UTC&&ne(this)}pe.fromGregorianDate=function(e,t){var n=ie(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond);return r.defined(t)?(re(n[0],n[1],t),ne(t),t):new pe(n[0],n[1],$.UTC)},pe.fromDate=function(e,t){var n=ie(e.getUTCFullYear(),e.getUTCMonth()+1,e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds());return r.defined(t)?(re(n[0],n[1],t),ne(t),t):new pe(n[0],n[1],$.UTC)},pe.fromIso8601=function(e,t){var n,a,i,s,o=(e=e.replace(",",".")).split("T"),u=1,l=1,d=0,c=0,f=0,h=0,m=o[0],p=o[1];if(null!==(o=m.match(de)))n=+o[1],u=+o[2],l=+o[3];else if(null!==(o=m.match(oe)))n=+o[1],u=+o[2];else if(null!==(o=m.match(se)))n=+o[1];else{var y;if(null!==(o=m.match(ue)))n=+o[1],y=+o[2],i=X(n);else if(null!==(o=m.match(le)))n=+o[1],y=7*+o[2]+(+o[3]||0)-new Date(Date.UTC(n,0,4)).getUTCDay()-3;(a=new Date(Date.UTC(n,0,1))).setUTCDate(y),u=a.getUTCMonth()+1,l=a.getUTCDate()}if(i=X(n),r.defined(p)){null!==(o=p.match(me))?(d=+o[1],c=+o[2],f=+o[3],h=1e3*+(o[4]||0),s=5):null!==(o=p.match(he))?(d=+o[1],c=+o[2],f=60*+(o[3]||0),s=4):null!==(o=p.match(fe))&&(d=+o[1],c=60*+(o[2]||0),s=3);var w=o[s],C=+o[s+1],x=+(o[s+2]||0);switch(w){case"+":d-=C,c-=x;break;case"-":d+=C,c+=x;break;case"Z":break;default:c+=new Date(Date.UTC(n,u-1,l,d,c)).getTimezoneOffset()}}var M=60===f;for(M&&f--;c>=60;)c-=60,d++;for(;d>=24;)d-=24,l++;for(a=i&&2===u?29:K[u-1];l>a;)l-=a,++u>12&&(u-=12,n++),a=i&&2===u?29:K[u-1];for(;c<0;)c+=60,d--;for(;d<0;)d+=24,l--;for(;l<1;)--u<1&&(u+=12,n--),l+=a=i&&2===u?29:K[u-1];var _=ie(n,u,l,d,c,f,h);return r.defined(t)?(re(_[0],_[1],t),ne(t)):t=new pe(_[0],_[1],$.UTC),M&&pe.addSeconds(t,1,t),t},pe.now=function(e){return pe.fromDate(new Date,e)};var ye=new pe(0,0,$.TAI);function we(e){if(e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT),this._dates=void 0,this._samples=void 0,this._dateColumn=-1,this._xPoleWanderRadiansColumn=-1,this._yPoleWanderRadiansColumn=-1,this._ut1MinusUtcSecondsColumn=-1,this._xCelestialPoleOffsetRadiansColumn=-1,this._yCelestialPoleOffsetRadiansColumn=-1,this._taiMinusUtcSecondsColumn=-1,this._columnCount=0,this._lastIndex=-1,this._downloadPromise=void 0,this._dataError=void 0,this._addNewLeapSeconds=r.defaultValue(e.addNewLeapSeconds,!0),r.defined(e.data))xe(this,e.data);else if(r.defined(e.url)){var t=u.Resource.createIfNeeded(e.url),n=this;this._downloadPromise=r.when(t.fetchJson(),(function(e){xe(n,e)}),(function(){n._dataError="An error occurred while retrieving the EOP data from the URL "+t.url+"."}))}else xe(this,{columnNames:["dateIso8601","modifiedJulianDateUtc","xPoleWanderRadians","yPoleWanderRadians","ut1MinusUtcSeconds","lengthOfDayCorrectionSeconds","xCelestialPoleOffsetRadians","yCelestialPoleOffsetRadians","taiMinusUtcSeconds"],samples:[]})}function Ce(e,t){return pe.compare(e.julianDate,t)}function xe(e,t){if(r.defined(t.columnNames))if(r.defined(t.samples)){var n=t.columnNames.indexOf("modifiedJulianDateUtc"),a=t.columnNames.indexOf("xPoleWanderRadians"),i=t.columnNames.indexOf("yPoleWanderRadians"),s=t.columnNames.indexOf("ut1MinusUtcSeconds"),o=t.columnNames.indexOf("xCelestialPoleOffsetRadians"),u=t.columnNames.indexOf("yCelestialPoleOffsetRadians"),l=t.columnNames.indexOf("taiMinusUtcSeconds");if(n<0||a<0||i<0||s<0||o<0||u<0||l<0)e._dataError="Error in loaded EOP data: The columnNames property must include modifiedJulianDateUtc, xPoleWanderRadians, yPoleWanderRadians, ut1MinusUtcSeconds, xCelestialPoleOffsetRadians, yCelestialPoleOffsetRadians, and taiMinusUtcSeconds columns";else{var d,c=e._samples=t.samples,f=e._dates=[];e._dateColumn=n,e._xPoleWanderRadiansColumn=a,e._yPoleWanderRadiansColumn=i,e._ut1MinusUtcSecondsColumn=s,e._xCelestialPoleOffsetRadiansColumn=o,e._yCelestialPoleOffsetRadiansColumn=u,e._taiMinusUtcSecondsColumn=l,e._columnCount=t.columnNames.length,e._lastIndex=void 0;for(var h=e._addNewLeapSeconds,m=0,p=c.length;m<p;m+=e._columnCount){var y=c[m+n],w=c[m+l],C=new pe(y+H.MODIFIED_JULIAN_DATE_DIFFERENCE,w,$.TAI);if(f.push(C),h){if(w!==d&&r.defined(d)){var x=pe.leapSeconds,M=B(x,C,Ce);if(M<0){var _=new J(C,w);x.splice(~M,0,_)}}d=w}}}}else e._dataError="Error in loaded EOP data: The samples property is required.";else e._dataError="Error in loaded EOP data: The columnNames property is required."}function Me(e,t,n,a,r){var i=n*a;r.xPoleWander=t[i+e._xPoleWanderRadiansColumn],r.yPoleWander=t[i+e._yPoleWanderRadiansColumn],r.xPoleOffset=t[i+e._xCelestialPoleOffsetRadiansColumn],r.yPoleOffset=t[i+e._yCelestialPoleOffsetRadiansColumn],r.ut1MinusUtc=t[i+e._ut1MinusUtcSecondsColumn]}function _e(e,t,n){return t+e*(n-t)}function Ee(e,t,n,a,r,i,s){var o=e._columnCount;if(i>t.length-1)return s.xPoleWander=0,s.yPoleWander=0,s.xPoleOffset=0,s.yPoleOffset=0,s.ut1MinusUtc=0,s;var u=t[r],l=t[i];if(u.equals(l)||a.equals(u))return Me(e,n,r,o,s),s;if(a.equals(l))return Me(e,n,i,o,s),s;var d=pe.secondsDifference(a,u)/pe.secondsDifference(l,u),c=r*o,f=i*o,h=n[c+e._ut1MinusUtcSecondsColumn],m=n[f+e._ut1MinusUtcSecondsColumn],p=m-h;if(p>.5||p<-.5){var y=n[c+e._taiMinusUtcSecondsColumn],w=n[f+e._taiMinusUtcSecondsColumn];y!==w&&(l.equals(a)?h=m:m-=w-y)}return s.xPoleWander=_e(d,n[c+e._xPoleWanderRadiansColumn],n[f+e._xPoleWanderRadiansColumn]),s.yPoleWander=_e(d,n[c+e._yPoleWanderRadiansColumn],n[f+e._yPoleWanderRadiansColumn]),s.xPoleOffset=_e(d,n[c+e._xCelestialPoleOffsetRadiansColumn],n[f+e._xCelestialPoleOffsetRadiansColumn]),s.yPoleOffset=_e(d,n[c+e._yCelestialPoleOffsetRadiansColumn],n[f+e._yCelestialPoleOffsetRadiansColumn]),s.ut1MinusUtc=_e(d,h,m),s}function Oe(e,t,n){this.heading=r.defaultValue(e,0),this.pitch=r.defaultValue(t,0),this.roll=r.defaultValue(n,0)}function ve(e,t,n){this.x=e,this.y=t,this.s=n}function Se(e){e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT),this._xysFileUrlTemplate=u.Resource.createIfNeeded(e.xysFileUrlTemplate),this._interpolationOrder=r.defaultValue(e.interpolationOrder,9),this._sampleZeroJulianEphemerisDate=r.defaultValue(e.sampleZeroJulianEphemerisDate,2442396.5),this._sampleZeroDateTT=new pe(this._sampleZeroJulianEphemerisDate,0,$.TAI),this._stepSizeDays=r.defaultValue(e.stepSizeDays,1),this._samplesPerXysFile=r.defaultValue(e.samplesPerXysFile,1e3),this._totalSamples=r.defaultValue(e.totalSamples,27426),this._samples=new Array(3*this._totalSamples),this._chunkDownloadsInProgress=[];for(var t=this._interpolationOrder,n=this._denominators=new Array(t+1),a=this._xTable=new Array(t+1),i=Math.pow(this._stepSizeDays,t),s=0;s<=t;++s){n[s]=i,a[s]=s*this._stepSizeDays;for(var o=0;o<=t;++o)o!==s&&(n[s]*=s-o);n[s]=1/n[s]}this._work=new Array(t+1),this._coef=new Array(t+1)}pe.toGregorianDate=function(e,t){var n=!1,a=ae(e,ye);r.defined(a)||(pe.addSeconds(e,-1,ye),a=ae(ye,ye),n=!0);var i=a.dayNumber,s=a.secondsOfDay;s>=43200&&(i+=1);var o=i+68569|0,u=4*o/146097|0,l=4e3*((o=o-((146097*u+3)/4|0)|0)+1)/1461001|0,d=80*(o=o-(1461*l/4|0)+31|0)/2447|0,c=o-(2447*d/80|0)|0,f=d+2-12*(o=d/11|0)|0,h=100*(u-49)+l+o|0,m=s/H.SECONDS_PER_HOUR|0,p=s-m*H.SECONDS_PER_HOUR,y=p/H.SECONDS_PER_MINUTE|0,w=0|(p-=y*H.SECONDS_PER_MINUTE),C=(p-w)/H.SECONDS_PER_MILLISECOND;return(m+=12)>23&&(m-=24),n&&(w+=1),r.defined(t)?(t.year=h,t.month=f,t.day=c,t.hour=m,t.minute=y,t.second=w,t.millisecond=C,t.isLeapSecond=n,t):new Z(h,f,c,m,y,w,C,n)},pe.toDate=function(e){var t=pe.toGregorianDate(e,Q),n=t.second;return t.isLeapSecond&&(n-=1),new Date(Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,n,t.millisecond))},pe.toIso8601=function(e,t){var n=pe.toGregorianDate(e,Q),a=n.year,i=n.month,s=n.day,o=n.hour,u=n.minute,l=n.second,d=n.millisecond;return 1e4===a&&1===i&&1===s&&0===o&&0===u&&0===l&&0===d&&(a=9999,i=12,s=31,o=24),r.defined(t)||0===d?r.defined(t)&&0!==t?G("%04d-%02d-%02dT%02d:%02d:%02d.%sZ",a,i,s,o,u,l,(.01*d).toFixed(t).replace(".","").slice(0,t)):G("%04d-%02d-%02dT%02d:%02d:%02dZ",a,i,s,o,u,l):G("%04d-%02d-%02dT%02d:%02d:%02d.%sZ",a,i,s,o,u,l,(.01*d).toString().replace(".",""))},pe.clone=function(e,t){if(r.defined(e))return r.defined(t)?(t.dayNumber=e.dayNumber,t.secondsOfDay=e.secondsOfDay,t):new pe(e.dayNumber,e.secondsOfDay,$.TAI)},pe.compare=function(e,t){var n=e.dayNumber-t.dayNumber;return 0!==n?n:e.secondsOfDay-t.secondsOfDay},pe.equals=function(e,t){return e===t||r.defined(e)&&r.defined(t)&&e.dayNumber===t.dayNumber&&e.secondsOfDay===t.secondsOfDay},pe.equalsEpsilon=function(e,t,n){return e===t||r.defined(e)&&r.defined(t)&&Math.abs(pe.secondsDifference(e,t))<=n},pe.totalDays=function(e){return e.dayNumber+e.secondsOfDay/H.SECONDS_PER_DAY},pe.secondsDifference=function(e,t){return(e.dayNumber-t.dayNumber)*H.SECONDS_PER_DAY+(e.secondsOfDay-t.secondsOfDay)},pe.daysDifference=function(e,t){return e.dayNumber-t.dayNumber+(e.secondsOfDay-t.secondsOfDay)/H.SECONDS_PER_DAY},pe.computeTaiMinusUtc=function(e){te.julianDate=e;var t=pe.leapSeconds,n=B(t,te,ee);return n<0&&(n=~n,--n<0&&(n=0)),t[n].offset},pe.addSeconds=function(e,t,n){return re(e.dayNumber,e.secondsOfDay+t,n)},pe.addMinutes=function(e,t,n){var a=e.secondsOfDay+t*H.SECONDS_PER_MINUTE;return re(e.dayNumber,a,n)},pe.addHours=function(e,t,n){var a=e.secondsOfDay+t*H.SECONDS_PER_HOUR;return re(e.dayNumber,a,n)},pe.addDays=function(e,t,n){return re(e.dayNumber+t,e.secondsOfDay,n)},pe.lessThan=function(e,t){return pe.compare(e,t)<0},pe.lessThanOrEquals=function(e,t){return pe.compare(e,t)<=0},pe.greaterThan=function(e,t){return pe.compare(e,t)>0},pe.greaterThanOrEquals=function(e,t){return pe.compare(e,t)>=0},pe.prototype.clone=function(e){return pe.clone(this,e)},pe.prototype.equals=function(e){return pe.equals(this,e)},pe.prototype.equalsEpsilon=function(e,t){return pe.equalsEpsilon(this,e,t)},pe.prototype.toString=function(){return pe.toIso8601(this)},pe.leapSeconds=[new J(new pe(2441317,43210,$.TAI),10),new J(new pe(2441499,43211,$.TAI),11),new J(new pe(2441683,43212,$.TAI),12),new J(new pe(2442048,43213,$.TAI),13),new J(new pe(2442413,43214,$.TAI),14),new J(new pe(2442778,43215,$.TAI),15),new J(new pe(2443144,43216,$.TAI),16),new J(new pe(2443509,43217,$.TAI),17),new J(new pe(2443874,43218,$.TAI),18),new J(new pe(2444239,43219,$.TAI),19),new J(new pe(2444786,43220,$.TAI),20),new J(new pe(2445151,43221,$.TAI),21),new J(new pe(2445516,43222,$.TAI),22),new J(new pe(2446247,43223,$.TAI),23),new J(new pe(2447161,43224,$.TAI),24),new J(new pe(2447892,43225,$.TAI),25),new J(new pe(2448257,43226,$.TAI),26),new J(new pe(2448804,43227,$.TAI),27),new J(new pe(2449169,43228,$.TAI),28),new J(new pe(2449534,43229,$.TAI),29),new J(new pe(2450083,43230,$.TAI),30),new J(new pe(2450630,43231,$.TAI),31),new J(new pe(2451179,43232,$.TAI),32),new J(new pe(2453736,43233,$.TAI),33),new J(new pe(2454832,43234,$.TAI),34),new J(new pe(2456109,43235,$.TAI),35),new J(new pe(2457204,43236,$.TAI),36),new J(new pe(2457754,43237,$.TAI),37)],we.NONE=Object.freeze({getPromiseToLoad:function(){return r.when()},compute:function(e,t){return r.defined(t)?(t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0):t=new j(0,0,0,0,0),t}}),we.prototype.getPromiseToLoad=function(){return r.when(this._downloadPromise)},we.prototype.compute=function(e,t){if(r.defined(this._samples)){if(r.defined(t)||(t=new j(0,0,0,0,0)),0===this._samples.length)return t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0,t;var n=this._dates,a=this._lastIndex,i=0,s=0;if(r.defined(a)){var o=n[a],u=n[a+1],d=pe.lessThanOrEquals(o,e),c=!r.defined(u),f=c||pe.greaterThanOrEquals(u,e);if(d&&f)return i=a,!c&&u.equals(e)&&++i,s=i+1,Ee(this,n,this._samples,e,i,s,t),t}var h=B(n,e,pe.compare,this._dateColumn);return h>=0?(h<n.length-1&&n[h+1].equals(e)&&++h,i=h,s=h):(i=(s=~h)-1)<0&&(i=0),this._lastIndex=i,Ee(this,n,this._samples,e,i,s,t),t}if(r.defined(this._dataError))throw new l.RuntimeError(this._dataError)},Oe.fromQuaternion=function(e,t){r.defined(t)||(t=new Oe);var n=2*(e.w*e.y-e.z*e.x),a=1-2*(e.x*e.x+e.y*e.y),i=2*(e.w*e.x+e.y*e.z),o=1-2*(e.y*e.y+e.z*e.z),u=2*(e.w*e.z+e.x*e.y);return t.heading=-Math.atan2(u,o),t.roll=Math.atan2(i,a),t.pitch=-s.CesiumMath.asinClamped(n),t},Oe.fromDegrees=function(e,t,n,a){return r.defined(a)||(a=new Oe),a.heading=e*s.CesiumMath.RADIANS_PER_DEGREE,a.pitch=t*s.CesiumMath.RADIANS_PER_DEGREE,a.roll=n*s.CesiumMath.RADIANS_PER_DEGREE,a},Oe.clone=function(e,t){if(r.defined(e))return r.defined(t)?(t.heading=e.heading,t.pitch=e.pitch,t.roll=e.roll,t):new Oe(e.heading,e.pitch,e.roll)},Oe.equals=function(e,t){return e===t||r.defined(e)&&r.defined(t)&&e.heading===t.heading&&e.pitch===t.pitch&&e.roll===t.roll},Oe.equalsEpsilon=function(e,t,n,a){return e===t||r.defined(e)&&r.defined(t)&&s.CesiumMath.equalsEpsilon(e.heading,t.heading,n,a)&&s.CesiumMath.equalsEpsilon(e.pitch,t.pitch,n,a)&&s.CesiumMath.equalsEpsilon(e.roll,t.roll,n,a)},Oe.prototype.clone=function(e){return Oe.clone(this,e)},Oe.prototype.equals=function(e){return Oe.equals(this,e)},Oe.prototype.equalsEpsilon=function(e,t,n){return Oe.equalsEpsilon(this,e,t,n)},Oe.prototype.toString=function(){return"("+this.heading+", "+this.pitch+", "+this.roll+")"};var Te=new pe(0,0,$.TAI);function ge(e,t,n){var a=Te;return a.dayNumber=t,a.secondsOfDay=n,pe.daysDifference(a,e._sampleZeroDateTT)}function De(e,t){if(e._chunkDownloadsInProgress[t])return e._chunkDownloadsInProgress[t];var n,a=r.when.defer();e._chunkDownloadsInProgress[t]=a;var i=e._xysFileUrlTemplate;return n=r.defined(i)?i.getDerivedResource({templateValues:{0:t}}):new u.Resource({url:u.buildModuleUrl("Assets/IAU2006_XYS/IAU2006_XYS_"+t+".json")}),r.when(n.fetchJson(),(function(n){e._chunkDownloadsInProgress[t]=!1;for(var r=e._samples,i=n.samples,s=t*e._samplesPerXysFile*3,o=0,u=i.length;o<u;++o)r[s+o]=i[o];a.resolve()})),a.promise}Se.prototype.preload=function(e,t,n,a){var i=ge(this,e,t),s=ge(this,n,a),o=i/this._stepSizeDays-this._interpolationOrder/2|0;o<0&&(o=0);var u=s/this._stepSizeDays-this._interpolationOrder/2|0+this._interpolationOrder;u>=this._totalSamples&&(u=this._totalSamples-1);for(var l=o/this._samplesPerXysFile|0,d=u/this._samplesPerXysFile|0,c=[],f=l;f<=d;++f)c.push(De(this,f));return r.when.all(c)},Se.prototype.computeXysRadians=function(e,t,n){var a=ge(this,e,t);if(!(a<0)){var i=a/this._stepSizeDays|0;if(!(i>=this._totalSamples)){var s=this._interpolationOrder,o=i-(s/2|0);o<0&&(o=0);var u=o+s;u>=this._totalSamples&&(o=(u=this._totalSamples-1)-s)<0&&(o=0);var l=!1,d=this._samples;if(r.defined(d[3*o])||(De(this,o/this._samplesPerXysFile|0),l=!0),r.defined(d[3*u])||(De(this,u/this._samplesPerXysFile|0),l=!0),!l){r.defined(n)?(n.x=0,n.y=0,n.s=0):n=new ve(0,0,0);var c,f,h=a-o*this._stepSizeDays,m=this._work,p=this._denominators,y=this._coef,w=this._xTable;for(c=0;c<=s;++c)m[c]=h-w[c];for(c=0;c<=s;++c){for(y[c]=1,f=0;f<=s;++f)f!==c&&(y[c]*=m[f]);y[c]*=p[c];var C=3*(o+c);n.x+=y[c]*d[C++],n.y+=y[c]*d[C++],n.s+=y[c]*d[C]}return n}}}};var Pe={},Ne={up:{south:"east",north:"west",west:"south",east:"north"},down:{south:"west",north:"east",west:"north",east:"south"},south:{up:"west",down:"east",west:"down",east:"up"},north:{up:"east",down:"west",west:"up",east:"down"},west:{up:"north",down:"south",north:"down",south:"up"},east:{up:"south",down:"north",north:"up",south:"down"}},Ie={north:[-1,0,0],east:[0,1,0],up:[0,0,1],south:[1,0,0],west:[0,-1,0],down:[0,0,-1]},Re={},Ae={east:new n.Cartesian3,north:new n.Cartesian3,up:new n.Cartesian3,west:new n.Cartesian3,south:new n.Cartesian3,down:new n.Cartesian3},Ue=new n.Cartesian3,be=new n.Cartesian3,ze=new n.Cartesian3;Pe.localFrameToFixedFrameGenerator=function(e,o){if(!Ne.hasOwnProperty(e)||!Ne[e].hasOwnProperty(o))throw new a.DeveloperError("firstAxis and secondAxis must be east, north, up, west, south or down.");var u,l=Ne[e][o],d=e+o;return r.defined(Re[d])?u=Re[d]:(u=function(a,u,d){if(r.defined(d)||(d=new i.Matrix4),n.Cartesian3.equalsEpsilon(a,n.Cartesian3.ZERO,s.CesiumMath.EPSILON14))n.Cartesian3.unpack(Ie[e],0,Ue),n.Cartesian3.unpack(Ie[o],0,be),n.Cartesian3.unpack(Ie[l],0,ze);else if(s.CesiumMath.equalsEpsilon(a.x,0,s.CesiumMath.EPSILON14)&&s.CesiumMath.equalsEpsilon(a.y,0,s.CesiumMath.EPSILON14)){var c=s.CesiumMath.sign(a.z);n.Cartesian3.unpack(Ie[e],0,Ue),"east"!==e&&"west"!==e&&n.Cartesian3.multiplyByScalar(Ue,c,Ue),n.Cartesian3.unpack(Ie[o],0,be),"east"!==o&&"west"!==o&&n.Cartesian3.multiplyByScalar(be,c,be),n.Cartesian3.unpack(Ie[l],0,ze),"east"!==l&&"west"!==l&&n.Cartesian3.multiplyByScalar(ze,c,ze)}else{(u=r.defaultValue(u,t.Ellipsoid.WGS84)).geodeticSurfaceNormal(a,Ae.up);var f=Ae.up,h=Ae.east;h.x=-a.y,h.y=a.x,h.z=0,n.Cartesian3.normalize(h,Ae.east),n.Cartesian3.cross(f,h,Ae.north),n.Cartesian3.multiplyByScalar(Ae.up,-1,Ae.down),n.Cartesian3.multiplyByScalar(Ae.east,-1,Ae.west),n.Cartesian3.multiplyByScalar(Ae.north,-1,Ae.south),Ue=Ae[e],be=Ae[o],ze=Ae[l]}return d[0]=Ue.x,d[1]=Ue.y,d[2]=Ue.z,d[3]=0,d[4]=be.x,d[5]=be.y,d[6]=be.z,d[7]=0,d[8]=ze.x,d[9]=ze.y,d[10]=ze.z,d[11]=0,d[12]=a.x,d[13]=a.y,d[14]=a.z,d[15]=1,d},Re[d]=u),u},Pe.eastNorthUpToFixedFrame=Pe.localFrameToFixedFrameGenerator("east","north"),Pe.northEastDownToFixedFrame=Pe.localFrameToFixedFrameGenerator("north","east"),Pe.northUpEastToFixedFrame=Pe.localFrameToFixedFrameGenerator("north","up"),Pe.northWestUpToFixedFrame=Pe.localFrameToFixedFrameGenerator("north","west");var Fe=new m,Ve=new n.Cartesian3(1,1,1),We=new i.Matrix4;Pe.headingPitchRollToFixedFrame=function(e,t,a,s,o){s=r.defaultValue(s,Pe.eastNorthUpToFixedFrame);var u=m.fromHeadingPitchRoll(t,Fe),l=i.Matrix4.fromTranslationQuaternionRotationScale(n.Cartesian3.ZERO,u,Ve,We);return o=s(e,a,o),i.Matrix4.multiply(o,l,o)};var qe=new i.Matrix4,Le=new i.Matrix3;Pe.headingPitchRollQuaternion=function(e,t,n,a,r){var s=Pe.headingPitchRollToFixedFrame(e,t,n,a,qe),o=i.Matrix4.getMatrix3(s,Le);return m.fromRotationMatrix(o,r)};var ke=new n.Cartesian3(1,1,1),Ye=new n.Cartesian3,Be=new i.Matrix4,je=new i.Matrix4,Ge=new i.Matrix3,Ze=new m;Pe.fixedFrameToHeadingPitchRoll=function(e,a,s,o){a=r.defaultValue(a,t.Ellipsoid.WGS84),s=r.defaultValue(s,Pe.eastNorthUpToFixedFrame),r.defined(o)||(o=new Oe);var u=i.Matrix4.getTranslation(e,Ye);if(n.Cartesian3.equals(u,n.Cartesian3.ZERO))return o.heading=0,o.pitch=0,o.roll=0,o;var l=i.Matrix4.inverseTransformation(s(u,a,Be),Be),d=i.Matrix4.setScale(e,ke,je);d=i.Matrix4.setTranslation(d,n.Cartesian3.ZERO,d),l=i.Matrix4.multiply(l,d,l);var c=m.fromRotationMatrix(i.Matrix4.getMatrix3(l,Ge),Ze);return c=m.normalize(c,c),Oe.fromQuaternion(c,o)};var Xe=s.CesiumMath.TWO_PI/86400,Je=new pe;Pe.computeTemeToPseudoFixedMatrix=function(e,t){var n,a=(Je=pe.addSeconds(e,-pe.computeTaiMinusUtc(e),Je)).dayNumber,o=Je.secondsOfDay,u=a-2451545,l=(24110.54841+(n=o>=43200?(u+.5)/H.DAYS_PER_JULIAN_CENTURY:(u-.5)/H.DAYS_PER_JULIAN_CENTURY)*(8640184.812866+n*(.093104+-62e-7*n)))*Xe%s.CesiumMath.TWO_PI+(72921158553e-15+11772758384668e-32*(a-2451545.5))*((o+.5*H.SECONDS_PER_DAY)%H.SECONDS_PER_DAY),d=Math.cos(l),c=Math.sin(l);return r.defined(t)?(t[0]=d,t[1]=-c,t[2]=0,t[3]=c,t[4]=d,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t):new i.Matrix3(d,c,0,-c,d,0,0,0,1)},Pe.iau2006XysData=new Se,Pe.earthOrientationParameters=we.NONE;var He=32.184;Pe.preloadIcrfFixed=function(e){var t=e.start.dayNumber,n=e.start.secondsOfDay+He,a=e.stop.dayNumber,i=e.stop.secondsOfDay+He,s=Pe.iau2006XysData.preload(t,n,a,i),o=Pe.earthOrientationParameters.getPromiseToLoad();return r.when.all([s,o])},Pe.computeIcrfToFixedMatrix=function(e,t){r.defined(t)||(t=new i.Matrix3);var n=Pe.computeFixedToIcrfMatrix(e,t);if(r.defined(n))return i.Matrix3.transpose(n,t)};var $e=new ve(0,0,0),Qe=new j(0,0,0,0,0,0),Ke=new i.Matrix3,et=new i.Matrix3;Pe.computeFixedToIcrfMatrix=function(e,t){r.defined(t)||(t=new i.Matrix3);var n=Pe.earthOrientationParameters.compute(e,Qe);if(r.defined(n)){var a=e.dayNumber,o=e.secondsOfDay+He,u=Pe.iau2006XysData.computeXysRadians(a,o,$e);if(r.defined(u)){var l=u.x+n.xPoleOffset,d=u.y+n.yPoleOffset,c=1/(1+Math.sqrt(1-l*l-d*d)),f=Ke;f[0]=1-c*l*l,f[3]=-c*l*d,f[6]=l,f[1]=-c*l*d,f[4]=1-c*d*d,f[7]=d,f[2]=-l,f[5]=-d,f[8]=1-c*(l*l+d*d);var h=i.Matrix3.fromRotationZ(-u.s,et),m=i.Matrix3.multiply(f,h,Ke),p=e.dayNumber-2451545,y=(e.secondsOfDay-pe.computeTaiMinusUtc(e)+n.ut1MinusUtc)/H.SECONDS_PER_DAY,w=.779057273264+y+.00273781191135448*(p+y);w=w%1*s.CesiumMath.TWO_PI;var C=i.Matrix3.fromRotationZ(w,et),x=i.Matrix3.multiply(m,C,Ke),M=Math.cos(n.xPoleWander),_=Math.cos(n.yPoleWander),E=Math.sin(n.xPoleWander),O=Math.sin(n.yPoleWander),v=a-2451545+o/H.SECONDS_PER_DAY,S=-47e-6*(v/=36525)*s.CesiumMath.RADIANS_PER_DEGREE/3600,T=Math.cos(S),g=Math.sin(S),D=et;return D[0]=M*T,D[1]=M*g,D[2]=E,D[3]=-_*g+O*E*T,D[4]=_*T+O*E*g,D[5]=-O*M,D[6]=-O*g-_*E*T,D[7]=O*T-_*E*g,D[8]=_*M,i.Matrix3.multiply(x,D,t)}}};var tt=new o.Cartesian4;Pe.pointToWindowCoordinates=function(e,t,n,a){return(a=Pe.pointToGLWindowCoordinates(e,t,n,a)).y=2*t[5]-a.y,a},Pe.pointToGLWindowCoordinates=function(e,n,a,s){r.defined(s)||(s=new t.Cartesian2);var u=tt;return i.Matrix4.multiplyByVector(e,o.Cartesian4.fromElements(a.x,a.y,a.z,1,u),u),o.Cartesian4.multiplyByScalar(u,1/u.w,u),i.Matrix4.multiplyByVector(n,u,u),t.Cartesian2.fromCartesian4(u,s)};var nt=new n.Cartesian3,at=new n.Cartesian3,rt=new n.Cartesian3;Pe.rotationMatrixFromPositionVelocity=function(e,a,o,u){var l=r.defaultValue(o,t.Ellipsoid.WGS84).geodeticSurfaceNormal(e,nt),d=n.Cartesian3.cross(a,l,at);n.Cartesian3.equalsEpsilon(d,n.Cartesian3.ZERO,s.CesiumMath.EPSILON6)&&(d=n.Cartesian3.clone(n.Cartesian3.UNIT_X,d));var c=n.Cartesian3.cross(d,a,rt);return n.Cartesian3.normalize(c,c),n.Cartesian3.cross(a,c,d),n.Cartesian3.negate(d,d),n.Cartesian3.normalize(d,d),r.defined(u)||(u=new i.Matrix3),u[0]=a.x,u[1]=a.y,u[2]=a.z,u[3]=d.x,u[4]=d.y,u[5]=d.z,u[6]=c.x,u[7]=c.y,u[8]=c.z,u};var it=new i.Matrix4(0,0,1,0,1,0,0,0,0,1,0,0,0,0,0,1),st=new n.Cartographic,ot=new n.Cartesian3,ut=new n.Cartesian3,lt=new i.Matrix3,dt=new i.Matrix4,ct=new i.Matrix4;function ft(e){e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT),this.attributes=e.attributes,this.indices=e.indices,this.primitiveType=r.defaultValue(e.primitiveType,i.PrimitiveType.TRIANGLES),this.boundingSphere=e.boundingSphere,this.geometryType=r.defaultValue(e.geometryType,d.NONE),this.boundingSphereCV=e.boundingSphereCV,this.offsetAttribute=e.offsetAttribute}Pe.basisTo2D=function(e,t,a){var r=i.Matrix4.getTranslation(t,ut),s=e.ellipsoid,o=s.cartesianToCartographic(r,st),u=e.project(o,ot);n.Cartesian3.fromElements(u.z,u.x,u.y,u);var l=Pe.eastNorthUpToFixedFrame(r,s,dt),d=i.Matrix4.inverseTransformation(l,ct),c=i.Matrix4.getMatrix3(t,lt),f=i.Matrix4.multiplyByMatrix3(d,c,a);return i.Matrix4.multiply(it,f,a),i.Matrix4.setTranslation(a,u,a),a},Pe.wgs84To2DModelMatrix=function(e,t,a){var r=e.ellipsoid,s=Pe.eastNorthUpToFixedFrame(t,r,dt),o=i.Matrix4.inverseTransformation(s,ct),u=r.cartesianToCartographic(t,st),l=e.project(u,ot);n.Cartesian3.fromElements(l.z,l.x,l.y,l);var d=i.Matrix4.fromTranslation(l,dt);return i.Matrix4.multiply(it,o,a),i.Matrix4.multiply(d,a,a),a},Pe.buildUp=function(e,t){var a=t.clone(),r=e.clone();r=n.Cartesian3.normalize(r,r),Math.abs(n.Cartesian3.dot(r,a))>=1&&(r=Math.abs(n.Cartesian3.dot(a,n.Cartesian3.UNIT_Y))<1?n.Cartesian3.clone(n.Cartesian3.UNIT_Y,r):n.Cartesian3.clone(n.Cartesian3.UNIT_Z,r));var i=new n.Cartesian3;return n.Cartesian3.cross(r,a,i),i=n.Cartesian3.normalize(i,i),n.Cartesian3.cross(a,i,r),r=n.Cartesian3.normalize(r,r)},Pe.getHeading=function(e,t){var n;return n=s.CesiumMath.equalsEpsilon(Math.abs(e.z),1,s.CesiumMath.EPSILON3)?Math.atan2(t.y,t.x)-s.CesiumMath.PI_OVER_TWO:Math.atan2(e.y,e.x)-s.CesiumMath.PI_OVER_TWO,s.CesiumMath.TWO_PI-s.CesiumMath.zeroToTwoPi(n)},Pe.convertToColumbusCartesian=function(e){var t=new u.GeographicProjection,a=t.ellipsoid,r=new n.Cartesian3,i=new n.Cartographic;return a.cartesianToCartographic(e,i),t.project(i,r),n.Cartesian3.fromElements(r.z,r.x,r.y)},Pe.convertTo3DCartesian=function(e){var t=new u.GeographicProjection,a=t.ellipsoid,r=new n.Cartesian3,i=new n.Cartographic;return r=n.Cartesian3.fromElements(e.y,e.z,e.x),t.unproject(r,i),a.cartographicToCartesian(i,r)},ft.computeNumberOfVertices=function(e){var t=-1;for(var n in e.attributes)if(e.attributes.hasOwnProperty(n)&&r.defined(e.attributes[n])&&r.defined(e.attributes[n].values)){var a=e.attributes[n];if(a.isInstanceAttribute)continue;t=a.values.length/a.componentsPerAttribute}return t};var ht=new n.Cartographic,mt=new n.Cartesian3,pt=new i.Matrix4,yt=[new n.Cartographic,new n.Cartographic,new n.Cartographic],wt=[new t.Cartesian2,new t.Cartesian2,new t.Cartesian2],Ct=[new t.Cartesian2,new t.Cartesian2,new t.Cartesian2],xt=new n.Cartesian3,Mt=new m,_t=new i.Matrix4,Et=new c;ft._textureCoordinateRotationPoints=function(e,a,r,s){var o,u=t.Rectangle.center(s,ht),l=n.Cartographic.toCartesian(u,r,mt),d=Pe.eastNorthUpToFixedFrame(l,r,pt),f=i.Matrix4.inverse(d,pt),h=wt,p=yt;p[0].longitude=s.west,p[0].latitude=s.south,p[1].longitude=s.west,p[1].latitude=s.north,p[2].longitude=s.east,p[2].latitude=s.south;var y=xt;for(o=0;o<3;o++)n.Cartographic.toCartesian(p[o],r,y),y=i.Matrix4.multiplyByPointAsVector(f,y,y),h[o].x=y.x,h[o].y=y.y;var w=m.fromAxisAngle(n.Cartesian3.UNIT_Z,-a,Mt),C=i.Matrix3.fromQuaternion(w,_t),x=e.length,M=Number.POSITIVE_INFINITY,_=Number.POSITIVE_INFINITY,E=Number.NEGATIVE_INFINITY,O=Number.NEGATIVE_INFINITY;for(o=0;o<x;o++)y=i.Matrix4.multiplyByPointAsVector(f,e[o],y),y=i.Matrix3.multiplyByVector(C,y,y),M=Math.min(M,y.x),_=Math.min(_,y.y),E=Math.max(E,y.x),O=Math.max(O,y.y);var v=c.fromRotation(a,Et),S=Ct;S[0].x=M,S[0].y=_,S[1].x=M,S[1].y=O,S[2].x=E,S[2].y=_;var T=h[0],g=h[2].x-T.x,D=h[1].y-T.y;for(o=0;o<3;o++){var P=S[o];c.multiplyByVector(v,P,P),P.x=(P.x-T.x)/g,P.y=(P.y-T.y)/D}var N=S[0],I=S[1],R=S[2],A=new Array(6);return t.Cartesian2.pack(N,A),t.Cartesian2.pack(I,A,2),t.Cartesian2.pack(R,A,4),A},e.Geometry=ft,e.GeometryAttribute=function(e){e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT),this.componentDatatype=e.componentDatatype,this.componentsPerAttribute=e.componentsPerAttribute,this.normalize=r.defaultValue(e.normalize,!1),this.values=e.values},e.GeometryType=d,e.Matrix2=c,e.Quaternion=m,e.Transforms=Pe}));
