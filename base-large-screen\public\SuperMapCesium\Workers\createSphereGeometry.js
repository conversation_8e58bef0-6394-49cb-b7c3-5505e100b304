define(["./when-b60132fc","./Cartographic-3309dd0d","./Check-7b2a090c","./EllipsoidGeometry-77f654e8","./VertexFormat-6446fca0","./Math-119be1a3","./arrayFill-4513d7ad","./buildModuleUrl-8958744c","./Cartesian2-47311507","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4"],(function(e,t,r,i,a,o,n,s,d,c,l,m,u,p,y,f,G,b,k){"use strict";function v(r){var a=e.defaultValue(r.radius,1),o={radii:new t.Cartesian3(a,a,a),stackPartitions:r.stackPartitions,slicePartitions:r.slicePartitions,vertexFormat:r.vertexFormat};this._ellipsoidGeometry=new i.EllipsoidGeometry(o),this._workerName="createSphereGeometry"}v.packedLength=i.EllipsoidGeometry.packedLength,v.pack=function(e,t,r){return i.EllipsoidGeometry.pack(e._ellipsoidGeometry,t,r)};var x=new i.EllipsoidGeometry,F={radius:void 0,radii:new t.Cartesian3,vertexFormat:new a.VertexFormat,stackPartitions:void 0,slicePartitions:void 0};return v.unpack=function(r,o,n){var s=i.EllipsoidGeometry.unpack(r,o,x);return F.vertexFormat=a.VertexFormat.clone(s._vertexFormat,F.vertexFormat),F.stackPartitions=s._stackPartitions,F.slicePartitions=s._slicePartitions,e.defined(n)?(t.Cartesian3.clone(s._radii,F.radii),n._ellipsoidGeometry=new i.EllipsoidGeometry(F),n):(F.radius=s._radii.x,new v(F))},v.createGeometry=function(e){return i.EllipsoidGeometry.createGeometry(e._ellipsoidGeometry)},function(t,r){return e.defined(r)&&(t=v.unpack(t,r)),v.createGeometry(t)}}));
