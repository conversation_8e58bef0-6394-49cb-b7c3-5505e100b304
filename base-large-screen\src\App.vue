<template>
  <LayerView router-name="editLayer" :z-index="1"></LayerView>
  <LayerView router-name="firstLayer" :z-index="2"></LayerView>
  <LayerView router-name="secondLayer" :z-index="3"></LayerView>
  <LayerView router-name="thirdLayer" :z-index="4" render-mode="fullPath"></LayerView>
  <LayerView router-name="fourthLayer" :z-index="5"></LayerView>
  <LayerView router-name="fifthLayer" :z-index="6"></LayerView>
</template>
<script setup>
import LayerView from './components/core/LayerView.vue'
</script>
