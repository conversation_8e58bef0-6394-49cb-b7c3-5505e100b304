<template>
  <div class="cesium-gh-example">
    <CesiumGH 
      ref="cesiumGHRef"
      :showGHControls="showControls"
    />
    
    <!-- 外部控制按钮 -->
    <div class="external-controls">
      <button @click="toggleControls">{{ showControls ? '隐藏' : '显示' }}控制面板</button>
      <button @click="testGHFeatures">测试GH功能</button>
      <button @click="resetView">重置视角</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import CesiumGH from './CesiumGH.vue'

const cesiumGHRef = ref(null)
const showControls = ref(true)

// 切换控制面板显示
function toggleControls() {
  showControls.value = !showControls.value
}

// 测试GH功能
function testGHFeatures() {
  if (!cesiumGHRef.value) return
  
  console.log('Testing GH features...')
  
  // 依次测试各种功能
  setTimeout(() => {
    cesiumGHRef.value.toggleLighting()
  }, 1000)
  
  setTimeout(() => {
    cesiumGHRef.value.toggleAtmosphere()
  }, 2000)
  
  setTimeout(() => {
    cesiumGHRef.value.addTileCoordinates()
  }, 3000)
  
  setTimeout(() => {
    cesiumGHRef.value.toggleFog()
  }, 4000)
  
  setTimeout(() => {
    cesiumGHRef.value.setCustomView()
  }, 5000)
}

// 重置视角
function resetView() {
  if (!cesiumGHRef.value) return
  
  // 清除所有GH功能并重置视角
  cesiumGHRef.value.clearGHFeatures()
  
  // 重置到默认视角
  setTimeout(() => {
    cesiumGHRef.value.setCustomView()
  }, 500)
}

onMounted(() => {
  console.log('CesiumGH Example mounted')
})
</script>

<style scoped>
.cesium-gh-example {
  position: relative;
  width: 100%;
  height: 100vh;
}

.external-controls {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.external-controls button {
  padding: 10px 15px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border: 1px solid #00d4ff;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.external-controls button:hover {
  background: rgba(0, 212, 255, 0.2);
  transform: translateY(-1px);
}

.external-controls button:active {
  background: rgba(0, 212, 255, 0.4);
  transform: translateY(0);
}
</style>
