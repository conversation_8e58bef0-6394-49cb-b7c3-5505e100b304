<template>
  <LayoutBox :parts="parts" :focus="true"></LayoutBox>
</template>
<script setup>
import LeftOne from './LeftOne.vue'
import LeftTwo from './LeftTwo.vue'
import LeftThree from './LeftThree.vue'
import RightOne from './RightOne.vue'
import RightTwo from './RightTwo.vue'
import RightThree from './RightThree.vue'
const parts = [
  {
    component: LeftOne,
    positionStyle: {
      top: "100px",
      left: "10px"
    },
    animation: {
      type: "LeftSlide",
      startTime: 0
    }
  },
  {
    component: LeftTwo,
    positionStyle: {
      top: "430px",
      left: "10px"
    },
    animation: {
      type: "LeftSlide",
      startTime: 0.5
    }
  },
  {
    component: LeftThree,
    positionStyle: {
      top: "760px",
      left: "10px"
    },
    animation: {
      type: "LeftSlide",
      startTime: 1
    }
  },
  {
    component: RightOne,
    positionStyle: {
      top: "100px",
      right: "10px"
    },
    animation: {
      type: "RightSlide",
      startTime: 0
    }
  },
  {
    component: RightTwo,
    positionStyle: {
      top: "430px",
      right: "10px"
    },
    animation: {
      type: "RightSlide",
      startTime: 0.5
    }
  },
  {
    component: RightThree,
    positionStyle: {
      top: "760px",
      right: "10px"
    },
    animation: {
      type: "RightSlide",
      startTime: 1
    }
  },
]
</script>
