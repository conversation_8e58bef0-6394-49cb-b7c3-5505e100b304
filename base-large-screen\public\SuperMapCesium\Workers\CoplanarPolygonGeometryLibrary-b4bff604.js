define(["exports","./Cartesian2-47311507","./Cartographic-3309dd0d","./Check-7b2a090c","./FeatureDetection-806b12f0","./OrientedBoundingBox-08964f84"],(function(n,t,e,r,a,i){"use strict";var o={},u=new e.Cartesian3,s=new e.Cartesian3,C=new e.Cartesian3,c=new e.Cartesian3,m=new i.OrientedBoundingBox;function d(n,r,a,i,o){var s=e.Cartesian3.subtract(n,r,u),C=e.Cartesian3.dot(a,s),c=e.Cartesian3.dot(i,s);return t.Cartesian2.fromElements(C,c,o)}o.validOutline=function(n){var t=i.OrientedBoundingBox.fromPoints(n,m).halfAxes,r=a.Matrix3.getColumn(t,0,s),o=a.Matrix3.getColumn(t,1,C),u=a.Matrix3.getColumn(t,2,c),d=e.Cartesian3.magnitude(r),g=e.Cartesian3.magnitude(o),l=e.Cartesian3.magnitude(u);return!(0===d&&(0===g||0===l)||0===g&&0===l)},o.computeProjectTo2DArguments=function(n,t,r,o){var u,d,g=i.OrientedBoundingBox.fromPoints(n,m),l=g.halfAxes,f=a.Matrix3.getColumn(l,0,s),x=a.Matrix3.getColumn(l,1,C),B=a.Matrix3.getColumn(l,2,c),P=e.Cartesian3.magnitude(f),h=e.Cartesian3.magnitude(x),M=e.Cartesian3.magnitude(B),v=Math.min(P,h,M);return(0!==P||0!==h&&0!==M)&&(0!==h||0!==M)&&(v!==h&&v!==M||(u=f),v===P?u=x:v===M&&(d=x),v!==P&&v!==h||(d=B),e.Cartesian3.normalize(u,r),e.Cartesian3.normalize(d,o),e.Cartesian3.clone(g.center,t),!0)},o.createProjectPointsTo2DFunction=function(n,t,e){return function(r){for(var a=new Array(r.length),i=0;i<r.length;i++)a[i]=d(r[i],n,t,e);return a}},o.createProjectPointTo2DFunction=function(n,t,e){return function(r,a){return d(r,n,t,e,a)}},n.CoplanarPolygonGeometryLibrary=o}));
