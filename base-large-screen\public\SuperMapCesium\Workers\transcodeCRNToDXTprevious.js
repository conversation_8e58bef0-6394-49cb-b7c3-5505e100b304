define(["./CompressedTextureBuffer-290a1ff4","./when-b60132fc","./PixelFormat-9345f1c7","./RuntimeError-4a5c8994","./createTaskProcessorWorker","./WebGLConstants-4ae0db90"],(function(CompressedTextureBuffer,when,PixelFormat,RuntimeError,createTaskProcessorWorker,WebGLConstants){"use strict";
/**
     * @licence
     * crunch/crnlib uses the ZLIB license:
     * http://opensource.org/licenses/Zlib
     *
     * Copyright (c) 2010-2016 <PERSON>, Jr. and Binomial LLC
     *
     * This software is provided 'as-is', without any express or implied
     * warranty.  In no event will the authors be held liable for any damages
     * arising from the use of this software.
     *
     * Permission is granted to anyone to use this software for any purpose,
     * including commercial applications, and to alter it and redistribute it
     * freely, subject to the following restrictions:
     *
     * 1. The origin of this software must not be misrepresented; you must not
     * claim that you wrote the original software. If you use this software
     * in a product, an acknowledgment in the product documentation would be
     * appreciated but is not required.
     *
     * 2. Altered source versions must be plainly marked as such, and must not be
     * misrepresented as being the original software.
     *
     * 3. This notice may not be removed or altered from any source distribution.
     */var Module;Module||(Module=(void 0!==Module?Module:null)||{});var moduleOverrides={};for(var key in Module)Module.hasOwnProperty(key)&&(moduleOverrides[key]=Module[key]);var ENVIRONMENT_IS_WEB=!1,ENVIRONMENT_IS_WORKER=!1,ENVIRONMENT_IS_NODE=!1,ENVIRONMENT_IS_SHELL=!1,nodeFS,nodePath;if(Module.ENVIRONMENT)if("WEB"===Module.ENVIRONMENT)ENVIRONMENT_IS_WEB=!0;else if("WORKER"===Module.ENVIRONMENT)ENVIRONMENT_IS_WORKER=!0;else if("NODE"===Module.ENVIRONMENT)ENVIRONMENT_IS_NODE=!0;else{if("SHELL"!==Module.ENVIRONMENT)throw new Error("The provided Module['ENVIRONMENT'] value is not valid. It must be one of: WEB|WORKER|NODE|SHELL.");ENVIRONMENT_IS_SHELL=!0}else ENVIRONMENT_IS_WEB="object"==typeof window,ENVIRONMENT_IS_WORKER="function"==typeof importScripts,ENVIRONMENT_IS_NODE="object"==typeof process&&"function"==typeof require&&!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_WORKER,ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER;if(ENVIRONMENT_IS_NODE)Module.print||(Module.print=console.log),Module.printErr||(Module.printErr=console.warn),Module.read=function(e,r){nodeFS||(nodeFS=require("fs")),nodePath||(nodePath=require("path")),e=nodePath.normalize(e);var i=nodeFS.readFileSync(e);return r?i:i.toString()},Module.readBinary=function(e){var r=Module.read(e,!0);return r.buffer||(r=new Uint8Array(r)),assert(r.buffer),r},Module.load=function(e){globalEval(read(e))},Module.thisProgram||(process.argv.length>1?Module.thisProgram=process.argv[1].replace(/\\/g,"/"):Module.thisProgram="unknown-program"),Module.arguments=process.argv.slice(2),"undefined"!=typeof module&&(module.exports=Module),process.on("uncaughtException",(function(e){if(!(e instanceof ExitStatus))throw e})),Module.inspect=function(){return"[Emscripten Module object]"};else if(ENVIRONMENT_IS_SHELL)Module.print||(Module.print=print),"undefined"!=typeof printErr&&(Module.printErr=printErr),"undefined"!=typeof read?Module.read=read:Module.read=function(){throw"no read() available"},Module.readBinary=function(e){if("function"==typeof readbuffer)return new Uint8Array(readbuffer(e));var r=read(e,"binary");return assert("object"==typeof r),r},"undefined"!=typeof scriptArgs?Module.arguments=scriptArgs:void 0!==arguments&&(Module.arguments=arguments),"function"==typeof quit&&(Module.quit=function(e,r){quit(e)});else{if(!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_WORKER)throw"Unknown runtime environment. Where are we?";if(Module.read=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.send(null),r.responseText},ENVIRONMENT_IS_WORKER&&(Module.readBinary=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)}),Module.readAsync=function(e,r,i){var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=function(){200==n.status||0==n.status&&n.response?r(n.response):i()},n.onerror=i,n.send(null)},void 0!==arguments&&(Module.arguments=arguments),"undefined"!=typeof console)Module.print||(Module.print=function(e){console.log(e)}),Module.printErr||(Module.printErr=function(e){console.warn(e)});else{var TRY_USE_DUMP=!1;Module.print||(Module.print=TRY_USE_DUMP&&"undefined"!=typeof dump?function(e){dump(e)}:function(e){})}ENVIRONMENT_IS_WORKER&&(Module.load=importScripts),void 0===Module.setWindowTitle&&(Module.setWindowTitle=function(e){document.title=e})}function globalEval(e){eval.call(null,e)}for(var key in!Module.load&&Module.read&&(Module.load=function(e){globalEval(Module.read(e))}),Module.print||(Module.print=function(){}),Module.printErr||(Module.printErr=Module.print),Module.arguments||(Module.arguments=[]),Module.thisProgram||(Module.thisProgram="./this.program"),Module.quit||(Module.quit=function(e,r){throw r}),Module.print=Module.print,Module.printErr=Module.printErr,Module.preRun=[],Module.postRun=[],moduleOverrides)moduleOverrides.hasOwnProperty(key)&&(Module[key]=moduleOverrides[key]);moduleOverrides=void 0;var Runtime={setTempRet0:function(e){return tempRet0=e,e},getTempRet0:function(){return tempRet0},stackSave:function(){return STACKTOP},stackRestore:function(e){STACKTOP=e},getNativeTypeSize:function(e){switch(e){case"i1":case"i8":return 1;case"i16":return 2;case"i32":case"float":return 4;case"i64":case"double":return 8;default:if("*"===e[e.length-1])return Runtime.QUANTUM_SIZE;if("i"===e[0]){var r=parseInt(e.substr(1));return assert(r%8==0),r/8}return 0}},getNativeFieldSize:function(e){return Math.max(Runtime.getNativeTypeSize(e),Runtime.QUANTUM_SIZE)},STACK_ALIGN:16,prepVararg:function(e,r){return"double"===r||"i64"===r?7&e&&(assert(4==(7&e)),e+=4):assert(0==(3&e)),e},getAlignSize:function(e,r,i){return i||"i64"!=e&&"double"!=e?e?Math.min(r||(e?Runtime.getNativeFieldSize(e):0),Runtime.QUANTUM_SIZE):Math.min(r,8):8},dynCall:function(e,r,i){return i&&i.length?Module["dynCall_"+e].apply(null,[r].concat(i)):Module["dynCall_"+e].call(null,r)},functionPointers:[],addFunction:function(e){for(var r=0;r<Runtime.functionPointers.length;r++)if(!Runtime.functionPointers[r])return Runtime.functionPointers[r]=e,2*(1+r);throw"Finished up all reserved function pointers. Use a higher value for RESERVED_FUNCTION_POINTERS."},removeFunction:function(e){Runtime.functionPointers[(e-2)/2]=null},warnOnce:function(e){Runtime.warnOnce.shown||(Runtime.warnOnce.shown={}),Runtime.warnOnce.shown[e]||(Runtime.warnOnce.shown[e]=1,Module.printErr(e))},funcWrappers:{},getFuncWrapper:function(e,r){assert(r),Runtime.funcWrappers[r]||(Runtime.funcWrappers[r]={});var i=Runtime.funcWrappers[r];return i[e]||(1===r.length?i[e]=function(){return Runtime.dynCall(r,e)}:2===r.length?i[e]=function(i){return Runtime.dynCall(r,e,[i])}:i[e]=function(){return Runtime.dynCall(r,e,Array.prototype.slice.call(arguments))}),i[e]},getCompilerSetting:function(e){throw"You must build with -s RETAIN_COMPILER_SETTINGS=1 for Runtime.getCompilerSetting or emscripten_get_compiler_setting to work"},stackAlloc:function(e){var r=STACKTOP;return STACKTOP=(STACKTOP=STACKTOP+e|0)+15&-16,r},staticAlloc:function(e){var r=STATICTOP;return STATICTOP=(STATICTOP=STATICTOP+e|0)+15&-16,r},dynamicAlloc:function(e){var r=HEAP32[DYNAMICTOP_PTR>>2],i=-16&(r+e+15|0);if((HEAP32[DYNAMICTOP_PTR>>2]=i,i>=TOTAL_MEMORY)&&!enlargeMemory())return HEAP32[DYNAMICTOP_PTR>>2]=r,0;return r},alignMemory:function(e,r){return e=Math.ceil(e/(r||16))*(r||16)},makeBigInt:function(e,r,i){return i?+(e>>>0)+4294967296*+(r>>>0):+(e>>>0)+4294967296*+(0|r)},GLOBAL_BASE:8,QUANTUM_SIZE:4,__dummy__:0};Module.Runtime=Runtime;var ABORT=0,cwrap,ccall;function assert(e,r){e||abort("Assertion failed: "+r)}function getCFunc(ident){var func=Module["_"+ident];if(!func)try{func=eval("_"+ident)}catch(e){}return assert(func,"Cannot call unknown function "+ident+" (perhaps LLVM optimizations or closure removed it?)"),func}function setValue(e,r,i,n){switch("*"===(i=i||"i8").charAt(i.length-1)&&(i="i32"),i){case"i1":case"i8":HEAP8[e>>0]=r;break;case"i16":HEAP16[e>>1]=r;break;case"i32":HEAP32[e>>2]=r;break;case"i64":tempI64=[r>>>0,(tempDouble=r,+Math_abs(tempDouble)>=1?tempDouble>0?(0|Math_min(+Math_floor(tempDouble/4294967296),4294967295))>>>0:~~+Math_ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[e>>2]=tempI64[0],HEAP32[e+4>>2]=tempI64[1];break;case"float":HEAPF32[e>>2]=r;break;case"double":HEAPF64[e>>3]=r;break;default:abort("invalid type for setValue: "+i)}}function getValue(e,r,i){switch("*"===(r=r||"i8").charAt(r.length-1)&&(r="i32"),r){case"i1":case"i8":return HEAP8[e>>0];case"i16":return HEAP16[e>>1];case"i32":case"i64":return HEAP32[e>>2];case"float":return HEAPF32[e>>2];case"double":return HEAPF64[e>>3];default:abort("invalid type for setValue: "+r)}return null}(function(){var JSfuncs={stackSave:function(){Runtime.stackSave()},stackRestore:function(){Runtime.stackRestore()},arrayToC:function(e){var r=Runtime.stackAlloc(e.length);return writeArrayToMemory(e,r),r},stringToC:function(e){var r=0;if(null!=e&&0!==e){var i=1+(e.length<<2);stringToUTF8(e,r=Runtime.stackAlloc(i),i)}return r}},toC={string:JSfuncs.stringToC,array:JSfuncs.arrayToC};ccall=function(e,r,i,n,t){var o=getCFunc(e),a=[],u=0;if(n)for(var f=0;f<n.length;f++){var l=toC[i[f]];l?(0===u&&(u=Runtime.stackSave()),a[f]=l(n[f])):a[f]=n[f]}var c=o.apply(null,a);if("string"===r&&(c=Pointer_stringify(c)),0!==u){if(t&&t.async)return void EmterpreterAsync.asyncFinalizers.push((function(){Runtime.stackRestore(u)}));Runtime.stackRestore(u)}return c};var sourceRegex=/^function\s*[a-zA-Z$_0-9]*\s*\(([^)]*)\)\s*{\s*([^*]*?)[\s;]*(?:return\s*(.*?)[;\s]*)?}$/;function parseJSFunc(e){var r=e.toString().match(sourceRegex).slice(1);return{arguments:r[0],body:r[1],returnValue:r[2]}}var JSsource=null;function ensureJSsource(){if(!JSsource)for(var e in JSsource={},JSfuncs)JSfuncs.hasOwnProperty(e)&&(JSsource[e]=parseJSFunc(JSfuncs[e]))}cwrap=function cwrap(ident,returnType,argTypes){argTypes=argTypes||[];var cfunc=getCFunc(ident),numericArgs=argTypes.every((function(e){return"number"===e})),numericRet="string"!==returnType;if(numericRet&&numericArgs)return cfunc;var argNames=argTypes.map((function(e,r){return"$"+r})),funcstr="(function("+argNames.join(",")+") {",nargs=argTypes.length;if(!numericArgs){ensureJSsource(),funcstr+="var stack = "+JSsource.stackSave.body+";";for(var i=0;i<nargs;i++){var arg=argNames[i],type=argTypes[i];if("number"!==type){var convertCode=JSsource[type+"ToC"];funcstr+="var "+convertCode.arguments+" = "+arg+";",funcstr+=convertCode.body+";",funcstr+=arg+"=("+convertCode.returnValue+");"}}}var cfuncname=parseJSFunc((function(){return cfunc})).returnValue;if(funcstr+="var ret = "+cfuncname+"("+argNames.join(",")+");",!numericRet){var strgfy=parseJSFunc((function(){return Pointer_stringify})).returnValue;funcstr+="ret = "+strgfy+"(ret);"}return numericArgs||(ensureJSsource(),funcstr+=JSsource.stackRestore.body.replace("()","(stack)")+";"),funcstr+="return ret})",eval(funcstr)}})(),Module.ccall=ccall,Module.cwrap=cwrap,Module.setValue=setValue,Module.getValue=getValue;var ALLOC_NORMAL=0,ALLOC_STACK=1,ALLOC_STATIC=2,ALLOC_DYNAMIC=3,ALLOC_NONE=4;function allocate(e,r,i,n){var t,o;"number"==typeof e?(t=!0,o=e):(t=!1,o=e.length);var a,u="string"==typeof r?r:null;if(a=i==ALLOC_NONE?n:["function"==typeof _malloc?_malloc:Runtime.staticAlloc,Runtime.stackAlloc,Runtime.staticAlloc,Runtime.dynamicAlloc][void 0===i?ALLOC_STATIC:i](Math.max(o,u?1:r.length)),t){var f;n=a;for(assert(0==(3&a)),f=a+(-4&o);n<f;n+=4)HEAP32[n>>2]=0;for(f=a+o;n<f;)HEAP8[n++>>0]=0;return a}if("i8"===u)return e.subarray||e.slice?HEAPU8.set(e,a):HEAPU8.set(new Uint8Array(e),a),a;for(var l,c,s,d=0;d<o;){var _=e[d];"function"==typeof _&&(_=Runtime.getFunctionIndex(_)),0!==(l=u||r[d])?("i64"==l&&(l="i32"),setValue(a+d,_,l),s!==l&&(c=Runtime.getNativeTypeSize(l),s=l),d+=c):d++}return a}function getMemory(e){return staticSealed?runtimeInitialized?_malloc(e):Runtime.dynamicAlloc(e):Runtime.staticAlloc(e)}function Pointer_stringify(e,r){if(0===r||!e)return"";for(var i,n=0,t=0;n|=i=HEAPU8[e+t>>0],(0!=i||r)&&(t++,!r||t!=r););r||(r=t);var o="";if(n<128){for(var a,u=1024;r>0;)a=String.fromCharCode.apply(String,HEAPU8.subarray(e,e+Math.min(r,u))),o=o?o+a:a,e+=u,r-=u;return o}return Module.UTF8ToString(e)}function AsciiToString(e){for(var r="";;){var i=HEAP8[e++>>0];if(!i)return r;r+=String.fromCharCode(i)}}function stringToAscii(e,r){return writeAsciiToMemory(e,r,!1)}Module.ALLOC_NORMAL=ALLOC_NORMAL,Module.ALLOC_STACK=ALLOC_STACK,Module.ALLOC_STATIC=ALLOC_STATIC,Module.ALLOC_DYNAMIC=ALLOC_DYNAMIC,Module.ALLOC_NONE=ALLOC_NONE,Module.allocate=allocate,Module.getMemory=getMemory,Module.Pointer_stringify=Pointer_stringify,Module.AsciiToString=AsciiToString,Module.stringToAscii=stringToAscii;var UTF8Decoder="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function UTF8ArrayToString(e,r){for(var i=r;e[i];)++i;if(i-r>16&&e.subarray&&UTF8Decoder)return UTF8Decoder.decode(e.subarray(r,i));for(var n,t,o,a,u,f="";;){if(!(n=e[r++]))return f;if(128&n)if(t=63&e[r++],192!=(224&n))if(o=63&e[r++],224==(240&n)?n=(15&n)<<12|t<<6|o:(a=63&e[r++],240==(248&n)?n=(7&n)<<18|t<<12|o<<6|a:(u=63&e[r++],n=248==(252&n)?(3&n)<<24|t<<18|o<<12|a<<6|u:(1&n)<<30|t<<24|o<<18|a<<12|u<<6|63&e[r++])),n<65536)f+=String.fromCharCode(n);else{var l=n-65536;f+=String.fromCharCode(55296|l>>10,56320|1023&l)}else f+=String.fromCharCode((31&n)<<6|t);else f+=String.fromCharCode(n)}}function UTF8ToString(e){return UTF8ArrayToString(HEAPU8,e)}function stringToUTF8Array(e,r,i,n){if(!(n>0))return 0;for(var t=i,o=i+n-1,a=0;a<e.length;++a){var u=e.charCodeAt(a);if(u>=55296&&u<=57343&&(u=65536+((1023&u)<<10)|1023&e.charCodeAt(++a)),u<=127){if(i>=o)break;r[i++]=u}else if(u<=2047){if(i+1>=o)break;r[i++]=192|u>>6,r[i++]=128|63&u}else if(u<=65535){if(i+2>=o)break;r[i++]=224|u>>12,r[i++]=128|u>>6&63,r[i++]=128|63&u}else if(u<=2097151){if(i+3>=o)break;r[i++]=240|u>>18,r[i++]=128|u>>12&63,r[i++]=128|u>>6&63,r[i++]=128|63&u}else if(u<=67108863){if(i+4>=o)break;r[i++]=248|u>>24,r[i++]=128|u>>18&63,r[i++]=128|u>>12&63,r[i++]=128|u>>6&63,r[i++]=128|63&u}else{if(i+5>=o)break;r[i++]=252|u>>30,r[i++]=128|u>>24&63,r[i++]=128|u>>18&63,r[i++]=128|u>>12&63,r[i++]=128|u>>6&63,r[i++]=128|63&u}}return r[i]=0,i-t}function stringToUTF8(e,r,i){return stringToUTF8Array(e,HEAPU8,r,i)}function lengthBytesUTF8(e){for(var r=0,i=0;i<e.length;++i){var n=e.charCodeAt(i);n>=55296&&n<=57343&&(n=65536+((1023&n)<<10)|1023&e.charCodeAt(++i)),n<=127?++r:r+=n<=2047?2:n<=65535?3:n<=2097151?4:n<=67108863?5:6}return r}function demangle(e){var r=Module.___cxa_demangle||Module.__cxa_demangle;if(r){try{var i=e.substr(1),n=lengthBytesUTF8(i)+1,t=_malloc(n);stringToUTF8(i,t,n);var o=_malloc(4),a=r(t,0,0,o);if(0===getValue(o,"i32")&&a)return Pointer_stringify(a)}catch(e){}finally{t&&_free(t),o&&_free(o),a&&_free(a)}return e}return Runtime.warnOnce("warning: build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling"),e}function demangleAll(e){return e.replace(/__Z[\w\d_]+/g,(function(e){var r=demangle(e);return e===r?e:e+" ["+r+"]"}))}function jsStackTrace(){var e=new Error;if(!e.stack){try{throw new Error(0)}catch(r){e=r}if(!e.stack)return"(no stack trace available)"}return e.stack.toString()}function stackTrace(){var e=jsStackTrace();return Module.extraStackTrace&&(e+="\n"+Module.extraStackTrace()),demangleAll(e)}Module.UTF8ArrayToString=UTF8ArrayToString,Module.UTF8ToString=UTF8ToString,Module.stringToUTF8Array=stringToUTF8Array,Module.stringToUTF8=stringToUTF8,Module.lengthBytesUTF8=lengthBytesUTF8,"undefined"!=typeof TextDecoder&&new TextDecoder("utf-16le"),Module.stackTrace=stackTrace;var WASM_PAGE_SIZE=65536,ASMJS_PAGE_SIZE=16777216,MIN_TOTAL_MEMORY=16777216,HEAP,buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64,STATIC_BASE,STATICTOP,staticSealed,STACK_BASE,STACKTOP,STACK_MAX,DYNAMIC_BASE,DYNAMICTOP_PTR,byteLength;function alignUp(e,r){return e%r>0&&(e+=r-e%r),e}function updateGlobalBuffer(e){Module.buffer=buffer=e}function updateGlobalBufferViews(){Module.HEAP8=HEAP8=new Int8Array(buffer),Module.HEAP16=HEAP16=new Int16Array(buffer),Module.HEAP32=HEAP32=new Int32Array(buffer),Module.HEAPU8=HEAPU8=new Uint8Array(buffer),Module.HEAPU16=HEAPU16=new Uint16Array(buffer),Module.HEAPU32=HEAPU32=new Uint32Array(buffer),Module.HEAPF32=HEAPF32=new Float32Array(buffer),Module.HEAPF64=HEAPF64=new Float64Array(buffer)}function abortOnCannotGrowMemory(){abort("Cannot enlarge memory arrays. Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value "+TOTAL_MEMORY+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime but prevents some optimizations, (3) set Module.TOTAL_MEMORY to a higher value before the program runs, or (4) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ")}function enlargeMemory(){var e=Module.usingWasm?WASM_PAGE_SIZE:ASMJS_PAGE_SIZE,r=2147483648-e;if(HEAP32[DYNAMICTOP_PTR>>2]>r)return!1;var i=TOTAL_MEMORY;for(TOTAL_MEMORY=Math.max(TOTAL_MEMORY,MIN_TOTAL_MEMORY);TOTAL_MEMORY<HEAP32[DYNAMICTOP_PTR>>2];)TOTAL_MEMORY=TOTAL_MEMORY<=536870912?alignUp(2*TOTAL_MEMORY,e):Math.min(alignUp((3*TOTAL_MEMORY+2147483648)/4,e),r);var n=Module.reallocBuffer(TOTAL_MEMORY);return n&&n.byteLength==TOTAL_MEMORY?(updateGlobalBuffer(n),updateGlobalBufferViews(),!0):(TOTAL_MEMORY=i,!1)}STATIC_BASE=STATICTOP=STACK_BASE=STACKTOP=STACK_MAX=DYNAMIC_BASE=DYNAMICTOP_PTR=0,staticSealed=!1,Module.reallocBuffer||(Module.reallocBuffer=function(e){var r;try{if(ArrayBuffer.transfer)r=ArrayBuffer.transfer(buffer,e);else{var i=HEAP8;r=new ArrayBuffer(e),new Int8Array(r).set(i)}}catch(e){return!1}return!!_emscripten_replace_memory(r)&&r});try{byteLength=Function.prototype.call.bind(Object.getOwnPropertyDescriptor(ArrayBuffer.prototype,"byteLength").get),byteLength(new ArrayBuffer(4))}catch(e){byteLength=function(e){return e.byteLength}}var TOTAL_STACK=Module.TOTAL_STACK||5242880,TOTAL_MEMORY=Module.TOTAL_MEMORY||16777216;function getTotalMemory(){return TOTAL_MEMORY}if(TOTAL_MEMORY<TOTAL_STACK&&Module.printErr("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+TOTAL_MEMORY+"! (TOTAL_STACK="+TOTAL_STACK+")"),buffer=Module.buffer?Module.buffer:new ArrayBuffer(TOTAL_MEMORY),updateGlobalBufferViews(),HEAP32[0]=1668509029,HEAP16[1]=25459,115!==HEAPU8[2]||99!==HEAPU8[3])throw"Runtime error: expected the system to be little-endian!";function callRuntimeCallbacks(e){for(;e.length>0;){var r=e.shift();if("function"!=typeof r){var i=r.func;"number"==typeof i?void 0===r.arg?Module.dynCall_v(i):Module.dynCall_vi(i,r.arg):i(void 0===r.arg?null:r.arg)}else r()}}Module.HEAP=HEAP,Module.buffer=buffer,Module.HEAP8=HEAP8,Module.HEAP16=HEAP16,Module.HEAP32=HEAP32,Module.HEAPU8=HEAPU8,Module.HEAPU16=HEAPU16,Module.HEAPU32=HEAPU32,Module.HEAPF32=HEAPF32,Module.HEAPF64=HEAPF64;var __ATPRERUN__=[],__ATINIT__=[],__ATMAIN__=[],__ATEXIT__=[],__ATPOSTRUN__=[],runtimeInitialized=!1;function preRun(){if(Module.preRun)for("function"==typeof Module.preRun&&(Module.preRun=[Module.preRun]);Module.preRun.length;)addOnPreRun(Module.preRun.shift());callRuntimeCallbacks(__ATPRERUN__)}function ensureInitRuntime(){runtimeInitialized||(runtimeInitialized=!0,callRuntimeCallbacks(__ATINIT__))}function preMain(){callRuntimeCallbacks(__ATMAIN__)}function exitRuntime(){callRuntimeCallbacks(__ATEXIT__)}function postRun(){if(Module.postRun)for("function"==typeof Module.postRun&&(Module.postRun=[Module.postRun]);Module.postRun.length;)addOnPostRun(Module.postRun.shift());callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(e){__ATPRERUN__.unshift(e)}function addOnInit(e){__ATINIT__.unshift(e)}function addOnPreMain(e){__ATMAIN__.unshift(e)}function addOnExit(e){__ATEXIT__.unshift(e)}function addOnPostRun(e){__ATPOSTRUN__.unshift(e)}function intArrayFromString(e,r,i){var n=i>0?i:lengthBytesUTF8(e)+1,t=new Array(n),o=stringToUTF8Array(e,t,0,t.length);return r&&(t.length=o),t}function intArrayToString(e){for(var r=[],i=0;i<e.length;i++){var n=e[i];n>255&&(n&=255),r.push(String.fromCharCode(n))}return r.join("")}function writeStringToMemory(e,r,i){var n,t;Runtime.warnOnce("writeStringToMemory is deprecated and should not be called! Use stringToUTF8() instead!"),i&&(t=r+lengthBytesUTF8(e),n=HEAP8[t]),stringToUTF8(e,r,1/0),i&&(HEAP8[t]=n)}function writeArrayToMemory(e,r){HEAP8.set(e,r)}function writeAsciiToMemory(e,r,i){for(var n=0;n<e.length;++n)HEAP8[r++>>0]=e.charCodeAt(n);i||(HEAP8[r>>0]=0)}Module.addOnPreRun=addOnPreRun,Module.addOnInit=addOnInit,Module.addOnPreMain=addOnPreMain,Module.addOnExit=addOnExit,Module.addOnPostRun=addOnPostRun,Module.intArrayFromString=intArrayFromString,Module.intArrayToString=intArrayToString,Module.writeStringToMemory=writeStringToMemory,Module.writeArrayToMemory=writeArrayToMemory,Module.writeAsciiToMemory=writeAsciiToMemory,Math.imul&&-5===Math.imul(4294967295,5)||(Math.imul=function(e,r){var i=65535&e,n=65535&r;return i*n+((e>>>16)*n+i*(r>>>16)<<16)|0}),Math.imul=Math.imul,Math.clz32||(Math.clz32=function(e){e>>>=0;for(var r=0;r<32;r++)if(e&1<<31-r)return r;return 32}),Math.clz32=Math.clz32,Math.trunc||(Math.trunc=function(e){return e<0?Math.ceil(e):Math.floor(e)}),Math.trunc=Math.trunc;var Math_abs=Math.abs,Math_ceil=Math.ceil,Math_floor=Math.floor,Math_min=Math.min,runDependencies=0,dependenciesFulfilled=null;function addRunDependency(e){runDependencies++,Module.monitorRunDependencies&&Module.monitorRunDependencies(runDependencies)}function removeRunDependency(e){if(runDependencies--,Module.monitorRunDependencies&&Module.monitorRunDependencies(runDependencies),0==runDependencies&&dependenciesFulfilled){var r=dependenciesFulfilled;dependenciesFulfilled=null,r()}}Module.addRunDependency=addRunDependency,Module.removeRunDependency=removeRunDependency,Module.preloadedImages={},Module.preloadedAudios={},STATIC_BASE=Runtime.GLOBAL_BASE,STATICTOP=STATIC_BASE+6192,__ATINIT__.push(),allocate([228,2,0,0,81,16,0,0,12,3,0,0,177,16,0,0,32,0,0,0,0,0,0,0,12,3,0,0,94,16,0,0,48,0,0,0,0,0,0,0,228,2,0,0,127,16,0,0,12,3,0,0,140,16,0,0,16,0,0,0,0,0,0,0,12,3,0,0,183,17,0,0,32,0,0,0,0,0,0,0,12,3,0,0,147,17,0,0,72,0,0,0,0,0,0,0,108,0,0,0,5,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,32,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,248,19,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,224,1,0,0,5,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,3,0,0,0,2,0,0,0,40,20,0,0,0,4,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,10,255,255,255,255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,16,0,0,0,1,0,0,0,2,0,0,0,3,0,0,0,4,0,0,0,5,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,56,0,0,0,1,0,0,0,5,0,0,0,3,0,0,0,4,0,0,0,5,0,0,0,2,0,0,0,2,0,0,0,2,0,0,0,37,115,40,37,117,41,58,32,65,115,115,101,114,116,105,111,110,32,102,97,105,108,117,114,101,58,32,34,37,115,34,10,0,109,95,115,105,122,101,32,60,61,32,109,95,99,97,112,97,99,105,116,121,0,46,47,105,110,99,92,99,114,110,95,100,101,99,111,109,112,46,104,0,109,105,110,95,110,101,119,95,99,97,112,97,99,105,116,121,32,60,32,40,48,120,55,70,70,70,48,48,48,48,85,32,47,32,101,108,101,109,101,110,116,95,115,105,122,101,41,0,110,101,119,95,99,97,112,97,99,105,116,121,32,38,38,32,40,110,101,119,95,99,97,112,97,99,105,116,121,32,62,32,109,95,99,97,112,97,99,105,116,121,41,0,110,117,109,95,99,111,100,101,115,91,99,93,0,115,111,114,116,101,100,95,112,111,115,32,60,32,116,111,116,97,108,95,117,115,101,100,95,115,121,109,115,0,112,67,111,100,101,115,105,122,101,115,91,115,121,109,95,105,110,100,101,120,93,32,61,61,32,99,111,100,101,115,105,122,101,0,116,32,60,32,40,49,85,32,60,60,32,116,97,98,108,101,95,98,105,116,115,41,0,109,95,108,111,111,107,117,112,91,116,93,32,61,61,32,99,85,73,78,84,51,50,95,77,65,88,0,99,114,110,100,95,109,97,108,108,111,99,58,32,115,105,122,101,32,116,111,111,32,98,105,103,0,99,114,110,100,95,109,97,108,108,111,99,58,32,111,117,116,32,111,102,32,109,101,109,111,114,121,0,40,40,117,105,110,116,51,50,41,112,95,110,101,119,32,38,32,40,67,82,78,68,95,77,73,78,95,65,76,76,79,67,95,65,76,73,71,78,77,69,78,84,32,45,32,49,41,41,32,61,61,32,48,0,99,114,110,100,95,114,101,97,108,108,111,99,58,32,98,97,100,32,112,116,114,0,99,114,110,100,95,102,114,101,101,58,32,98,97,100,32,112,116,114,0,102,97,108,115,101,0,40,116,111,116,97,108,95,115,121,109,115,32,62,61,32,49,41,32,38,38,32,40,116,111,116,97,108,95,115,121,109,115,32,60,61,32,112,114,101,102,105,120,95,99,111,100,105,110,103,58,58,99,77,97,120,83,117,112,112,111,114,116,101,100,83,121,109,115,41,0,17,18,19,20,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15,16,48,0,110,117,109,95,98,105,116,115,32,60,61,32,51,50,85,0,109,95,98,105,116,95,99,111,117,110,116,32,60,61,32,99,66,105,116,66,117,102,83,105,122,101,0,116,32,33,61,32,99,85,73,78,84,51,50,95,77,65,88,0,109,111,100,101,108,46,109,95,99,111,100,101,95,115,105,122,101,115,91,115,121,109,93,32,61,61,32,108,101,110,0,0,2,3,1,0,2,3,4,5,6,7,1,40,108,101,110,32,62,61,32,49,41,32,38,38,32,40,108,101,110,32,60,61,32,99,77,97,120,69,120,112,101,99,116,101,100,67,111,100,101,83,105,122,101,41,0,105,32,60,32,109,95,115,105,122,101,0,110,101,120,116,95,108,101,118,101,108,95,111,102,115,32,62,32,99,117,114,95,108,101,118,101,108,95,111,102,115,0,1,2,2,3,3,3,3,4,0,0,0,0,0,0,1,1,0,1,0,1,0,0,1,2,1,2,0,0,0,1,0,2,1,0,2,0,0,1,2,3,110,117,109,32,38,38,32,40,110,117,109,32,61,61,32,126,110,117,109,95,99,104,101,99,107,41,0,17,0,10,0,17,17,17,0,0,0,0,5,0,0,0,0,0,0,9,0,0,0,0,11,0,0,0,0,0,0,0,0,17,0,15,10,17,17,17,3,10,7,0,1,19,9,11,11,0,0,9,6,11,0,0,11,0,6,17,0,0,0,17,17,17,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,11,0,0,0,0,0,0,0,0,17,0,10,10,17,17,17,0,10,0,0,2,0,9,11,0,0,0,9,0,11,0,0,11,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,12,0,0,0,0,9,12,0,0,0,0,0,12,0,0,12,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,14,0,0,0,0,0,0,0,0,0,0,0,13,0,0,0,4,13,0,0,0,0,9,14,0,0,0,0,0,14,0,0,14,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16,0,0,0,0,0,0,0,0,0,0,0,15,0,0,0,0,15,0,0,0,0,9,16,0,0,0,0,0,16,0,0,16,0,0,18,0,0,0,18,18,18,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,18,0,0,0,18,18,18,0,0,0,0,0,0,9,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,11,0,0,0,0,0,0,0,0,0,0,0,10,0,0,0,0,10,0,0,0,0,9,11,0,0,0,0,0,11,0,0,11,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,12,0,0,0,0,9,12,0,0,0,0,0,12,0,0,12,0,0,45,43,32,32,32,48,88,48,120,0,40,110,117,108,108,41,0,45,48,88,43,48,88,32,48,88,45,48,120,43,48,120,32,48,120,0,105,110,102,0,73,78,70,0,110,97,110,0,78,65,78,0,48,49,50,51,52,53,54,55,56,57,65,66,67,68,69,70,46,0,84,33,34,25,13,1,2,3,17,75,28,12,16,4,11,29,18,30,39,104,110,111,112,113,98,32,5,6,15,19,20,21,26,8,22,7,40,36,23,24,9,10,14,27,31,37,35,131,130,125,38,42,43,60,61,62,63,67,71,74,77,88,89,90,91,92,93,94,95,96,97,99,100,101,102,103,105,106,107,108,114,115,116,121,122,123,124,0,73,108,108,101,103,97,108,32,98,121,116,101,32,115,101,113,117,101,110,99,101,0,68,111,109,97,105,110,32,101,114,114,111,114,0,82,101,115,117,108,116,32,110,111,116,32,114,101,112,114,101,115,101,110,116,97,98,108,101,0,78,111,116,32,97,32,116,116,121,0,80,101,114,109,105,115,115,105,111,110,32,100,101,110,105,101,100,0,79,112,101,114,97,116,105,111,110,32,110,111,116,32,112,101,114,109,105,116,116,101,100,0,78,111,32,115,117,99,104,32,102,105,108,101,32,111,114,32,100,105,114,101,99,116,111,114,121,0,78,111,32,115,117,99,104,32,112,114,111,99,101,115,115,0,70,105,108,101,32,101,120,105,115,116,115,0,86,97,108,117,101,32,116,111,111,32,108,97,114,103,101,32,102,111,114,32,100,97,116,97,32,116,121,112,101,0,78,111,32,115,112,97,99,101,32,108,101,102,116,32,111,110,32,100,101,118,105,99,101,0,79,117,116,32,111,102,32,109,101,109,111,114,121,0,82,101,115,111,117,114,99,101,32,98,117,115,121,0,73,110,116,101,114,114,117,112,116,101,100,32,115,121,115,116,101,109,32,99,97,108,108,0,82,101,115,111,117,114,99,101,32,116,101,109,112,111,114,97,114,105,108,121,32,117,110,97,118,97,105,108,97,98,108,101,0,73,110,118,97,108,105,100,32,115,101,101,107,0,67,114,111,115,115,45,100,101,118,105,99,101,32,108,105,110,107,0,82,101,97,100,45,111,110,108,121,32,102,105,108,101,32,115,121,115,116,101,109,0,68,105,114,101,99,116,111,114,121,32,110,111,116,32,101,109,112,116,121,0,67,111,110,110,101,99,116,105,111,110,32,114,101,115,101,116,32,98,121,32,112,101,101,114,0,79,112,101,114,97,116,105,111,110,32,116,105,109,101,100,32,111,117,116,0,67,111,110,110,101,99,116,105,111,110,32,114,101,102,117,115,101,100,0,72,111,115,116,32,105,115,32,100,111,119,110,0,72,111,115,116,32,105,115,32,117,110,114,101,97,99,104,97,98,108,101,0,65,100,100,114,101,115,115,32,105,110,32,117,115,101,0,66,114,111,107,101,110,32,112,105,112,101,0,73,47,79,32,101,114,114,111,114,0,78,111,32,115,117,99,104,32,100,101,118,105,99,101,32,111,114,32,97,100,100,114,101,115,115,0,66,108,111,99,107,32,100,101,118,105,99,101,32,114,101,113,117,105,114,101,100,0,78,111,32,115,117,99,104,32,100,101,118,105,99,101,0,78,111,116,32,97,32,100,105,114,101,99,116,111,114,121,0,73,115,32,97,32,100,105,114,101,99,116,111,114,121,0,84,101,120,116,32,102,105,108,101,32,98,117,115,121,0,69,120,101,99,32,102,111,114,109,97,116,32,101,114,114,111,114,0,73,110,118,97,108,105,100,32,97,114,103,117,109,101,110,116,0,65,114,103,117,109,101,110,116,32,108,105,115,116,32,116,111,111,32,108,111,110,103,0,83,121,109,98,111,108,105,99,32,108,105,110,107,32,108,111,111,112,0,70,105,108,101,110,97,109,101,32,116,111,111,32,108,111,110,103,0,84,111,111,32,109,97,110,121,32,111,112,101,110,32,102,105,108,101,115,32,105,110,32,115,121,115,116,101,109,0,78,111,32,102,105,108,101,32,100,101,115,99,114,105,112,116,111,114,115,32,97,118,97,105,108,97,98,108,101,0,66,97,100,32,102,105,108,101,32,100,101,115,99,114,105,112,116,111,114,0,78,111,32,99,104,105,108,100,32,112,114,111,99,101,115,115,0,66,97,100,32,97,100,100,114,101,115,115,0,70,105,108,101,32,116,111,111,32,108,97,114,103,101,0,84,111,111,32,109,97,110,121,32,108,105,110,107,115,0,78,111,32,108,111,99,107,115,32,97,118,97,105,108,97,98,108,101,0,82,101,115,111,117,114,99,101,32,100,101,97,100,108,111,99,107,32,119,111,117,108,100,32,111,99,99,117,114,0,83,116,97,116,101,32,110,111,116,32,114,101,99,111,118,101,114,97,98,108,101,0,80,114,101,118,105,111,117,115,32,111,119,110,101,114,32,100,105,101,100,0,79,112,101,114,97,116,105,111,110,32,99,97,110,99,101,108,101,100,0,70,117,110,99,116,105,111,110,32,110,111,116,32,105,109,112,108,101,109,101,110,116,101,100,0,78,111,32,109,101,115,115,97,103,101,32,111,102,32,100,101,115,105,114,101,100,32,116,121,112,101,0,73,100,101,110,116,105,102,105,101,114,32,114,101,109,111,118,101,100,0,68,101,118,105,99,101,32,110,111,116,32,97,32,115,116,114,101,97,109,0,78,111,32,100,97,116,97,32,97,118,97,105,108,97,98,108,101,0,68,101,118,105,99,101,32,116,105,109,101,111,117,116,0,79,117,116,32,111,102,32,115,116,114,101,97,109,115,32,114,101,115,111,117,114,99,101,115,0,76,105,110,107,32,104,97,115,32,98,101,101,110,32,115,101,118,101,114,101,100,0,80,114,111,116,111,99,111,108,32,101,114,114,111,114,0,66,97,100,32,109,101,115,115,97,103,101,0,70,105,108,101,32,100,101,115,99,114,105,112,116,111,114,32,105,110,32,98,97,100,32,115,116,97,116,101,0,78,111,116,32,97,32,115,111,99,107,101,116,0,68,101,115,116,105,110,97,116,105,111,110,32,97,100,100,114,101,115,115,32,114,101,113,117,105,114,101,100,0,77,101,115,115,97,103,101,32,116,111,111,32,108,97,114,103,101,0,80,114,111,116,111,99,111,108,32,119,114,111,110,103,32,116,121,112,101,32,102,111,114,32,115,111,99,107,101,116,0,80,114,111,116,111,99,111,108,32,110,111,116,32,97,118,97,105,108,97,98,108,101,0,80,114,111,116,111,99,111,108,32,110,111,116,32,115,117,112,112,111,114,116,101,100,0,83,111,99,107,101,116,32,116,121,112,101,32,110,111,116,32,115,117,112,112,111,114,116,101,100,0,78,111,116,32,115,117,112,112,111,114,116,101,100,0,80,114,111,116,111,99,111,108,32,102,97,109,105,108,121,32,110,111,116,32,115,117,112,112,111,114,116,101,100,0,65,100,100,114,101,115,115,32,102,97,109,105,108,121,32,110,111,116,32,115,117,112,112,111,114,116,101,100,32,98,121,32,112,114,111,116,111,99,111,108,0,65,100,100,114,101,115,115,32,110,111,116,32,97,118,97,105,108,97,98,108,101,0,78,101,116,119,111,114,107,32,105,115,32,100,111,119,110,0,78,101,116,119,111,114,107,32,117,110,114,101,97,99,104,97,98,108,101,0,67,111,110,110,101,99,116,105,111,110,32,114,101,115,101,116,32,98,121,32,110,101,116,119,111,114,107,0,67,111,110,110,101,99,116,105,111,110,32,97,98,111,114,116,101,100,0,78,111,32,98,117,102,102,101,114,32,115,112,97,99,101,32,97,118,97,105,108,97,98,108,101,0,83,111,99,107,101,116,32,105,115,32,99,111,110,110,101,99,116,101,100,0,83,111,99,107,101,116,32,110,111,116,32,99,111,110,110,101,99,116,101,100,0,67,97,110,110,111,116,32,115,101,110,100,32,97,102,116,101,114,32,115,111,99,107,101,116,32,115,104,117,116,100,111,119,110,0,79,112,101,114,97,116,105,111,110,32,97,108,114,101,97,100,121,32,105,110,32,112,114,111,103,114,101,115,115,0,79,112,101,114,97,116,105,111,110,32,105,110,32,112,114,111,103,114,101,115,115,0,83,116,97,108,101,32,102,105,108,101,32,104,97,110,100,108,101,0,82,101,109,111,116,101,32,73,47,79,32,101,114,114,111,114,0,81,117,111,116,97,32,101,120,99,101,101,100,101,100,0,78,111,32,109,101,100,105,117,109,32,102,111,117,110,100,0,87,114,111,110,103,32,109,101,100,105,117,109,32,116,121,112,101,0,78,111,32,101,114,114,111,114,32,105,110,102,111,114,109,97,116,105,111,110,0,0,116,101,114,109,105,110,97,116,105,110,103,32,119,105,116,104,32,37,115,32,101,120,99,101,112,116,105,111,110,32,111,102,32,116,121,112,101,32,37,115,58,32,37,115,0,116,101,114,109,105,110,97,116,105,110,103,32,119,105,116,104,32,37,115,32,101,120,99,101,112,116,105,111,110,32,111,102,32,116,121,112,101,32,37,115,0,116,101,114,109,105,110,97,116,105,110,103,32,119,105,116,104,32,37,115,32,102,111,114,101,105,103,110,32,101,120,99,101,112,116,105,111,110,0,116,101,114,109,105,110,97,116,105,110,103,0,117,110,99,97,117,103,104,116,0,83,116,57,101,120,99,101,112,116,105,111,110,0,78,49,48,95,95,99,120,120,97,98,105,118,49,49,54,95,95,115,104,105,109,95,116,121,112,101,95,105,110,102,111,69,0,83,116,57,116,121,112,101,95,105,110,102,111,0,78,49,48,95,95,99,120,120,97,98,105,118,49,50,48,95,95,115,105,95,99,108,97,115,115,95,116,121,112,101,95,105,110,102,111,69,0,78,49,48,95,95,99,120,120,97,98,105,118,49,49,55,95,95,99,108,97,115,115,95,116,121,112,101,95,105,110,102,111,69,0,112,116,104,114,101,97,100,95,111,110,99,101,32,102,97,105,108,117,114,101,32,105,110,32,95,95,99,120,97,95,103,101,116,95,103,108,111,98,97,108,115,95,102,97,115,116,40,41,0,99,97,110,110,111,116,32,99,114,101,97,116,101,32,112,116,104,114,101,97,100,32,107,101,121,32,102,111,114,32,95,95,99,120,97,95,103,101,116,95,103,108,111,98,97,108,115,40,41,0,99,97,110,110,111,116,32,122,101,114,111,32,111,117,116,32,116,104,114,101,97,100,32,118,97,108,117,101,32,102,111,114,32,95,95,99,120,97,95,103,101,116,95,103,108,111,98,97,108,115,40,41,0,116,101,114,109,105,110,97,116,101,95,104,97,110,100,108,101,114,32,117,110,101,120,112,101,99,116,101,100,108,121,32,114,101,116,117,114,110,101,100,0,78,49,48,95,95,99,120,120,97,98,105,118,49,49,57,95,95,112,111,105,110,116,101,114,95,116,121,112,101,95,105,110,102,111,69,0,78,49,48,95,95,99,120,120,97,98,105,118,49,49,55,95,95,112,98,97,115,101,95,116,121,112,101,95,105,110,102,111,69,0],"i8",ALLOC_NONE,Runtime.GLOBAL_BASE);var tempDoublePtr=STATICTOP;function _abort(){Module.abort()}function __ZSt18uncaught_exceptionv(){return!!__ZSt18uncaught_exceptionv.uncaught_exception}STATICTOP+=16;var EXCEPTIONS={last:0,caught:[],infos:{},deAdjust:function(e){if(!e||EXCEPTIONS.infos[e])return e;for(var r in EXCEPTIONS.infos){if(EXCEPTIONS.infos[r].adjusted===e)return r}return e},addRef:function(e){e&&EXCEPTIONS.infos[e].refcount++},decRef:function(e){if(e){var r=EXCEPTIONS.infos[e];assert(r.refcount>0),r.refcount--,0!==r.refcount||r.rethrown||(r.destructor&&Module.dynCall_vi(r.destructor,e),delete EXCEPTIONS.infos[e],___cxa_free_exception(e))}},clearRef:function(e){e&&(EXCEPTIONS.infos[e].refcount=0)}};function ___cxa_begin_catch(e){var r=EXCEPTIONS.infos[e];return r&&!r.caught&&(r.caught=!0,__ZSt18uncaught_exceptionv.uncaught_exception--),r&&(r.rethrown=!1),EXCEPTIONS.caught.push(e),EXCEPTIONS.addRef(EXCEPTIONS.deAdjust(e)),e}function _pthread_once(e,r){_pthread_once.seen||(_pthread_once.seen={}),e in _pthread_once.seen||(Module.dynCall_v(r),_pthread_once.seen[e]=1)}function _emscripten_memcpy_big(e,r,i){return HEAPU8.set(HEAPU8.subarray(r,r+i),e),e}var SYSCALLS={varargs:0,get:function(e){return SYSCALLS.varargs+=4,HEAP32[SYSCALLS.varargs-4>>2]},getStr:function(){return Pointer_stringify(SYSCALLS.get())},get64:function(){var e=SYSCALLS.get(),r=SYSCALLS.get();return assert(e>=0?0===r:-1===r),e},getZero:function(){assert(0===SYSCALLS.get())}};function ___syscall6(e,r){SYSCALLS.varargs=r;try{var i=SYSCALLS.getStreamFromFD();return FS.close(i),0}catch(e){return"undefined"!=typeof FS&&e instanceof FS.ErrnoError||abort(e),-e.errno}}var cttz_i8=allocate([8,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,6,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,7,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,6,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0],"i8",ALLOC_STATIC),PTHREAD_SPECIFIC={};function _pthread_getspecific(e){return PTHREAD_SPECIFIC[e]||0}function ___setErrNo(e){return Module.___errno_location&&(HEAP32[Module.___errno_location()>>2]=e),e}var PTHREAD_SPECIFIC_NEXT_KEY=1,ERRNO_CODES={EPERM:1,ENOENT:2,ESRCH:3,EINTR:4,EIO:5,ENXIO:6,E2BIG:7,ENOEXEC:8,EBADF:9,ECHILD:10,EAGAIN:11,EWOULDBLOCK:11,ENOMEM:12,EACCES:13,EFAULT:14,ENOTBLK:15,EBUSY:16,EEXIST:17,EXDEV:18,ENODEV:19,ENOTDIR:20,EISDIR:21,EINVAL:22,ENFILE:23,EMFILE:24,ENOTTY:25,ETXTBSY:26,EFBIG:27,ENOSPC:28,ESPIPE:29,EROFS:30,EMLINK:31,EPIPE:32,EDOM:33,ERANGE:34,ENOMSG:42,EIDRM:43,ECHRNG:44,EL2NSYNC:45,EL3HLT:46,EL3RST:47,ELNRNG:48,EUNATCH:49,ENOCSI:50,EL2HLT:51,EDEADLK:35,ENOLCK:37,EBADE:52,EBADR:53,EXFULL:54,ENOANO:55,EBADRQC:56,EBADSLT:57,EDEADLOCK:35,EBFONT:59,ENOSTR:60,ENODATA:61,ETIME:62,ENOSR:63,ENONET:64,ENOPKG:65,EREMOTE:66,ENOLINK:67,EADV:68,ESRMNT:69,ECOMM:70,EPROTO:71,EMULTIHOP:72,EDOTDOT:73,EBADMSG:74,ENOTUNIQ:76,EBADFD:77,EREMCHG:78,ELIBACC:79,ELIBBAD:80,ELIBSCN:81,ELIBMAX:82,ELIBEXEC:83,ENOSYS:38,ENOTEMPTY:39,ENAMETOOLONG:36,ELOOP:40,EOPNOTSUPP:95,EPFNOSUPPORT:96,ECONNRESET:104,ENOBUFS:105,EAFNOSUPPORT:97,EPROTOTYPE:91,ENOTSOCK:88,ENOPROTOOPT:92,ESHUTDOWN:108,ECONNREFUSED:111,EADDRINUSE:98,ECONNABORTED:103,ENETUNREACH:101,ENETDOWN:100,ETIMEDOUT:110,EHOSTDOWN:112,EHOSTUNREACH:113,EINPROGRESS:115,EALREADY:114,EDESTADDRREQ:89,EMSGSIZE:90,EPROTONOSUPPORT:93,ESOCKTNOSUPPORT:94,EADDRNOTAVAIL:99,ENETRESET:102,EISCONN:106,ENOTCONN:107,ETOOMANYREFS:109,EUSERS:87,EDQUOT:122,ESTALE:116,ENOTSUP:95,ENOMEDIUM:123,EILSEQ:84,EOVERFLOW:75,ECANCELED:125,ENOTRECOVERABLE:131,EOWNERDEAD:130,ESTRPIPE:86};function _pthread_key_create(e,r){return 0==e?ERRNO_CODES.EINVAL:(HEAP32[e>>2]=PTHREAD_SPECIFIC_NEXT_KEY,PTHREAD_SPECIFIC[PTHREAD_SPECIFIC_NEXT_KEY]=0,PTHREAD_SPECIFIC_NEXT_KEY++,0)}function ___resumeException(e){throw EXCEPTIONS.last||(EXCEPTIONS.last=e),e+" - Exception catching is disabled, this exception cannot be caught. Compile with -s DISABLE_EXCEPTION_CATCHING=0 or DISABLE_EXCEPTION_CATCHING=2 to catch."}function ___cxa_find_matching_catch(){var e=EXCEPTIONS.last;if(!e)return 0|(Runtime.setTempRet0(0),0);var r=EXCEPTIONS.infos[e],i=r.type;if(!i)return 0|(Runtime.setTempRet0(0),e);var n=Array.prototype.slice.call(arguments);Module.___cxa_is_pointer_type(i),___cxa_find_matching_catch.buffer||(___cxa_find_matching_catch.buffer=_malloc(4)),HEAP32[___cxa_find_matching_catch.buffer>>2]=e,e=___cxa_find_matching_catch.buffer;for(var t=0;t<n.length;t++)if(n[t]&&Module.___cxa_can_catch(n[t],i,e))return e=HEAP32[e>>2],r.adjusted=e,0|(Runtime.setTempRet0(n[t]),e);return e=HEAP32[e>>2],0|(Runtime.setTempRet0(i),e)}function ___gxx_personality_v0(){}function _pthread_setspecific(e,r){return e in PTHREAD_SPECIFIC?(PTHREAD_SPECIFIC[e]=r,0):ERRNO_CODES.EINVAL}function ___syscall140(e,r){SYSCALLS.varargs=r;try{var i=SYSCALLS.getStreamFromFD(),n=(SYSCALLS.get(),SYSCALLS.get()),t=SYSCALLS.get(),o=SYSCALLS.get(),a=n;return FS.llseek(i,a,o),HEAP32[t>>2]=i.position,i.getdents&&0===a&&0===o&&(i.getdents=null),0}catch(e){return"undefined"!=typeof FS&&e instanceof FS.ErrnoError||abort(e),-e.errno}}function ___syscall146(e,r){SYSCALLS.varargs=r;try{var i=SYSCALLS.get(),n=SYSCALLS.get(),t=SYSCALLS.get(),o=0;___syscall146.buffer||(___syscall146.buffers=[null,[],[]],___syscall146.printChar=function(e,r){var i=___syscall146.buffers[e];assert(i),0===r||10===r?((1===e?Module.print:Module.printErr)(UTF8ArrayToString(i,0)),i.length=0):i.push(r)});for(var a=0;a<t;a++){for(var u=HEAP32[n+8*a>>2],f=HEAP32[n+(8*a+4)>>2],l=0;l<f;l++)___syscall146.printChar(i,HEAPU8[u+l]);o+=f}return o}catch(e){return"undefined"!=typeof FS&&e instanceof FS.ErrnoError||abort(e),-e.errno}}function ___syscall54(e,r){SYSCALLS.varargs=r;try{return 0}catch(e){return"undefined"!=typeof FS&&e instanceof FS.ErrnoError||abort(e),-e.errno}}function invoke_iiii(e,r,i,n){try{return Module.dynCall_iiii(e,r,i,n)}catch(e){if("number"!=typeof e&&"longjmp"!==e)throw e;Module.setThrew(1,0)}}function invoke_viiiii(e,r,i,n,t,o){try{Module.dynCall_viiiii(e,r,i,n,t,o)}catch(e){if("number"!=typeof e&&"longjmp"!==e)throw e;Module.setThrew(1,0)}}function invoke_vi(e,r){try{Module.dynCall_vi(e,r)}catch(e){if("number"!=typeof e&&"longjmp"!==e)throw e;Module.setThrew(1,0)}}function invoke_ii(e,r){try{return Module.dynCall_ii(e,r)}catch(e){if("number"!=typeof e&&"longjmp"!==e)throw e;Module.setThrew(1,0)}}function invoke_viii(e,r,i,n){try{Module.dynCall_viii(e,r,i,n)}catch(e){if("number"!=typeof e&&"longjmp"!==e)throw e;Module.setThrew(1,0)}}function invoke_v(e){try{Module.dynCall_v(e)}catch(e){if("number"!=typeof e&&"longjmp"!==e)throw e;Module.setThrew(1,0)}}function invoke_viiiiii(e,r,i,n,t,o,a){try{Module.dynCall_viiiiii(e,r,i,n,t,o,a)}catch(e){if("number"!=typeof e&&"longjmp"!==e)throw e;Module.setThrew(1,0)}}function invoke_viiii(e,r,i,n,t){try{Module.dynCall_viiii(e,r,i,n,t)}catch(e){if("number"!=typeof e&&"longjmp"!==e)throw e;Module.setThrew(1,0)}}__ATEXIT__.push((function(){var e=Module._fflush;e&&e(0);var r=___syscall146.printChar;if(r){var i=___syscall146.buffers;i[1].length&&r(1,10),i[2].length&&r(2,10)}})),DYNAMICTOP_PTR=allocate(1,"i32",ALLOC_STATIC),STACK_BASE=STACKTOP=Runtime.alignMemory(STATICTOP),STACK_MAX=STACK_BASE+TOTAL_STACK,DYNAMIC_BASE=Runtime.alignMemory(STACK_MAX),HEAP32[DYNAMICTOP_PTR>>2]=DYNAMIC_BASE,staticSealed=!0,Module.asmGlobalArg={Math:Math,Int8Array:Int8Array,Int16Array:Int16Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Uint16Array:Uint16Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array,NaN:NaN,Infinity:1/0,byteLength:byteLength},Module.asmLibraryArg={abort:abort,assert:assert,enlargeMemory:enlargeMemory,getTotalMemory:getTotalMemory,abortOnCannotGrowMemory:abortOnCannotGrowMemory,invoke_iiii:invoke_iiii,invoke_viiiii:invoke_viiiii,invoke_vi:invoke_vi,invoke_ii:invoke_ii,invoke_viii:invoke_viii,invoke_v:invoke_v,invoke_viiiiii:invoke_viiiiii,invoke_viiii:invoke_viiii,_pthread_getspecific:_pthread_getspecific,___syscall54:___syscall54,_pthread_setspecific:_pthread_setspecific,___gxx_personality_v0:___gxx_personality_v0,___syscall6:___syscall6,___setErrNo:___setErrNo,_abort:_abort,___cxa_begin_catch:___cxa_begin_catch,_pthread_once:_pthread_once,_emscripten_memcpy_big:_emscripten_memcpy_big,_pthread_key_create:_pthread_key_create,___syscall140:___syscall140,___resumeException:___resumeException,___cxa_find_matching_catch:___cxa_find_matching_catch,___syscall146:___syscall146,__ZSt18uncaught_exceptionv:__ZSt18uncaught_exceptionv,DYNAMICTOP_PTR:DYNAMICTOP_PTR,tempDoublePtr:tempDoublePtr,ABORT:ABORT,STACKTOP:STACKTOP,STACK_MAX:STACK_MAX,cttz_i8:cttz_i8};var asm=function(e,r,i){var n=e.Int8Array,t=new n(i),o=e.Int16Array,a=new o(i),u=e.Int32Array,f=new u(i),l=e.Uint8Array,c=new l(i),s=e.Uint16Array,d=new s(i),_=e.Uint32Array;new _(i);var E=e.Float32Array;new E(i);var M=e.Float64Array,T=new M(i),h=e.byteLength,A=0|r.DYNAMICTOP_PTR,b=0|r.tempDoublePtr;r.ABORT;var m=0|r.STACKTOP;r.STACK_MAX;var v=0|r.cttz_i8;e.NaN,e.Infinity;var p=0;e.Math.floor,e.Math.abs,e.Math.sqrt,e.Math.pow,e.Math.cos,e.Math.sin,e.Math.tan,e.Math.acos,e.Math.asin,e.Math.atan,e.Math.atan2,e.Math.exp,e.Math.log,e.Math.ceil;var S=e.Math.imul;e.Math.min,e.Math.max;var k=e.Math.clz32,R=r.abort;r.assert;var y=r.enlargeMemory,O=r.getTotalMemory,g=r.abortOnCannotGrowMemory;r.invoke_iiii,r.invoke_viiiii,r.invoke_vi,r.invoke_ii,r.invoke_viii,r.invoke_v,r.invoke_viiiiii,r.invoke_viiii;var w=r._pthread_getspecific,C=r.___syscall54,N=r._pthread_setspecific;r.___gxx_personality_v0;var P=r.___syscall6,I=r.___setErrNo,L=r._abort;r.___cxa_begin_catch;var D=r._pthread_once,F=r._emscripten_memcpy_big,U=r._pthread_key_create,H=r.___syscall140;r.___resumeException,r.___cxa_find_matching_catch;var x=r.___syscall146;function B(e){e|=0;var r,i=0,n=0,t=0,o=0,a=0,u=0,l=0,c=0,s=0,d=0,_=0,E=0,M=0,T=0,h=0,A=0,b=0,v=0,p=0,S=0;r=m,m=m+16|0,E=r;do{if(e>>>0<245){if(e=(s=e>>>0<11?16:e+11&-8)>>>3,3&(n=(_=0|f[1144])>>>e)|0)return t=0|f[(n=(e=4616+((i=(1&n^1)+e|0)<<1<<2)|0)+8|0)>>2],(0|e)==(0|(a=0|f[(o=t+8|0)>>2]))?f[1144]=_&~(1<<i):(f[a+12>>2]=e,f[n>>2]=a),S=i<<3,f[t+4>>2]=3|S,f[(S=t+S+4|0)>>2]=1|f[S>>2],m=r,0|(S=o);if(s>>>0>(d=0|f[1146])>>>0){if(0|n)return i=((i=n<<e&((i=2<<e)|0-i))&0-i)-1|0,o=0|f[(e=(i=4616+((t=((n=(i>>>=u=i>>>12&16)>>>5&8)|u|(o=(i>>>=n)>>>2&4)|(e=(i>>>=o)>>>1&2)|(t=(i>>>=e)>>>1&1))+(i>>>t)|0)<<1<<2)|0)+8|0)>>2],(0|i)==(0|(n=0|f[(u=o+8|0)>>2]))?(e=_&~(1<<t),f[1144]=e):(f[n+12>>2]=i,f[e>>2]=n,e=_),a=(t<<3)-s|0,f[o+4>>2]=3|s,f[(t=o+s|0)+4>>2]=1|a,f[t+a>>2]=a,0|d&&(o=0|f[1149],n=4616+((i=d>>>3)<<1<<2)|0,e&(i=1<<i)?i=0|f[(e=n+8|0)>>2]:(f[1144]=e|i,i=n,e=n+8|0),f[e>>2]=o,f[i+12>>2]=o,f[o+8>>2]=i,f[o+12>>2]=n),f[1146]=a,f[1149]=t,m=r,0|(S=u);if(l=0|f[1145]){if(n=(l&0-l)-1|0,e=0|f[4880+(((a=(n>>>=u=n>>>12&16)>>>5&8)|u|(c=(n>>>=a)>>>2&4)|(t=(n>>>=c)>>>1&2)|(e=(n>>>=t)>>>1&1))+(n>>>e)<<2)>>2],n=(-8&f[e+4>>2])-s|0,t=0|f[e+16+((0==(0|f[e+16>>2])&1)<<2)>>2]){do{n=(c=(u=(-8&f[t+4>>2])-s|0)>>>0<n>>>0)?u:n,e=c?t:e,t=0|f[t+16+((0==(0|f[t+16>>2])&1)<<2)>>2]}while(0!=(0|t));c=e,a=n}else c=e,a=n;if(c>>>0<(u=c+s|0)>>>0){o=0|f[c+24>>2],i=0|f[c+12>>2];do{if((0|i)==(0|c)){if(!(i=0|f[(e=c+20|0)>>2])&&!(i=0|f[(e=c+16|0)>>2])){n=0;break}for(;;)if(0|(t=0|f[(n=i+20|0)>>2]))i=t,e=n;else{if(!(t=0|f[(n=i+16|0)>>2]))break;i=t,e=n}f[e>>2]=0,n=i}else n=0|f[c+8>>2],f[n+12>>2]=i,f[i+8>>2]=n,n=i}while(0);do{if(0|o){if(i=0|f[c+28>>2],(0|c)==(0|f[(e=4880+(i<<2)|0)>>2])){if(f[e>>2]=n,!n){f[1145]=l&~(1<<i);break}}else if(f[o+16+(((0|f[o+16>>2])!=(0|c)&1)<<2)>>2]=n,!n)break;f[n+24>>2]=o,0|(i=0|f[c+16>>2])&&(f[n+16>>2]=i,f[i+24>>2]=n),0|(i=0|f[c+20>>2])&&(f[n+20>>2]=i,f[i+24>>2]=n)}}while(0);return a>>>0<16?(S=a+s|0,f[c+4>>2]=3|S,f[(S=c+S+4|0)>>2]=1|f[S>>2]):(f[c+4>>2]=3|s,f[u+4>>2]=1|a,f[u+a>>2]=a,0|d&&(t=0|f[1149],n=4616+((i=d>>>3)<<1<<2)|0,_&(i=1<<i)?i=0|f[(e=n+8|0)>>2]:(f[1144]=_|i,i=n,e=n+8|0),f[e>>2]=t,f[i+12>>2]=t,f[t+8>>2]=i,f[t+12>>2]=n),f[1146]=a,f[1149]=u),m=r,0|(S=c+8|0)}_=s}else _=s}else _=s}else if(e>>>0<=4294967231)if(s=-8&(e=e+11|0),c=0|f[1145]){t=0-s|0,l=(e>>>=8)?s>>>0>16777215?31:s>>>((l=14-((d=((p=e<<(_=(e+1048320|0)>>>16&8))+520192|0)>>>16&4)|_|(l=((p<<=d)+245760|0)>>>16&2))+(p<<l>>>15)|0)+7|0)&1|l<<1:0,n=0|f[4880+(l<<2)>>2];e:do{if(n)for(e=0,u=s<<(31==(0|l)?0:25-(l>>>1)|0),a=0;;){if((o=(-8&f[n+4>>2])-s|0)>>>0<t>>>0){if(!o){e=n,t=0,o=n,p=61;break e}e=n,t=o}if(a=0==(0|(o=0|f[n+20>>2]))|(0|o)==(0|(n=0|f[n+16+(u>>>31<<2)>>2]))?a:o,o=0==(0|n)){n=a,p=57;break}u<<=1&(1^o)}else n=0,e=0,p=57}while(0);if(57==(0|p)){if(0==(0|n)&0==(0|e)){if(!(e=c&((e=2<<l)|0-e))){_=s;break}_=(e&0-e)-1|0,e=0,n=0|f[4880+(((a=(_>>>=u=_>>>12&16)>>>5&8)|u|(l=(_>>>=a)>>>2&4)|(d=(_>>>=l)>>>1&2)|(n=(_>>>=d)>>>1&1))+(_>>>n)<<2)>>2]}n?(o=n,p=61):(l=e,u=t)}if(61==(0|p))for(;;){if(p=0,n=(_=(n=(-8&f[o+4>>2])-s|0)>>>0<t>>>0)?n:t,e=_?o:e,!(o=0|f[o+16+((0==(0|f[o+16>>2])&1)<<2)>>2])){l=e,u=n;break}t=n,p=61}if(0!=(0|l)&&u>>>0<((0|f[1146])-s|0)>>>0){if(l>>>0>=(a=l+s|0)>>>0)return m=r,0|(S=0);o=0|f[l+24>>2],i=0|f[l+12>>2];do{if((0|i)==(0|l)){if(!(i=0|f[(e=l+20|0)>>2])&&!(i=0|f[(e=l+16|0)>>2])){i=0;break}for(;;)if(0|(t=0|f[(n=i+20|0)>>2]))i=t,e=n;else{if(!(t=0|f[(n=i+16|0)>>2]))break;i=t,e=n}f[e>>2]=0}else S=0|f[l+8>>2],f[S+12>>2]=i,f[i+8>>2]=S}while(0);do{if(o){if(e=0|f[l+28>>2],(0|l)==(0|f[(n=4880+(e<<2)|0)>>2])){if(f[n>>2]=i,!i){t=c&~(1<<e),f[1145]=t;break}}else if(f[o+16+(((0|f[o+16>>2])!=(0|l)&1)<<2)>>2]=i,!i){t=c;break}f[i+24>>2]=o,0|(e=0|f[l+16>>2])&&(f[i+16>>2]=e,f[e+24>>2]=i),(e=0|f[l+20>>2])?(f[i+20>>2]=e,f[e+24>>2]=i,t=c):t=c}else t=c}while(0);do{if(u>>>0>=16){if(f[l+4>>2]=3|s,f[a+4>>2]=1|u,f[a+u>>2]=u,i=u>>>3,u>>>0<256){n=4616+(i<<1<<2)|0,(e=0|f[1144])&(i=1<<i)?i=0|f[(e=n+8|0)>>2]:(f[1144]=e|i,i=n,e=n+8|0),f[e>>2]=a,f[i+12>>2]=a,f[a+8>>2]=i,f[a+12>>2]=n;break}if(n=4880+((i=(i=u>>>8)?u>>>0>16777215?31:u>>>((i=14-((v=((S=i<<(p=(i+1048320|0)>>>16&8))+520192|0)>>>16&4)|p|(i=((S<<=v)+245760|0)>>>16&2))+(S<<i>>>15)|0)+7|0)&1|i<<1:0)<<2)|0,f[a+28>>2]=i,f[(e=a+16|0)+4>>2]=0,f[e>>2]=0,!(t&(e=1<<i))){f[1145]=t|e,f[n>>2]=a,f[a+24>>2]=n,f[a+12>>2]=a,f[a+8>>2]=a;break}for(e=u<<(31==(0|i)?0:25-(i>>>1)|0),n=0|f[n>>2];;){if((-8&f[n+4>>2]|0)==(0|u)){p=97;break}if(!(i=0|f[(t=n+16+(e>>>31<<2)|0)>>2])){p=96;break}e<<=1,n=i}if(96==(0|p)){f[t>>2]=a,f[a+24>>2]=n,f[a+12>>2]=a,f[a+8>>2]=a;break}if(97==(0|p)){S=0|f[(p=n+8|0)>>2],f[S+12>>2]=a,f[p>>2]=a,f[a+8>>2]=S,f[a+12>>2]=n,f[a+24>>2]=0;break}}else S=u+s|0,f[l+4>>2]=3|S,f[(S=l+S+4|0)>>2]=1|f[S>>2]}while(0);return m=r,0|(S=l+8|0)}_=s}else _=s;else _=-1}while(0);if((n=0|f[1146])>>>0>=_>>>0)return i=n-_|0,e=0|f[1149],i>>>0>15?(S=e+_|0,f[1149]=S,f[1146]=i,f[S+4>>2]=1|i,f[S+i>>2]=i,f[e+4>>2]=3|_):(f[1146]=0,f[1149]=0,f[e+4>>2]=3|n,f[(S=e+n+4|0)>>2]=1|f[S>>2]),m=r,0|(S=e+8|0);if((u=0|f[1147])>>>0>_>>>0)return v=u-_|0,f[1147]=v,p=(S=0|f[1150])+_|0,f[1150]=p,f[p+4>>2]=1|v,f[S+4>>2]=3|_,m=r,0|(S=S+8|0);if(0|f[1262]?e=0|f[1264]:(f[1264]=4096,f[1263]=4096,f[1265]=-1,f[1266]=-1,f[1267]=0,f[1255]=0,e=-16&E^1431655768,f[E>>2]=e,f[1262]=e,e=4096),l=_+48|0,(s=(a=e+(c=_+47|0)|0)&(o=0-e|0))>>>0<=_>>>0)return m=r,0|(S=0);if(0|(e=0|f[1254])&&(E=(d=0|f[1252])+s|0)>>>0<=d>>>0|E>>>0>e>>>0)return m=r,0|(S=0);e:do{if(4&f[1255])i=0,p=133;else{n=0|f[1150];r:do{if(n){for(t=5024;!((e=0|f[t>>2])>>>0<=n>>>0&&(e+(0|f[(h=t+4|0)>>2])|0)>>>0>n>>>0);){if(!(e=0|f[t+8>>2])){p=118;break r}t=e}if((i=a-u&o)>>>0<2147483647)if((0|(e=0|xe(0|i)))==((0|f[t>>2])+(0|f[h>>2])|0)){if(-1!=(0|e)){u=i,a=e,p=135;break e}}else t=e,p=126;else i=0}else p=118}while(0);do{if(118==(0|p))if(-1!=(0|(n=0|xe(0)))&&(i=n,T=(i=(0==((T=(M=0|f[1263])+-1|0)&i|0)?0:(T+i&0-M)-i|0)+s|0)+(M=0|f[1252])|0,i>>>0>_>>>0&i>>>0<2147483647)){if(0|(h=0|f[1254])&&T>>>0<=M>>>0|T>>>0>h>>>0){i=0;break}if((0|(e=0|xe(0|i)))==(0|n)){u=i,a=n,p=135;break e}t=e,p=126}else i=0}while(0);do{if(126==(0|p)){if(n=0-i|0,!(l>>>0>i>>>0&i>>>0<2147483647&-1!=(0|t))){if(-1==(0|t)){i=0;break}u=i,a=t,p=135;break e}if((e=c-i+(e=0|f[1264])&0-e)>>>0>=2147483647){u=i,a=t,p=135;break e}if(-1==(0|xe(0|e))){xe(0|n),i=0;break}u=e+i|0,a=t,p=135;break e}}while(0);f[1255]=4|f[1255],p=133}}while(0);if(133==(0|p)&&s>>>0<2147483647&&!(-1==(0|(v=0|xe(0|s)))|1^(b=(A=(h=0|xe(0))-v|0)>>>0>(_+40|0)>>>0)|v>>>0<h>>>0&-1!=(0|v)&-1!=(0|h)^1)&&(u=b?A:i,a=v,p=135),135==(0|p)){i=(0|f[1252])+u|0,f[1252]=i,i>>>0>(0|f[1253])>>>0&&(f[1253]=i),c=0|f[1150];do{if(c){for(i=5024;;){if((0|a)==((e=0|f[i>>2])+(t=0|f[(n=i+4|0)>>2])|0)){p=145;break}if(!(o=0|f[i+8>>2]))break;i=o}if(145==(0|p)&&0==(8&f[i+12>>2]|0)&&c>>>0<a>>>0&c>>>0>=e>>>0){f[n>>2]=t+u,p=c+(S=0==(7&(S=c+8|0)|0)?0:0-S&7)|0,S=(0|f[1147])+(u-S)|0,f[1150]=p,f[1147]=S,f[p+4>>2]=1|S,f[p+S+4>>2]=40,f[1151]=f[1266];break}for(a>>>0<(0|f[1148])>>>0&&(f[1148]=a),n=a+u|0,i=5024;;){if((0|f[i>>2])==(0|n)){p=153;break}if(!(e=0|f[i+8>>2]))break;i=e}if(153==(0|p)&&0==(8&f[i+12>>2]|0)){f[i>>2]=a,f[(d=i+4|0)>>2]=(0|f[d>>2])+u,s=(d=a+(0==(7&(d=a+8|0)|0)?0:0-d&7)|0)+_|0,l=(i=n+(0==(7&(i=n+8|0)|0)?0:0-i&7)|0)-d-_|0,f[d+4>>2]=3|_;do{if((0|i)!=(0|c)){if((0|i)==(0|f[1149])){S=(0|f[1146])+l|0,f[1146]=S,f[1149]=s,f[s+4>>2]=1|S,f[s+S>>2]=S;break}if(1==(3&(e=0|f[i+4>>2])|0)){u=-8&e,t=e>>>3;e:do{if(e>>>0<256){if(e=0|f[i+8>>2],(0|(n=0|f[i+12>>2]))==(0|e)){f[1144]=f[1144]&~(1<<t);break}f[e+12>>2]=n,f[n+8>>2]=e;break}a=0|f[i+24>>2],e=0|f[i+12>>2];do{if((0|e)==(0|i)){if(!(e=0|f[(n=(t=i+16|0)+4|0)>>2])){if(!(e=0|f[t>>2])){e=0;break}n=t}for(;;)if(0|(o=0|f[(t=e+20|0)>>2]))e=o,n=t;else{if(!(o=0|f[(t=e+16|0)>>2]))break;e=o,n=t}f[n>>2]=0}else S=0|f[i+8>>2],f[S+12>>2]=e,f[e+8>>2]=S}while(0);if(!a)break;t=4880+((n=0|f[i+28>>2])<<2)|0;do{if((0|i)==(0|f[t>>2])){if(f[t>>2]=e,0|e)break;f[1145]=f[1145]&~(1<<n);break e}if(f[a+16+(((0|f[a+16>>2])!=(0|i)&1)<<2)>>2]=e,!e)break e}while(0);if(f[e+24>>2]=a,0|(t=0|f[(n=i+16|0)>>2])&&(f[e+16>>2]=t,f[t+24>>2]=e),!(n=0|f[n+4>>2]))break;f[e+20>>2]=n,f[n+24>>2]=e}while(0);i=i+u|0,o=u+l|0}else o=l;if(f[(i=i+4|0)>>2]=-2&f[i>>2],f[s+4>>2]=1|o,f[s+o>>2]=o,i=o>>>3,o>>>0<256){n=4616+(i<<1<<2)|0,(e=0|f[1144])&(i=1<<i)?i=0|f[(e=n+8|0)>>2]:(f[1144]=e|i,i=n,e=n+8|0),f[e>>2]=s,f[i+12>>2]=s,f[s+8>>2]=i,f[s+12>>2]=n;break}i=o>>>8;do{if(i){if(o>>>0>16777215){i=31;break}i=o>>>((i=14-((v=((S=i<<(p=(i+1048320|0)>>>16&8))+520192|0)>>>16&4)|p|(i=((S<<=v)+245760|0)>>>16&2))+(S<<i>>>15)|0)+7|0)&1|i<<1}else i=0}while(0);if(t=4880+(i<<2)|0,f[s+28>>2]=i,f[(e=s+16|0)+4>>2]=0,f[e>>2]=0,!((e=0|f[1145])&(n=1<<i))){f[1145]=e|n,f[t>>2]=s,f[s+24>>2]=t,f[s+12>>2]=s,f[s+8>>2]=s;break}for(e=o<<(31==(0|i)?0:25-(i>>>1)|0),n=0|f[t>>2];;){if((-8&f[n+4>>2]|0)==(0|o)){p=194;break}if(!(i=0|f[(t=n+16+(e>>>31<<2)|0)>>2])){p=193;break}e<<=1,n=i}if(193==(0|p)){f[t>>2]=s,f[s+24>>2]=n,f[s+12>>2]=s,f[s+8>>2]=s;break}if(194==(0|p)){S=0|f[(p=n+8|0)>>2],f[S+12>>2]=s,f[p>>2]=s,f[s+8>>2]=S,f[s+12>>2]=n,f[s+24>>2]=0;break}}else S=(0|f[1147])+l|0,f[1147]=S,f[1150]=s,f[s+4>>2]=1|S}while(0);return m=r,0|(S=d+8|0)}for(i=5024;!((e=0|f[i>>2])>>>0<=c>>>0&&(S=e+(0|f[i+4>>2])|0)>>>0>c>>>0);)i=0|f[i+8>>2];i=(e=(e=(o=S+-47|0)+(0==(7&(e=o+8|0)|0)?0:0-e&7)|0)>>>0<(o=c+16|0)>>>0?c:e)+8|0,p=a+(n=0==(7&(n=a+8|0)|0)?0:0-n&7)|0,n=u+-40-n|0,f[1150]=p,f[1147]=n,f[p+4>>2]=1|n,f[p+n+4>>2]=40,f[1151]=f[1266],f[(n=e+4|0)>>2]=27,f[i>>2]=f[1256],f[i+4>>2]=f[1257],f[i+8>>2]=f[1258],f[i+12>>2]=f[1259],f[1256]=a,f[1257]=u,f[1259]=0,f[1258]=i,i=e+24|0;do{p=i,f[(i=i+4|0)>>2]=7}while((p+8|0)>>>0<S>>>0);if((0|e)!=(0|c)){if(a=e-c|0,f[n>>2]=-2&f[n>>2],f[c+4>>2]=1|a,f[e>>2]=a,i=a>>>3,a>>>0<256){n=4616+(i<<1<<2)|0,(e=0|f[1144])&(i=1<<i)?i=0|f[(e=n+8|0)>>2]:(f[1144]=e|i,i=n,e=n+8|0),f[e>>2]=c,f[i+12>>2]=c,f[c+8>>2]=i,f[c+12>>2]=n;break}if(t=4880+((n=(i=a>>>8)?a>>>0>16777215?31:a>>>((n=14-((v=((S=i<<(p=(i+1048320|0)>>>16&8))+520192|0)>>>16&4)|p|(n=((S<<=v)+245760|0)>>>16&2))+(S<<n>>>15)|0)+7|0)&1|n<<1:0)<<2)|0,f[c+28>>2]=n,f[c+20>>2]=0,f[o>>2]=0,!((i=0|f[1145])&(e=1<<n))){f[1145]=i|e,f[t>>2]=c,f[c+24>>2]=t,f[c+12>>2]=c,f[c+8>>2]=c;break}for(e=a<<(31==(0|n)?0:25-(n>>>1)|0),n=0|f[t>>2];;){if((-8&f[n+4>>2]|0)==(0|a)){p=216;break}if(!(i=0|f[(t=n+16+(e>>>31<<2)|0)>>2])){p=215;break}e<<=1,n=i}if(215==(0|p)){f[t>>2]=c,f[c+24>>2]=n,f[c+12>>2]=c,f[c+8>>2]=c;break}if(216==(0|p)){S=0|f[(p=n+8|0)>>2],f[S+12>>2]=c,f[p>>2]=c,f[c+8>>2]=S,f[c+12>>2]=n,f[c+24>>2]=0;break}}}else{0==(0|(S=0|f[1148]))|a>>>0<S>>>0&&(f[1148]=a),f[1256]=a,f[1257]=u,f[1259]=0,f[1153]=f[1262],f[1152]=-1,i=0;do{f[(S=4616+(i<<1<<2)|0)+12>>2]=S,f[S+8>>2]=S,i=i+1|0}while(32!=(0|i));p=a+(S=0==(7&(S=a+8|0)|0)?0:0-S&7)|0,S=u+-40-S|0,f[1150]=p,f[1147]=S,f[p+4>>2]=1|S,f[p+S+4>>2]=40,f[1151]=f[1266]}}while(0);if((i=0|f[1147])>>>0>_>>>0)return v=i-_|0,f[1147]=v,p=(S=0|f[1150])+_|0,f[1150]=p,f[p+4>>2]=1|v,f[S+4>>2]=3|_,m=r,0|(S=S+8|0)}return S=0|Mr(),f[S>>2]=12,m=r,0|(S=0)}function Y(e,r,i,n,o,a){e|=0,r=+r,i|=0,n|=0,o|=0,a|=0;var u,l=0,s=0,d=0,_=0,E=0,M=0,T=0,h=0,A=0,b=0,v=0,k=0,R=0,y=0,O=0,g=0,w=0,C=0,N=0,P=0,I=0,L=0;u=m,m=m+560|0,d=u+8|0,I=L=u+524|0,_=u+512|0,f[(v=u)>>2]=0,P=_+12|0,Qe(r),(0|p)<0?(r=-r,C=1,w=2087):(C=0!=(2049&o|0)&1,w=0==(2048&o|0)?0==(1&o|0)?2088:2093:2090),Qe(r),N=2146435072&p;do{if(N>>>0<2146435072|2146435072==(0|N)&!1){if((l=0!=(h=2*+Er(r,v)))&&(f[v>>2]=(0|f[v>>2])-1),97==(0|(R=32|a))){T=0==(0|(A=32&a))?w:w+9|0,M=2|C,l=12-n|0;do{if(!(n>>>0>11|0==(0|l))){r=8;do{l=l+-1|0,r*=16}while(0!=(0|l));if(45==(0|t[T>>0])){r=-(r+(-h-r));break}r=h+r-r;break}r=h}while(0);(0|(l=0|Pe(l=(0|(s=0|f[v>>2]))<0?0-s|0:s,((0|l)<0)<<31>>31,P)))==(0|P)&&(t[(l=_+11|0)>>0]=48),t[l+-1>>0]=43+(s>>31&2),t[(E=l+-2|0)>>0]=a+15,_=(0|n)<1,d=0==(8&o|0),l=L;do{N=~~r,s=l+1|0,t[l>>0]=c[2122+N>>0]|A,r=16*(r-+(0|N)),1!=(s-I|0)||d&_&0==r?l=s:(t[s>>0]=46,l=l+2|0)}while(0!=r);N=l-I|0,Fe(e,32,i,l=(I=P-E|0)+M+(P=0!=(0|n)&(N+-2|0)<(0|n)?n+2|0:N)|0,o),ir(e,T,M),Fe(e,48,i,l,65536^o),ir(e,L,N),Fe(e,48,P-N|0,0,0),ir(e,E,I),Fe(e,32,i,l,8192^o);break}s=(0|n)<0?6:n,l?(l=(0|f[v>>2])-28|0,f[v>>2]=l,r=268435456*h):(r=h,l=0|f[v>>2]),d=N=(0|l)<0?d:d+288|0;do{O=~~r>>>0,f[d>>2]=O,d=d+4|0,r=1e9*(r-+(O>>>0))}while(0!=r);if((0|l)>0)for(_=N,M=d;;){if(E=(0|l)<29?l:29,(l=M+-4|0)>>>0>=_>>>0){d=0;do{k=0|Ge(0|(y=0|$e(0|(y=0|je(0|f[l>>2],0,0|E)),0|p,0|d,0)),0|(O=p),1e9,0),f[l>>2]=k,d=0|ur(0|y,0|O,1e9,0),l=l+-4|0}while(l>>>0>=_>>>0);d&&(f[(_=_+-4|0)>>2]=d)}for(d=M;!(d>>>0<=_>>>0||0|f[(l=d+-4|0)>>2]);)d=l;if(l=(0|f[v>>2])-E|0,f[v>>2]=l,!((0|l)>0))break;M=d}else _=N;if((0|l)<0){n=1+((s+25|0)/9|0)|0,b=102==(0|R);do{if(A=(0|(A=0-l|0))<9?A:9,_>>>0<d>>>0){E=(1<<A)-1|0,M=1e9>>>A,T=0,l=_;do{O=0|f[l>>2],f[l>>2]=(O>>>A)+T,T=0|S(O&E,M),l=l+4|0}while(l>>>0<d>>>0);l=0==(0|f[_>>2])?_+4|0:_,T?(f[d>>2]=T,_=l,l=d+4|0):(_=l,l=d)}else _=0==(0|f[_>>2])?_+4|0:_,l=d;d=(l-(d=b?N:_)>>2|0)>(0|n)?d+(n<<2)|0:l,l=(0|f[v>>2])+A|0,f[v>>2]=l}while((0|l)<0);l=_,n=d}else l=_,n=d;if(O=N,l>>>0<n>>>0){if(d=9*(O-l>>2)|0,(E=0|f[l>>2])>>>0>=10){_=10;do{_=10*_|0,d=d+1|0}while(E>>>0>=_>>>0)}}else d=0;if((0|(_=s-(102!=(0|R)?d:0)+(((k=0!=(0|s))&(b=103==(0|R)))<<31>>31)|0))<((9*(n-O>>2)|0)-9|0)){if(A=N+4+(((0|(_=_+9216|0))/9|0)-1024<<2)|0,(0|(_=1+((0|_)%9|0)|0))<9){E=10;do{E=10*E|0,_=_+1|0}while(9!=(0|_))}else E=10;if((_=(A+4|0)==(0|n))&0==(0|(T=((M=0|f[A>>2])>>>0)%(E>>>0)|0)))_=A;else if(h=0==(1&((M>>>0)/(E>>>0)|0)|0)?9007199254740992:9007199254740994,r=T>>>0<(y=(0|E)/2|0)>>>0?.5:_&(0|T)==(0|y)?1:1.5,C&&(r=(y=45==(0|t[w>>0]))?-r:r,h=y?-h:h),_=M-T|0,f[A>>2]=_,h+r!=h){if(y=_+E|0,f[A>>2]=y,y>>>0>999999999)for(d=A;_=d+-4|0,f[d>>2]=0,_>>>0<l>>>0&&(f[(l=l+-4|0)>>2]=0),y=1+(0|f[_>>2])|0,f[_>>2]=y,y>>>0>999999999;)d=_;else _=A;if(d=9*(O-l>>2)|0,(M=0|f[l>>2])>>>0>=10){E=10;do{E=10*E|0,d=d+1|0}while(M>>>0>=E>>>0)}}else _=A;_=n>>>0>(_=_+4|0)>>>0?_:n,y=l}else _=n,y=l;for(R=_;;){if(R>>>0<=y>>>0){v=0;break}if(0|f[(l=R+-4|0)>>2]){v=1;break}R=l}n=0-d|0;do{if(b){if((0|(l=(1&(1^k))+s|0))>(0|d)&(0|d)>-5?(E=a+-1|0,s=l+-1-d|0):(E=a+-2|0,s=l+-1|0),!(l=8&o)){if(v&&0!=(0|(g=0|f[R+-4>>2])))if((g>>>0)%10|0)_=0;else{_=0,l=10;do{l=10*l|0,_=_+1|0}while(!(0|(g>>>0)%(l>>>0)))}else _=9;if(l=(9*(R-O>>2)|0)-9|0,102==(32|E)){s=(0|s)<(0|(A=(0|(A=l-_|0))>0?A:0))?s:A,A=0;break}s=(0|s)<(0|(A=(0|(A=l+d-_|0))>0?A:0))?s:A,A=0;break}A=l}else E=a,A=8&o}while(0);if(M=0!=(0|(b=s|A))&1,T=102==(32|E))k=0,l=(0|d)>0?d:0;else{if(((_=P)-(l=0|Pe(l=(0|d)<0?n:d,((0|l)<0)<<31>>31,P))|0)<2)do{t[(l=l+-1|0)>>0]=48}while((_-l|0)<2);t[l+-1>>0]=43+(d>>31&2),t[(l=l+-2|0)>>0]=E,k=l,l=_-l|0}if(Fe(e,32,i,l=C+1+s+M+l|0,o),ir(e,w,C),Fe(e,48,i,l,65536^o),T){M=A=L+9|0,T=L+8|0,_=E=y>>>0>N>>>0?N:y;do{if(d=0|Pe(0|f[_>>2],0,A),(0|_)==(0|E))(0|d)==(0|A)&&(t[T>>0]=48,d=T);else if(d>>>0>L>>>0){ke(0|L,48,d-I|0);do{d=d+-1|0}while(d>>>0>L>>>0)}ir(e,d,M-d|0),_=_+4|0}while(_>>>0<=N>>>0);if(0|b&&ir(e,2138,1),_>>>0<R>>>0&(0|s)>0)for(;;){if((d=0|Pe(0|f[_>>2],0,A))>>>0>L>>>0){ke(0|L,48,d-I|0);do{d=d+-1|0}while(d>>>0>L>>>0)}if(ir(e,d,(0|s)<9?s:9),d=s+-9|0,!((_=_+4|0)>>>0<R>>>0&(0|s)>9)){s=d;break}s=d}Fe(e,48,s+9|0,9,0)}else{if(b=v?R:y+4|0,(0|s)>-1){A=0==(0|A),n=v=L+9|0,M=0-I|0,T=L+8|0,E=y;do{(0|(d=0|Pe(0|f[E>>2],0,v)))==(0|v)&&(t[T>>0]=48,d=T);do{if((0|E)==(0|y)){if(_=d+1|0,ir(e,d,1),A&(0|s)<1){d=_;break}ir(e,2138,1),d=_}else{if(d>>>0<=L>>>0)break;ke(0|L,48,d+M|0);do{d=d+-1|0}while(d>>>0>L>>>0)}}while(0);ir(e,d,(0|s)>(0|(I=n-d|0))?I:s),s=s-I|0,E=E+4|0}while(E>>>0<b>>>0&(0|s)>-1)}Fe(e,48,s+18|0,18,0),ir(e,k,P-k|0)}Fe(e,32,i,l,8192^o)}else L=0!=(32&a|0),Fe(e,32,i,l=C+3|0,-65537&o),ir(e,w,C),ir(e,r!=r|!1?L?2114:2118:L?2106:2110,3),Fe(e,32,i,l,8192^o)}while(0);return m=u,0|((0|l)<(0|i)?i:l)}function X(e,r,i,n,o){e|=0,r|=0,i|=0,n|=0,o|=0;var u,l,c,s,d,_,E,M,h,A=0,b=0,v=0,S=0,k=0,R=0,y=0,O=0,g=0,w=0,C=0,N=0,P=0,I=0;h=m,m=m+64|0,_=h,I=h+24|0,E=h+8|0,M=h+20|0,f[(d=h+16|0)>>2]=r,u=0!=(0|e),c=l=I+40|0,I=I+39|0,s=E+4|0,b=0,A=0,R=0;e:for(;;){do{if((0|A)>-1){if((0|b)>(2147483647-A|0)){A=0|Mr(),f[A>>2]=75,A=-1;break}A=b+A|0;break}}while(0);if(!((b=0|t[r>>0])<<24>>24)){P=87;break}v=r;r:for(;;){switch(b<<24>>24){case 37:b=v,P=9;break r;case 0:b=v;break r}N=v+1|0,f[d>>2]=N,b=0|t[N>>0],v=N}r:do{if(9==(0|P))for(;;){if(P=0,37!=(0|t[v+1>>0]))break r;if(b=b+1|0,v=v+2|0,f[d>>2]=v,37!=(0|t[v>>0]))break;P=9}}while(0);if(b=b-r|0,u&&ir(e,r,b),0|b)r=v;else{(b=(0|t[(S=v+1|0)>>0])-48|0)>>>0<10?(C=(N=36==(0|t[v+2>>0]))?b:-1,R=N?1:R,S=N?v+3|0:S):C=-1,f[d>>2]=S,v=((b=0|t[S>>0])<<24>>24)-32|0;r:do{if(v>>>0<32)for(k=0,y=b;;){if(!(75913&(b=1<<v))){b=y;break r}if(k|=b,S=S+1|0,f[d>>2]=S,(v=((b=0|t[S>>0])<<24>>24)-32|0)>>>0>=32)break;y=b}else k=0}while(0);if(b<<24>>24==42){if((b=(0|t[(v=S+1|0)>>0])-48|0)>>>0<10&&36==(0|t[S+2>>0]))f[o+(b<<2)>>2]=10,b=0|f[n+((0|t[v>>0])-48<<3)>>2],R=1,S=S+3|0;else{if(0|R){A=-1;break}u?(R=3+(0|f[i>>2])&-4,b=0|f[R>>2],f[i>>2]=R+4,R=0,S=v):(b=0,R=0,S=v)}f[d>>2]=S,b=(N=(0|b)<0)?0-b|0:b,k=N?8192|k:k}else{if((0|(b=0|Be(d)))<0){A=-1;break}S=0|f[d>>2]}do{if(46==(0|t[S>>0])){if(42!=(0|t[S+1>>0])){f[d>>2]=S+1,v=0|Be(d),S=0|f[d>>2];break}if((v=(0|t[(y=S+2|0)>>0])-48|0)>>>0<10&&36==(0|t[S+3>>0])){f[o+(v<<2)>>2]=10,v=0|f[n+((0|t[y>>0])-48<<3)>>2],S=S+4|0,f[d>>2]=S;break}if(0|R){A=-1;break e}u?(N=3+(0|f[i>>2])&-4,v=0|f[N>>2],f[i>>2]=N+4):v=0,f[d>>2]=y,S=y}else v=-1}while(0);for(w=0;;){if(((0|t[S>>0])-65|0)>>>0>57){A=-1;break e}if(N=S+1|0,f[d>>2]=N,!(((O=255&(y=0|t[(0|t[S>>0])-65+(1606+(58*w|0))>>0]))+-1|0)>>>0<8))break;w=O,S=N}if(!(y<<24>>24)){A=-1;break}g=(0|C)>-1;do{if(y<<24>>24==19){if(g){A=-1;break e}P=49}else{if(g){f[o+(C<<2)>>2]=O,C=0|f[(g=n+(C<<3)|0)+4>>2],f[(P=_)>>2]=f[g>>2],f[P+4>>2]=C,P=49;break}if(!u){A=0;break e}ie(_,O,i)}}while(0);if(49!=(0|P)||(P=0,u)){S=0!=(0|w)&3==(15&(S=0|t[S>>0])|0)?-33&S:S,g=-65537&k,C=0==(8192&k|0)?k:g;r:do{switch(0|S){case 110:switch((255&w)<<24>>24){case 0:case 1:case 6:f[f[_>>2]>>2]=A,b=0,r=N;continue e;case 2:case 7:b=0|f[_>>2],f[b>>2]=A,f[b+4>>2]=((0|A)<0)<<31>>31,b=0,r=N;continue e;case 3:a[f[_>>2]>>1]=A,b=0,r=N;continue e;case 4:t[f[_>>2]>>0]=A,b=0,r=N;continue e;default:b=0,r=N;continue e}case 112:S=120,v=v>>>0>8?v:8,r=8|C,P=61;break;case 88:case 120:r=C,P=61;break;case 111:k=0,y=2070,v=0==(8&C|0)|(0|v)>(0|(g=c-(O=0|Ve(r=0|f[(S=_)>>2],S=0|f[S+4>>2],l))|0))?v:g+1|0,g=C,P=67;break;case 105:case 100:if(r=0|f[(S=_)>>2],(0|(S=0|f[S+4>>2]))<0){r=0|qe(0,0,0|r,0|S),S=p,f[(k=_)>>2]=r,f[k+4>>2]=S,k=1,y=2070,P=66;break r}k=0!=(2049&C|0)&1,y=0==(2048&C|0)?0==(1&C|0)?2070:2072:2071,P=66;break r;case 117:k=0,y=2070,r=0|f[(S=_)>>2],S=0|f[S+4>>2],P=66;break;case 99:t[I>>0]=f[_>>2],r=I,k=0,y=2070,O=l,S=1,v=g;break;case 109:S=0|Mr(),S=0|nr(0|f[S>>2]),P=71;break;case 115:S=0|(S=0|f[_>>2])?S:2080,P=71;break;case 67:f[E>>2]=f[_>>2],f[s>>2]=0,f[_>>2]=E,O=-1,S=E,P=75;break;case 83:r=0|f[_>>2],v?(O=v,S=r,P=75):(Fe(e,32,b,0,C),r=0,P=84);break;case 65:case 71:case 70:case 69:case 97:case 103:case 102:case 101:b=0|Y(e,+T[_>>3],b,v,C,S),r=N;continue e;default:k=0,y=2070,O=l,S=v,v=C}}while(0);r:do{if(61==(0|P))O=0|Ye(w=0|f[(C=_)>>2],C=0|f[C+4>>2],l,32&S),k=(y=0==(8&r|0)|0==(0|w)&0==(0|C))?0:2,y=y?2070:2070+(S>>4)|0,g=r,r=w,S=C,P=67;else if(66==(0|P))O=0|Pe(r,S,l),g=C,P=67;else if(71==(0|P))P=0,r=S,k=0,y=2070,O=(w=0==(0|(C=0|Te(S,0,v))))?S+v|0:C,S=w?v:C-S|0,v=g;else if(75==(0|P)){for(P=0,y=S,r=0,v=0;(k=0|f[y>>2])&&!((0|(v=0|tr(M,k)))<0|v>>>0>(O-r|0)>>>0)&&O>>>0>(r=v+r|0)>>>0;)y=y+4|0;if((0|v)<0){A=-1;break e}if(Fe(e,32,b,r,C),r)for(k=0;;){if(!(v=0|f[S>>2])){P=84;break r}if((0|(k=(v=0|tr(M,v))+k|0))>(0|r)){P=84;break r}if(ir(e,M,v),k>>>0>=r>>>0){P=84;break}S=S+4|0}else r=0,P=84}}while(0);if(67==(0|P))P=0,C=0!=(0|v)|(S=0!=(0|r)|0!=(0|S)),S=c-O+(1&(1^S))|0,r=C?O:l,O=l,S=C?(0|v)>(0|S)?v:S:v,v=(0|v)>-1?-65537&g:g;else if(84==(0|P)){P=0,Fe(e,32,b,r,8192^C),b=(0|b)>(0|r)?b:r,r=N;continue}Fe(e,32,b=(0|b)<(0|(C=(g=(0|S)<(0|(w=O-r|0))?w:S)+k|0))?C:b,C,v),ir(e,y,k),Fe(e,48,b,C,65536^v),Fe(e,48,g,w,0),ir(e,r,w),Fe(e,32,b,C,8192^v),r=N}else b=0,r=N}}e:do{if(87==(0|P)&&!e)if(R){for(A=1;r=0|f[o+(A<<2)>>2];)if(ie(n+(A<<3)|0,r,i),(0|(A=A+1|0))>=10){A=1;break e}for(;;){if(0|f[o+(A<<2)>>2]){A=-1;break e}if((0|(A=A+1|0))>=10){A=1;break}}}else A=0}while(0);return m=h,0|A}function K(e,r){r|=0;var i,n,o,a,u,l,s,d,_,E,M,T,h,A,b,v=0,p=0,S=0,k=0,R=0,y=0,O=0,g=0,w=0,C=0;if(b=m,m=m+704|0,h=b+144|0,T=b+128|0,M=b+112|0,E=b+96|0,_=b+80|0,d=b+64|0,s=b+48|0,A=b+32|0,i=b+16|0,y=b,o=b+184|0,C=b+160|0,a=0|function(e,r){e|=0;var i=0,n=0,t=0,o=0,a=0,u=0,l=0,s=0;if(s=m,m=m+528|0,a=s,o=s+16|0,!(r=r|0))return m=s,0|(l=0);if(r>>>0<=16)return l=0|Me(e,r),m=s,0|l;if(u=0|Me(e,r+-16|0),(0|(r=0|f[(l=e+20|0)>>2]))<16){n=e+4|0,t=e+8|0,i=e+16|0;do{(0|(e=0|f[n>>2]))==(0|f[t>>2])?e=0:(f[n>>2]=e+1,e=0|c[e>>0]),r=r+8|0,f[l>>2]=r,(0|r)>=33&&(f[a>>2]=866,f[a+4>>2]=3208,f[a+8>>2]=1366,ze(o,812,a),De(o),r=0|f[l>>2]),e=e<<32-r|f[i>>2],f[i>>2]=e}while((0|r)<16)}else i=e=e+16|0,e=0|f[e>>2];return f[i>>2]=e<<16,f[l>>2]=r+-16,m=s,0|(l=e>>>16|u<<16)}(e|=0,14),!a)return function(e){var r=0,i=0,n=0,o=0,a=0,u=0;u=m,m=m+544|0,a=u+16|0,n=u,o=u+32|0,f[(e=e|0)>>2]=0,0|(i=0|f[(r=e+4|0)>>2])&&(7&i?(f[n>>2]=866,f[n+4>>2]=2506,f[n+8>>2]=1232,ze(o,812,n),De(o)):ge(i,0,0,1,0),f[r>>2]=0,f[e+8>>2]=0,f[e+12>>2]=0);if(t[e+16>>0]=0,!(r=0|f[(e=e+20|0)>>2]))return void(m=u);_e(r),7&r?(f[a>>2]=866,f[a+4>>2]=2506,f[a+8>>2]=1232,ze(o,812,a),De(o)):ge(r,0,0,1,0);f[e>>2]=0,m=u}(r),m=b,0|(C=1);if(u=r+4|0,(0|(v=0|f[(l=r+8|0)>>2]))!=(0|a)){if(v>>>0<=a>>>0){do{if((0|f[r+12>>2])>>>0<a>>>0){if(0|te(u,a,(v+1|0)==(0|a),1,0)){v=0|f[l>>2];break}return t[r+16>>0]=1,m=b,0|(C=0)}}while(0);ke((0|f[u>>2])+v|0,0,a-v|0)}f[l>>2]=a}if(ke(0|f[u>>2],0,0|a),(0|(v=0|f[(n=e+20|0)>>2]))<5){k=e+4|0,R=e+8|0,S=e+16|0;do{(0|(p=0|f[k>>2]))==(0|f[R>>2])?p=0:(f[k>>2]=p+1,p=0|c[p>>0]),v=v+8|0,f[n>>2]=v,(0|v)>=33&&(f[y>>2]=866,f[y+4>>2]=3208,f[y+8>>2]=1366,ze(o,812,y),De(o),v=0|f[n>>2]),p=p<<32-v|f[S>>2],f[S>>2]=p}while((0|v)<5)}else S=p=e+16|0,p=0|f[p>>2];if(g=p>>>27,f[S>>2]=p<<5,f[n>>2]=v+-5,(g+-1|0)>>>0>20)return m=b,0|(C=0);f[C+20>>2]=0,f[C>>2]=0,f[C+4>>2]=0,f[C+8>>2]=0,f[C+12>>2]=0,t[C+16>>0]=0,v=C+4|0,p=C+8|0;e:do{if(0|te(v,21,0,1,0)){k=0|f[p>>2],ke((O=0|f[v>>2])+k|0,0,21-k|0),f[p>>2]=21,k=e+4|0,R=e+8|0,y=e+16|0,S=0;do{if((0|(v=0|f[n>>2]))<3)do{(0|(p=0|f[k>>2]))==(0|f[R>>2])?p=0:(f[k>>2]=p+1,p=0|c[p>>0]),v=v+8|0,f[n>>2]=v,(0|v)>=33&&(f[i>>2]=866,f[i+4>>2]=3208,f[i+8>>2]=1366,ze(o,812,i),De(o),v=0|f[n>>2]),p=p<<32-v|f[y>>2],f[y>>2]=p}while((0|v)<3);else p=0|f[y>>2];f[y>>2]=p<<3,f[n>>2]=v+-3,t[O+(0|c[1327+S>>0])>>0]=p>>>29,S=S+1|0}while((0|S)!=(0|g));if(0|se(C)){y=e+4|0,O=e+8|0,g=e+16|0,v=0;r:do{R=a-v|0,S=0|ee(e,C);i:do{if(S>>>0<17)(0|f[l>>2])>>>0<=v>>>0&&(f[A>>2]=866,f[A+4>>2]=910,f[A+8>>2]=1497,ze(o,812,A),De(o)),t[(0|f[u>>2])+v>>0]=S,v=v+1|0;else switch(0|S){case 17:if((0|(p=0|f[n>>2]))<3)do{(0|(S=0|f[y>>2]))==(0|f[O>>2])?S=0:(f[y>>2]=S+1,S=0|c[S>>0]),p=p+8|0,f[n>>2]=p,(0|p)>=33&&(f[s>>2]=866,f[s+4>>2]=3208,f[s+8>>2]=1366,ze(o,812,s),De(o),p=0|f[n>>2]),S=S<<32-p|f[g>>2],f[g>>2]=S}while((0|p)<3);else S=0|f[g>>2];if(f[g>>2]=S<<3,f[n>>2]=p+-3,p=(S=3+(S>>>29)|0)>>>0>R>>>0){v=0;break e}v=(p?0:S)+v|0;break i;case 18:if((0|(p=0|f[n>>2]))<7)do{(0|(S=0|f[y>>2]))==(0|f[O>>2])?S=0:(f[y>>2]=S+1,S=0|c[S>>0]),p=p+8|0,f[n>>2]=p,(0|p)>=33&&(f[d>>2]=866,f[d+4>>2]=3208,f[d+8>>2]=1366,ze(o,812,d),De(o),p=0|f[n>>2]),S=S<<32-p|f[g>>2],f[g>>2]=S}while((0|p)<7);else S=0|f[g>>2];if(f[g>>2]=S<<7,f[n>>2]=p+-7,p=(S=11+(S>>>25)|0)>>>0>R>>>0){v=0;break e}v=(p?0:S)+v|0;break i;default:if((S+-19|0)>>>0>=2){w=81;break r}if(p=0|f[n>>2],19==(0|S)){if((0|p)<2)for(S=p;(0|(p=0|f[y>>2]))==(0|f[O>>2])?k=0:(f[y>>2]=p+1,k=0|c[p>>0]),p=S+8|0,f[n>>2]=p,(0|p)>=33&&(f[_>>2]=866,f[_+4>>2]=3208,f[_+8>>2]=1366,ze(o,812,_),De(o),p=0|f[n>>2]),S=k<<32-p|f[g>>2],f[g>>2]=S,(0|p)<2;)S=p;else S=0|f[g>>2];f[g>>2]=S<<2,S>>>=30,k=3,p=p+-2|0}else{if((0|p)<6)do{(0|(S=0|f[y>>2]))==(0|f[O>>2])?S=0:(f[y>>2]=S+1,S=0|c[S>>0]),p=p+8|0,f[n>>2]=p,(0|p)>=33&&(f[E>>2]=866,f[E+4>>2]=3208,f[E+8>>2]=1366,ze(o,812,E),De(o),p=0|f[n>>2]),S=S<<32-p|f[g>>2],f[g>>2]=S}while((0|p)<6);else S=0|f[g>>2];f[g>>2]=S<<6,S>>>=26,k=7,p=p+-6|0}if(f[n>>2]=p,0==(0|v)|(S=S+k|0)>>>0>R>>>0){v=0;break e}if(p=v+-1|0,(0|f[l>>2])>>>0<=p>>>0&&(f[M>>2]=866,f[M+4>>2]=910,f[M+8>>2]=1497,ze(o,812,M),De(o)),!((k=0|t[(0|f[u>>2])+p>>0])<<24>>24)){v=0;break e}if(v>>>0>=(p=S+v|0)>>>0)break i;do{(0|f[l>>2])>>>0<=v>>>0&&(f[T>>2]=866,f[T+4>>2]=910,f[T+8>>2]=1497,ze(o,812,T),De(o)),t[(0|f[u>>2])+v>>0]=k,v=v+1|0}while((0|v)!=(0|p));v=p}}while(0)}while(a>>>0>v>>>0);if(81==(0|w)){f[h>>2]=866,f[h+4>>2]=3149,f[h+8>>2]=1348,ze(o,812,h),De(o),v=0;break}v=(0|a)==(0|v)?0|se(r):0}else v=0}else t[C+16>>0]=1,v=0}while(0);return ve(C),m=b,0|(C=v)}function V(e,r,i,n){i|=0;var o,u,l,s,_,E,M=0,T=0,h=0,A=0,b=0,v=0,p=0,S=0,k=0,R=0,y=0,O=0,g=0,w=0,C=0,N=0,P=0,I=0,L=0,D=0,F=0,U=0;if(E=m,m=m+880|0,F=E+144|0,_=E+128|0,s=E+112|0,l=E+96|0,L=E+80|0,w=E+64|0,O=E+48|0,g=E+32|0,S=E+16|0,p=E,o=E+360|0,u=E+296|0,U=E+224|0,y=E+156|0,0==(0|(r|=0))|(n|=0)>>>0>11)return m=E,0|(U=0);f[(e|=0)>>2]=r,T=(M=U)+68|0;do{f[M>>2]=0,M=M+4|0}while((0|M)<(0|T));M=0;do{T=U+((255&(D=0|t[i+M>>0]))<<2)|0,D<<24>>24&&(f[T>>2]=1+(0|f[T>>2])),M=M+1|0}while((0|M)!=(0|r));for(T=0,h=0,A=0,b=-1,v=1;(M=0|f[U+(v<<2)>>2])?(f[u+((k=v+-1|0)<<2)>>2]=T,T=M+T|0,D=16-v|0,f[e+28+(k<<2)>>2]=1+(T+-1<<D|(1<<D)-1),f[e+96+(k<<2)>>2]=h,f[y+(v<<2)>>2]=h,k=M+h|0,A=A>>>0>v>>>0?A:v,b=b>>>0<v>>>0?b:v):(f[e+28+(v+-1<<2)>>2]=0,k=h),17!=(0|(v=v+1|0));)T<<=1,h=k;f[e+4>>2]=k,T=e+172|0;do{if(k>>>0>(0|f[T>>2])>>>0){(M=k+-1|0)&k?(M|=M>>>16,M|=M>>>8,M|=M>>>4,M=(M=1+((M|=M>>>2)>>>1|M)|0)>>>0>r>>>0?r:M):M=k,f[T>>2]=M,M=0|f[(h=e+176|0)>>2];do{if(0|M){if(D=0|f[M+-4>>2],M=M+-8|0,0!=(0|D)&&(0|D)==(0|~f[M>>2])||(f[p>>2]=866,f[p+4>>2]=651,f[p+8>>2]=1579,ze(o,812,p),De(o)),7&M){f[S>>2]=866,f[S+4>>2]=2506,f[S+8>>2]=1232,ze(o,812,S),De(o);break}ge(M,0,0,1,0);break}}while(0);if(T=0|pe(8+((M=0|(M=0|f[T>>2])?M:1)<<1)|0,0)){f[T+4>>2]=M,f[T>>2]=~M,f[h>>2]=T+8,R=24;break}f[h>>2]=0,n=0;break}R=24}while(0);e:do{if(24==(0|R)){t[(D=e+24|0)>>0]=b,t[e+25>>0]=A,h=e+176|0,T=0;do{M=255&(I=0|t[i+T>>0]),I<<24>>24&&(0|f[U+(M<<2)>>2]||(f[g>>2]=866,f[g+4>>2]=2276,f[g+8>>2]=977,ze(o,812,g),De(o)),M=0|f[(I=y+(M<<2)|0)>>2],f[I>>2]=M+1,M>>>0>=k>>>0&&(f[O>>2]=866,f[O+4>>2]=2280,f[O+8>>2]=990,ze(o,812,O),De(o)),a[(0|f[h>>2])+(M<<1)>>1]=T),T=T+1|0}while((0|T)!=(0|r));if(P=(0|c[D>>0])>>>0<n>>>0?n:0,f[(I=e+8|0)>>2]=P,N=0!=(0|P)){C=1<<P,M=e+164|0;do{if(C>>>0>(0|f[M>>2])>>>0){f[M>>2]=C,M=0|f[(h=e+168|0)>>2];do{if(0|M){if(g=0|f[M+-4>>2],M=M+-8|0,0!=(0|g)&&(0|g)==(0|~f[M>>2])||(f[w>>2]=866,f[w+4>>2]=651,f[w+8>>2]=1579,ze(o,812,w),De(o)),7&M){f[L>>2]=866,f[L+4>>2]=2506,f[L+8>>2]=1232,ze(o,812,L),De(o);break}ge(M,0,0,1,0);break}}while(0);if(T=0|pe((M=C<<2)+8|0,0)){L=T+8|0,f[T+4>>2]=C,f[T>>2]=~C,f[h>>2]=L,T=L;break}f[h>>2]=0,n=0;break e}M=C<<2,h=T=e+168|0,T=0|f[T>>2]}while(0);ke(0|T,-1,0|M),O=e+176|0,y=1;do{if(0|f[U+(y<<2)>>2]&&(w=1<<(g=P-y|0),T=0|f[u+((M=y+-1|0)<<2)>>2],M>>>0>=16&&(f[l>>2]=866,f[l+4>>2]=1960,f[l+8>>2]=1453,ze(o,812,l),De(o)),T>>>0<=(r=0==(0|(r=0|f[e+28+(M<<2)>>2]))?-1:(r+-1|0)>>>(16-y|0))>>>0)){k=(0|f[e+96+(M<<2)>>2])-T|0,R=y<<16;do{M=0|d[(0|f[O>>2])+(k+T<<1)>>1],(0|c[i+M>>0])!=(0|y)&&(f[s>>2]=866,f[s+4>>2]=2322,f[s+8>>2]=1019,ze(o,812,s),De(o)),S=T<<g,v=M|R,b=0;do{(p=b+S|0)>>>0>=C>>>0&&(f[_>>2]=866,f[_+4>>2]=2328,f[_+8>>2]=1053,ze(o,812,_),De(o)),M=0|f[h>>2],-1!=(0|f[M+(p<<2)>>2])&&(f[F>>2]=866,f[F+4>>2]=2330,f[F+8>>2]=1076,ze(o,812,F),De(o),M=0|f[h>>2]),f[M+(p<<2)>>2]=v,b=b+1|0}while(b>>>0<w>>>0);T=T+1|0}while(T>>>0<=r>>>0)}y=y+1|0}while(P>>>0>=y>>>0)}f[(M=e+96|0)>>2]=(0|f[M>>2])-(0|f[u>>2]),f[(M=e+100|0)>>2]=(0|f[M>>2])-(0|f[u+4>>2]),f[(M=e+104|0)>>2]=(0|f[M>>2])-(0|f[u+8>>2]),f[(M=e+108|0)>>2]=(0|f[M>>2])-(0|f[u+12>>2]),f[(M=e+112|0)>>2]=(0|f[M>>2])-(0|f[u+16>>2]),f[(M=e+116|0)>>2]=(0|f[M>>2])-(0|f[u+20>>2]),f[(M=e+120|0)>>2]=(0|f[M>>2])-(0|f[u+24>>2]),f[(M=e+124|0)>>2]=(0|f[M>>2])-(0|f[u+28>>2]),f[(M=e+128|0)>>2]=(0|f[M>>2])-(0|f[u+32>>2]),f[(M=e+132|0)>>2]=(0|f[M>>2])-(0|f[u+36>>2]),f[(M=e+136|0)>>2]=(0|f[M>>2])-(0|f[u+40>>2]),f[(M=e+140|0)>>2]=(0|f[M>>2])-(0|f[u+44>>2]),f[(M=e+144|0)>>2]=(0|f[M>>2])-(0|f[u+48>>2]),f[(M=e+148|0)>>2]=(0|f[M>>2])-(0|f[u+52>>2]),f[(M=e+152|0)>>2]=(0|f[M>>2])-(0|f[u+56>>2]),f[(M=e+156|0)>>2]=(0|f[M>>2])-(0|f[u+60>>2]),f[(M=e+16|0)>>2]=0,f[(T=e+20|0)>>2]=c[D>>0];r:do{if(N){do{if(!n)break r;F=n,n=n+-1|0}while(!(0|f[U+(F<<2)>>2]));if(f[M>>2]=f[e+28+(n<<2)>>2],n=P+1|0,f[T>>2]=n,n>>>0<=A>>>0){for(;!(0|f[U+(n<<2)>>2]);)if((n=n+1|0)>>>0>A>>>0)break r;f[T>>2]=n}}}while(0);f[e+92>>2]=-1,f[e+160>>2]=1048575,f[e+12>>2]=32-(0|f[I>>2]),n=1}}while(0);return m=E,0|(U=n)}function G(e,r,i,n,o,a,u,l){r|=0,i|=0,n|=0,o|=0,a|=0,u|=0,l|=0;var s,_,E,M,T,h,A,b,v,p,S,k,R,y,O,g,w,C,N,P,I,L,D,F,U,H,x,B=0,Y=0,X=0,K=0,V=0,G=0,W=0,z=0,j=0,J=0,Z=0,q=0,Q=0,$=0,re=0,ie=0,ne=0,te=0,oe=0,ae=0,ue=0,fe=0,le=0,ce=0,se=0;if(x=m,m=m+640|0,F=x+80|0,D=x+64|0,L=x+48|0,H=x+32|0,U=x+16|0,I=x,N=x+128|0,P=x+112|0,T=x+96|0,A=0|f[(h=(e|=0)+272|0)>>2],se=0|f[e+88>>2],b=(0|c[se+63>>0])<<8|0|c[se+64>>0],v=255&(se=0|t[se+17>>0]),!(se<<24>>24))return m=x,1;p=0==(0|l),k=(S=u+-1|0)<<5,R=l+-1|0,y=n<<1,O=e+92|0,g=e+116|0,w=e+164|0,C=e+268|0,se=e+212|0,M=0==(1&o|0),E=0==(1&a|0),_=e+288|0,s=e+284|0,ce=0,e=0,a=0,o=0,i=0,B=1;do{if(!p)for(fe=0|f[r+(ce<<2)>>2],le=0;;){if(X=0==(0|(ue=1&le)),ae=(ue<<6^64)-32|0,ue=(ue<<1^2)-1|0,(0|(Y=X?0:S))!=(0|(te=X?u:-1)))for(oe=E|(0|le)!=(0|R),ne=X?fe:fe+k|0;;){1==(0|B)&&(B=512|ee(O,g)),ie=7&B,B>>>=3,K=0|c[1539+ie>>0],X=0;do{i=(re=($=(Q=(0|ee(O,w))+i|0)-A|0)>>31)&Q|$&~re,(0|f[h>>2])>>>0<=i>>>0&&(f[I>>2]=866,f[I+4>>2]=910,f[I+8>>2]=1497,ze(N,812,I),De(N)),f[P+(X<<2)>>2]=d[(0|f[C>>2])+(i<<1)>>1],X=X+1|0}while(X>>>0<K>>>0);X=0;do{a=(re=($=(Q=(0|ee(O,w))+a|0)-A|0)>>31)&Q|$&~re,(0|f[h>>2])>>>0<=a>>>0&&(f[U>>2]=866,f[U+4>>2]=910,f[U+8>>2]=1497,ze(N,812,U),De(N)),f[T+(X<<2)>>2]=d[(0|f[C>>2])+(a<<1)>>1],X=X+1|0}while(X>>>0<K>>>0);for(re=M|(0|Y)!=(0|S),Q=0,$=ne;;){if(J=oe|0==(0|Q),Z=Q<<1,re)for(z=0,j=$;o=(o=(W=(q=(0|ee(O,se))+o|0)-b|0)>>31)&q|W&~o,e=(e=(q=(W=(0|ee(O,se))+e|0)-b|0)>>31)&W|q&~e,J&&(W=0|c[z+Z+(1547+(ie<<2))>>0],K=3*o|0,(X=0|f[_>>2])>>>0<=K>>>0&&(f[H>>2]=866,f[H+4>>2]=910,f[H+8>>2]=1497,ze(N,812,H),De(N),X=0|f[_>>2]),K=(V=0|f[s>>2])+(K<<1)|0,X>>>0>(G=3*e|0)>>>0?X=V:(f[L>>2]=866,f[L+4>>2]=910,f[L+8>>2]=1497,ze(N,812,L),De(N),X=0|f[s>>2]),q=X+(G<<1)|0,f[j>>2]=(0|d[K>>1])<<16|f[P+(W<<2)>>2],f[j+4>>2]=(0|d[K+4>>1])<<16|0|d[K+2>>1],f[j+8>>2]=(0|d[q>>1])<<16|f[T+(W<<2)>>2],f[j+12>>2]=(0|d[q+4>>1])<<16|0|d[q+2>>1]),2!=(0|(z=z+1|0));)j=j+16|0;else for(q=1^J,J=1547+(ie<<2)+Z|0,z=0,j=$;o=(o=(W=(Z=(0|ee(O,se))+o|0)-b|0)>>31)&Z|W&~o,e=(e=(Z=(W=(0|ee(O,se))+e|0)-b|0)>>31)&W|Z&~e,0!=(0|z)|q||(W=0|c[J>>0],K=3*o|0,(X=0|f[_>>2])>>>0<=K>>>0&&(f[D>>2]=866,f[D+4>>2]=910,f[D+8>>2]=1497,ze(N,812,D),De(N),X=0|f[_>>2]),K=(V=0|f[s>>2])+(K<<1)|0,X>>>0>(G=3*e|0)>>>0?X=V:(f[F>>2]=866,f[F+4>>2]=910,f[F+8>>2]=1497,ze(N,812,F),De(N),X=0|f[s>>2]),Z=X+(G<<1)|0,f[j>>2]=(0|d[K>>1])<<16|f[P+(W<<2)>>2],f[j+4>>2]=(0|d[K+4>>1])<<16|0|d[K+2>>1],f[j+8>>2]=(0|d[Z>>1])<<16|f[T+(W<<2)>>2],f[j+12>>2]=(0|d[Z+4>>1])<<16|0|d[Z+2>>1]),2!=(0|(z=z+1|0));)j=j+16|0;if(2==(0|(Q=Q+1|0)))break;$=$+n|0}if((0|(Y=ue+Y|0))==(0|te))break;ne=ne+ae|0}if((0|(le=le+1|0))==(0|l))break;fe=fe+y|0}ce=ce+1|0}while((0|ce)!=(0|v));return m=x,1}function W(e){var r=0,i=0,n=0,t=0,o=0,a=0,u=0,l=0;if(e|=0){i=e+-8|0,t=0|f[1148],l=i+(r=-8&(e=0|f[e+-4>>2]))|0;do{if(1&e)u=i,a=i;else{if(n=0|f[i>>2],!(3&e))return;if(o=n+r|0,(a=i+(0-n)|0)>>>0<t>>>0)return;if((0|a)==(0|f[1149])){if(3!=(3&(r=0|f[(e=l+4|0)>>2])|0)){u=a,r=o;break}return f[1146]=o,f[e>>2]=-2&r,f[a+4>>2]=1|o,void(f[a+o>>2]=o)}if(i=n>>>3,n>>>0<256){if(e=0|f[a+8>>2],(0|(r=0|f[a+12>>2]))==(0|e)){f[1144]=f[1144]&~(1<<i),u=a,r=o;break}f[e+12>>2]=r,f[r+8>>2]=e,u=a,r=o;break}t=0|f[a+24>>2],e=0|f[a+12>>2];do{if((0|e)==(0|a)){if(!(e=0|f[(r=(i=a+16|0)+4|0)>>2])){if(!(e=0|f[i>>2])){e=0;break}r=i}for(;;)if(0|(n=0|f[(i=e+20|0)>>2]))e=n,r=i;else{if(!(n=0|f[(i=e+16|0)>>2]))break;e=n,r=i}f[r>>2]=0}else u=0|f[a+8>>2],f[u+12>>2]=e,f[e+8>>2]=u}while(0);if(t){if(r=0|f[a+28>>2],(0|a)==(0|f[(i=4880+(r<<2)|0)>>2])){if(f[i>>2]=e,!e){f[1145]=f[1145]&~(1<<r),u=a,r=o;break}}else if(f[t+16+(((0|f[t+16>>2])!=(0|a)&1)<<2)>>2]=e,!e){u=a,r=o;break}f[e+24>>2]=t,0|(i=0|f[(r=a+16|0)>>2])&&(f[e+16>>2]=i,f[i+24>>2]=e),(r=0|f[r+4>>2])?(f[e+20>>2]=r,f[r+24>>2]=e,u=a,r=o):(u=a,r=o)}else u=a,r=o}}while(0);if(!(a>>>0>=l>>>0)&&1&(n=0|f[(e=l+4|0)>>2])){if(2&n)f[e>>2]=-2&n,f[u+4>>2]=1|r,f[a+r>>2]=r,t=r;else{if(e=0|f[1149],(0|l)==(0|f[1150])){if(l=(0|f[1147])+r|0,f[1147]=l,f[1150]=u,f[u+4>>2]=1|l,(0|u)!=(0|e))return;return f[1149]=0,void(f[1146]=0)}if((0|l)==(0|e))return l=(0|f[1146])+r|0,f[1146]=l,f[1149]=a,f[u+4>>2]=1|l,void(f[a+l>>2]=l);t=(-8&n)+r|0,i=n>>>3;do{if(n>>>0<256){if(r=0|f[l+8>>2],(0|(e=0|f[l+12>>2]))==(0|r)){f[1144]=f[1144]&~(1<<i);break}f[r+12>>2]=e,f[e+8>>2]=r;break}o=0|f[l+24>>2],e=0|f[l+12>>2];do{if((0|e)==(0|l)){if(!(e=0|f[(r=(i=l+16|0)+4|0)>>2])){if(!(e=0|f[i>>2])){i=0;break}r=i}for(;;)if(0|(n=0|f[(i=e+20|0)>>2]))e=n,r=i;else{if(!(n=0|f[(i=e+16|0)>>2]))break;e=n,r=i}f[r>>2]=0,i=e}else i=0|f[l+8>>2],f[i+12>>2]=e,f[e+8>>2]=i,i=e}while(0);if(0|o){if(e=0|f[l+28>>2],(0|l)==(0|f[(r=4880+(e<<2)|0)>>2])){if(f[r>>2]=i,!i){f[1145]=f[1145]&~(1<<e);break}}else if(f[o+16+(((0|f[o+16>>2])!=(0|l)&1)<<2)>>2]=i,!i)break;f[i+24>>2]=o,0|(r=0|f[(e=l+16|0)>>2])&&(f[i+16>>2]=r,f[r+24>>2]=i),0|(e=0|f[e+4>>2])&&(f[i+20>>2]=e,f[e+24>>2]=i)}}while(0);if(f[u+4>>2]=1|t,f[a+t>>2]=t,(0|u)==(0|f[1149]))return void(f[1146]=t)}if(e=t>>>3,t>>>0<256)return i=4616+(e<<1<<2)|0,(r=0|f[1144])&(e=1<<e)?e=0|f[(r=i+8|0)>>2]:(f[1144]=r|e,e=i,r=i+8|0),f[r>>2]=u,f[e+12>>2]=u,f[u+8>>2]=e,void(f[u+12>>2]=i);n=4880+((e=(e=t>>>8)?t>>>0>16777215?31:t>>>((e=14-((o=((l=e<<(a=(e+1048320|0)>>>16&8))+520192|0)>>>16&4)|a|(e=((l<<=o)+245760|0)>>>16&2))+(l<<e>>>15)|0)+7|0)&1|e<<1:0)<<2)|0,f[u+28>>2]=e,f[u+20>>2]=0,f[u+16>>2]=0,r=0|f[1145],i=1<<e;do{if(r&i){for(r=t<<(31==(0|e)?0:25-(e>>>1)|0),i=0|f[n>>2];;){if((-8&f[i+4>>2]|0)==(0|t)){e=73;break}if(!(e=0|f[(n=i+16+(r>>>31<<2)|0)>>2])){e=72;break}r<<=1,i=e}if(72==(0|e)){f[n>>2]=u,f[u+24>>2]=i,f[u+12>>2]=u,f[u+8>>2]=u;break}if(73==(0|e)){l=0|f[(a=i+8|0)>>2],f[l+12>>2]=u,f[a>>2]=u,f[u+8>>2]=l,f[u+12>>2]=i,f[u+24>>2]=0;break}}else f[1145]=r|i,f[n>>2]=u,f[u+24>>2]=n,f[u+12>>2]=u,f[u+8>>2]=u}while(0);if(l=(0|f[1152])-1|0,f[1152]=l,!l){for(e=5032;e=0|f[e>>2];)e=e+8|0;f[1152]=-1}}}}function z(e,r,i,n,o,a,u,l){r|=0,i|=0,n|=0,o|=0,a|=0,u|=0,l|=0;var s,_,E,M,T,h,A,b,v,p,S,k,R,y,O,g,w,C,N,P,I,L,D,F,U,H,x,B,Y,X,K,V,G,W,z,j=0,J=0,Z=0,q=0,Q=0,$=0,re=0,ie=0,ne=0,te=0,oe=0,ae=0,ue=0,fe=0,le=0,ce=0,se=0,de=0,_e=0,Ee=0,Me=0,Te=0;if(z=m,m=m+640|0,V=z+80|0,K=z+64|0,X=z+48|0,W=z+32|0,G=z+16|0,Y=z,x=z+128|0,B=z+112|0,h=z+96|0,b=0|f[(A=(e|=0)+240|0)>>2],p=0|f[(v=e+256|0)>>2],k=0|f[(S=e+272|0)>>2],Te=0|f[e+88>>2],R=(0|c[Te+63>>0])<<8|0|c[Te+64>>0],y=255&(Te=0|t[Te+17>>0]),!(Te<<24>>24))return m=z,1;O=0==(0|l),w=(g=u+-1|0)<<5,C=l+-1|0,N=n<<1,P=e+92|0,I=e+116|0,L=e+164|0,D=e+268|0,F=e+140|0,U=e+236|0,H=e+212|0,Te=e+188|0,T=0==(1&o|0),M=0==(1&a|0),_=e+288|0,E=e+284|0,s=e+252|0,Me=0,e=0,a=0,o=0,i=0,j=1;do{if(!O)for(_e=0|f[r+(Me<<2)>>2],Ee=0;;){if(Z=0==(0|(de=1&Ee)),se=(de<<6^64)-32|0,de=(de<<1^2)-1|0,(0|(J=Z?0:g))!=(0|(le=Z?u:-1)))for(ce=M|(0|Ee)!=(0|C),fe=Z?_e:_e+w|0;;){1==(0|j)&&(j=512|ee(P,I)),ue=7&j,j>>>=3,q=0|c[1539+ue>>0],Z=0;do{a=(ae=(oe=(te=(0|ee(P,L))+a|0)-k|0)>>31)&te|oe&~ae,(0|f[S>>2])>>>0<=a>>>0&&(f[Y>>2]=866,f[Y+4>>2]=910,f[Y+8>>2]=1497,ze(x,812,Y),De(x)),f[h+(Z<<2)>>2]=d[(0|f[D>>2])+(a<<1)>>1],Z=Z+1|0}while(Z>>>0<q>>>0);Z=0;do{i=(ae=(oe=(te=(0|ee(P,F))+i|0)-b|0)>>31)&te|oe&~ae,(0|f[A>>2])>>>0<=i>>>0&&(f[G>>2]=866,f[G+4>>2]=910,f[G+8>>2]=1497,ze(x,812,G),De(x)),f[B+(Z<<2)>>2]=f[(0|f[U>>2])+(i<<2)>>2],Z=Z+1|0}while(Z>>>0<q>>>0);for(ae=T|(0|J)!=(0|g),te=0,oe=fe;;){if(re=ce|0==(0|te),ie=te<<1,ae)for(Q=0,$=oe;e=(e=(q=(ne=(0|ee(P,H))+e|0)-R|0)>>31)&ne|q&~e,o=(o=(ne=(q=(0|ee(P,Te))+o|0)-p|0)>>31)&q|ne&~o,re&&(Z=0|c[Q+ie+(1547+(ue<<2))>>0],q=3*e|0,(0|f[_>>2])>>>0<=q>>>0&&(f[W>>2]=866,f[W+4>>2]=910,f[W+8>>2]=1497,ze(x,812,W),De(x)),ne=(0|f[E>>2])+(q<<1)|0,f[$>>2]=(0|d[ne>>1])<<16|f[h+(Z<<2)>>2],f[$+4>>2]=(0|d[ne+4>>1])<<16|0|d[ne+2>>1],f[$+8>>2]=f[B+(Z<<2)>>2],(0|f[v>>2])>>>0<=o>>>0&&(f[X>>2]=866,f[X+4>>2]=910,f[X+8>>2]=1497,ze(x,812,X),De(x)),f[$+12>>2]=f[(0|f[s>>2])+(o<<2)>>2]),2!=(0|(Q=Q+1|0));)$=$+16|0;else for(ne=1^re,re=1547+(ue<<2)+ie|0,Q=0,$=oe;e=(e=(q=(ie=(0|ee(P,H))+e|0)-R|0)>>31)&ie|q&~e,o=(o=(ie=(q=(0|ee(P,Te))+o|0)-p|0)>>31)&q|ie&~o,0!=(0|Q)|ne||(Z=0|c[re>>0],q=3*e|0,(0|f[_>>2])>>>0<=q>>>0&&(f[K>>2]=866,f[K+4>>2]=910,f[K+8>>2]=1497,ze(x,812,K),De(x)),ie=(0|f[E>>2])+(q<<1)|0,f[$>>2]=(0|d[ie>>1])<<16|f[h+(Z<<2)>>2],f[$+4>>2]=(0|d[ie+4>>1])<<16|0|d[ie+2>>1],f[$+8>>2]=f[B+(Z<<2)>>2],(0|f[v>>2])>>>0<=o>>>0&&(f[V>>2]=866,f[V+4>>2]=910,f[V+8>>2]=1497,ze(x,812,V),De(x)),f[$+12>>2]=f[(0|f[s>>2])+(o<<2)>>2]),2!=(0|(Q=Q+1|0));)$=$+16|0;if(2==(0|(te=te+1|0)))break;oe=oe+n|0}if((0|(J=de+J|0))==(0|le))break;fe=fe+se|0}if((0|(Ee=Ee+1|0))==(0|l))break;_e=_e+N|0}Me=Me+1|0}while((0|Me)!=(0|y));return m=z,1}function j(e,r){var i=0,n=0,t=0,o=0,a=0,u=0,l=0;l=(e|=0)+(r|=0)|0,i=0|f[e+4>>2];do{if(1&i)u=e,i=r;else{if(n=0|f[e>>2],!(3&i))return;if(a=n+r|0,(0|(o=e+(0-n)|0))==(0|f[1149])){if(3!=(3&(i=0|f[(e=l+4|0)>>2])|0)){u=o,i=a;break}return f[1146]=a,f[e>>2]=-2&i,f[o+4>>2]=1|a,void(f[o+a>>2]=a)}if(r=n>>>3,n>>>0<256){if(e=0|f[o+8>>2],(0|(i=0|f[o+12>>2]))==(0|e)){f[1144]=f[1144]&~(1<<r),u=o,i=a;break}f[e+12>>2]=i,f[i+8>>2]=e,u=o,i=a;break}t=0|f[o+24>>2],e=0|f[o+12>>2];do{if((0|e)==(0|o)){if(!(e=0|f[(i=(r=o+16|0)+4|0)>>2])){if(!(e=0|f[r>>2])){e=0;break}i=r}for(;;)if(0|(n=0|f[(r=e+20|0)>>2]))e=n,i=r;else{if(!(n=0|f[(r=e+16|0)>>2]))break;e=n,i=r}f[i>>2]=0}else u=0|f[o+8>>2],f[u+12>>2]=e,f[e+8>>2]=u}while(0);if(t){if(i=0|f[o+28>>2],(0|o)==(0|f[(r=4880+(i<<2)|0)>>2])){if(f[r>>2]=e,!e){f[1145]=f[1145]&~(1<<i),u=o,i=a;break}}else if(f[t+16+(((0|f[t+16>>2])!=(0|o)&1)<<2)>>2]=e,!e){u=o,i=a;break}f[e+24>>2]=t,0|(r=0|f[(i=o+16|0)>>2])&&(f[e+16>>2]=r,f[r+24>>2]=e),(i=0|f[i+4>>2])?(f[e+20>>2]=i,f[i+24>>2]=e,u=o,i=a):(u=o,i=a)}else u=o,i=a}}while(0);if(2&(n=0|f[(e=l+4|0)>>2]))f[e>>2]=-2&n,f[u+4>>2]=1|i,f[u+i>>2]=i;else{if(e=0|f[1149],(0|l)==(0|f[1150])){if(l=(0|f[1147])+i|0,f[1147]=l,f[1150]=u,f[u+4>>2]=1|l,(0|u)!=(0|e))return;return f[1149]=0,void(f[1146]=0)}if((0|l)==(0|e))return l=(0|f[1146])+i|0,f[1146]=l,f[1149]=u,f[u+4>>2]=1|l,void(f[u+l>>2]=l);o=(-8&n)+i|0,r=n>>>3;do{if(n>>>0<256){if(i=0|f[l+8>>2],(0|(e=0|f[l+12>>2]))==(0|i)){f[1144]=f[1144]&~(1<<r);break}f[i+12>>2]=e,f[e+8>>2]=i;break}t=0|f[l+24>>2],e=0|f[l+12>>2];do{if((0|e)==(0|l)){if(!(e=0|f[(i=(r=l+16|0)+4|0)>>2])){if(!(e=0|f[r>>2])){r=0;break}i=r}for(;;)if(0|(n=0|f[(r=e+20|0)>>2]))e=n,i=r;else{if(!(n=0|f[(r=e+16|0)>>2]))break;e=n,i=r}f[i>>2]=0,r=e}else r=0|f[l+8>>2],f[r+12>>2]=e,f[e+8>>2]=r,r=e}while(0);if(0|t){if(e=0|f[l+28>>2],(0|l)==(0|f[(i=4880+(e<<2)|0)>>2])){if(f[i>>2]=r,!r){f[1145]=f[1145]&~(1<<e);break}}else if(f[t+16+(((0|f[t+16>>2])!=(0|l)&1)<<2)>>2]=r,!r)break;f[r+24>>2]=t,0|(i=0|f[(e=l+16|0)>>2])&&(f[r+16>>2]=i,f[i+24>>2]=r),0|(e=0|f[e+4>>2])&&(f[r+20>>2]=e,f[e+24>>2]=r)}}while(0);if(f[u+4>>2]=1|o,f[u+o>>2]=o,(0|u)==(0|f[1149]))return void(f[1146]=o);i=o}if(e=i>>>3,i>>>0<256)return r=4616+(e<<1<<2)|0,(i=0|f[1144])&(e=1<<e)?e=0|f[(i=r+8|0)>>2]:(f[1144]=i|e,e=r,i=r+8|0),f[i>>2]=u,f[e+12>>2]=u,f[u+8>>2]=e,void(f[u+12>>2]=r);if(t=4880+((e=(e=i>>>8)?i>>>0>16777215?31:i>>>((e=14-((o=((l=e<<(a=(e+1048320|0)>>>16&8))+520192|0)>>>16&4)|a|(e=((l<<=o)+245760|0)>>>16&2))+(l<<e>>>15)|0)+7|0)&1|e<<1:0)<<2)|0,f[u+28>>2]=e,f[u+20>>2]=0,f[u+16>>2]=0,!((r=0|f[1145])&(n=1<<e)))return f[1145]=r|n,f[t>>2]=u,f[u+24>>2]=t,f[u+12>>2]=u,void(f[u+8>>2]=u);for(r=i<<(31==(0|e)?0:25-(e>>>1)|0),n=0|f[t>>2];;){if((-8&f[n+4>>2]|0)==(0|i)){e=69;break}if(!(e=0|f[(t=n+16+(r>>>31<<2)|0)>>2])){e=68;break}r<<=1,n=e}return 68==(0|e)?(f[t>>2]=u,f[u+24>>2]=n,f[u+12>>2]=u,void(f[u+8>>2]=u)):69==(0|e)?(l=0|f[(a=n+8|0)>>2],f[l+12>>2]=u,f[a>>2]=u,f[u+8>>2]=l,f[u+12>>2]=n,void(f[u+24>>2]=0)):void 0}function J(e){var r,i,n,o,u,l,s=0,d=0,_=0,E=0,M=0,T=0,h=0,A=0,b=0,v=0,p=0,S=0,k=0,R=0,y=0,O=0,g=0,w=0,C=0,N=0,P=0,I=0,L=0,D=0,F=0,U=0,H=0,x=0,B=0,Y=0,X=0,V=0,G=0,W=0,z=0;if(l=m,m=m+2416|0,T=l,M=l+1904|0,G=l+1880|0,n=l+980|0,o=l+80|0,u=l+16|0,d=0|f[(e|=0)+88>>2],r=(0|c[d+63>>0])<<8|0|c[d+64>>0],i=e+92|0,s=(0|f[e+4>>2])+((0|c[d+58>>0])<<8|(0|c[d+57>>0])<<16|0|c[d+59>>0])|0,!(d=(0|c[d+61>>0])<<8|(0|c[d+60>>0])<<16|0|c[d+62>>0]))return m=l,0|(G=0);if(f[i>>2]=s,f[e+96>>2]=s,f[e+104>>2]=d,f[e+100>>2]=s+d,f[e+108>>2]=0,f[e+112>>2]=0,f[G+20>>2]=0,f[G>>2]=0,f[G+4>>2]=0,f[G+8>>2]=0,f[G+12>>2]=0,t[G+16>>0]=0,0|K(i,G)){for(s=0,d=-7,_=-7;f[n+(s<<2)>>2]=_,f[o+(s<<2)>>2]=d,E=(0|_)>6,225!=(0|(s=s+1|0));)d=(1&E)+d|0,_=E?-7:_+1|0;d=(s=u)+64|0;do{f[s>>2]=0,s=s+4|0}while((0|s)<(0|d));E=e+284|0,d=3*r|0,s=0|f[(_=e+288|0)>>2];e:do{if((0|s)==(0|d))h=13;else{if(s>>>0<=d>>>0){do{if((0|f[e+292>>2])>>>0<d>>>0){if(0|te(E,d,(s+1|0)==(0|d),2,0)){s=0|f[_>>2];break}t[e+296>>0]=1,s=0;break e}}while(0);ke((0|f[E>>2])+(s<<1)|0,0,d-s<<1|0)}f[_>>2]=d,h=13}}while(0);do{if(13==(0|h)){if(!r){f[T>>2]=866,f[T+4>>2]=910,f[T+8>>2]=1497,ze(M,812,T),De(M),s=1;break}for(w=u+4|0,C=u+8|0,N=u+12|0,P=u+16|0,I=u+20|0,L=u+24|0,D=u+28|0,F=u+32|0,U=u+36|0,H=u+40|0,x=u+44|0,B=u+48|0,Y=u+52|0,X=u+56|0,V=u+60|0,g=0,s=0|f[E>>2],d=0|f[u>>2],_=0|f[w>>2],E=0|f[C>>2],e=0|f[N>>2],M=0|f[P>>2],T=0|f[I>>2],h=0|f[L>>2],A=0|f[D>>2],b=0|f[F>>2],v=0|f[U>>2],p=0|f[H>>2],S=0|f[x>>2],k=0,R=0,y=0,O=0;z=0|ee(i,G),d=d+(0|f[n+(z<<2)>>2])&7,_=_+(0|f[o+(z<<2)>>2])&7,z=0|ee(i,G),E=E+(0|f[n+(z<<2)>>2])&7,e=e+(0|f[o+(z<<2)>>2])&7,z=0|ee(i,G),M=M+(0|f[n+(z<<2)>>2])&7,T=T+(0|f[o+(z<<2)>>2])&7,z=0|ee(i,G),h=h+(0|f[n+(z<<2)>>2])&7,A=A+(0|f[o+(z<<2)>>2])&7,z=0|ee(i,G),b=b+(0|f[n+(z<<2)>>2])&7,v=v+(0|f[o+(z<<2)>>2])&7,z=0|ee(i,G),p=p+(0|f[n+(z<<2)>>2])&7,S=S+(0|f[o+(z<<2)>>2])&7,z=0|ee(i,G),k=k+(0|f[n+(z<<2)>>2])&7,R=R+(0|f[o+(z<<2)>>2])&7,z=0|ee(i,G),y=y+(0|f[n+(z<<2)>>2])&7,O=O+(0|f[o+(z<<2)>>2])&7,z=0|c[1445+T>>0],a[s>>1]=(0|c[1445+_>>0])<<3|0|c[1445+d>>0]|(0|c[1445+E>>0])<<6|(0|c[1445+e>>0])<<9|(0|c[1445+M>>0])<<12|z<<15,W=0|c[1445+p>>0],a[s+2>>1]=(0|c[1445+h>>0])<<2|z>>>1|(0|c[1445+A>>0])<<5|(0|c[1445+b>>0])<<8|(0|c[1445+v>>0])<<11|W<<14,a[s+4>>1]=(0|c[1445+S>>0])<<1|W>>>2|(0|c[1445+k>>0])<<4|(0|c[1445+R>>0])<<7|(0|c[1445+y>>0])<<10|(0|c[1445+O>>0])<<13,!((g=g+1|0)>>>0>=r>>>0);)s=s+6|0;f[u>>2]=d,f[w>>2]=_,f[C>>2]=E,f[N>>2]=e,f[P>>2]=M,f[I>>2]=T,f[L>>2]=h,f[D>>2]=A,f[F>>2]=b,f[U>>2]=v,f[H>>2]=p,f[x>>2]=S,f[B>>2]=k,f[Y>>2]=R,f[X>>2]=y,f[V>>2]=O,s=1}}while(0)}else s=0;return ve(G),m=l,0|(z=s)}function Z(e){var r,i,n,o,a,u,l=0,s=0,d=0,_=0,E=0,M=0,T=0,h=0,A=0,b=0,v=0,p=0,S=0,k=0,R=0,y=0,O=0,g=0,w=0,C=0,N=0,P=0,I=0,L=0,D=0,F=0,U=0,H=0,x=0,B=0,Y=0,X=0,V=0;if(u=m,m=m+1008|0,M=u,E=u+496|0,w=u+472|0,n=u+276|0,o=u+80|0,a=u+16|0,s=0|f[(e|=0)+88>>2],r=(0|c[s+47>>0])<<8|0|c[s+48>>0],i=e+92|0,l=(0|f[e+4>>2])+((0|c[s+42>>0])<<8|(0|c[s+41>>0])<<16|0|c[s+43>>0])|0,!(s=(0|c[s+45>>0])<<8|(0|c[s+44>>0])<<16|0|c[s+46>>0]))return m=u,0|(w=0);if(f[i>>2]=l,f[e+96>>2]=l,f[e+104>>2]=s,f[e+100>>2]=l+s,f[e+108>>2]=0,f[e+112>>2]=0,f[w+20>>2]=0,f[w>>2]=0,f[w+4>>2]=0,f[w+8>>2]=0,f[w+12>>2]=0,t[w+16>>0]=0,0|K(i,w)){for(l=0,s=-3,d=-3;f[n+(l<<2)>>2]=d,f[o+(l<<2)>>2]=s,_=(0|d)>2,49!=(0|(l=l+1|0));)s=(1&_)+s|0,d=_?-3:d+1|0;s=(l=a)+64|0;do{f[l>>2]=0,l=l+4|0}while((0|l)<(0|s));d=e+252|0,l=0|f[(s=e+256|0)>>2];e:do{if((0|l)==(0|r))T=13;else{if(l>>>0<=r>>>0){do{if((0|f[e+260>>2])>>>0<r>>>0){if(0|te(d,r,(l+1|0)==(0|r),4,0)){l=0|f[s>>2];break}t[e+264>>0]=1,l=0;break e}}while(0);ke((0|f[d>>2])+(l<<2)|0,0,r-l<<2|0)}f[s>>2]=r,T=13}}while(0);do{if(13==(0|T)){if(!r){f[M>>2]=866,f[M+4>>2]=910,f[M+8>>2]=1497,ze(E,812,M),De(E),l=1;break}for(e=a+4|0,E=a+8|0,M=a+12|0,T=a+16|0,h=a+20|0,A=a+24|0,b=a+28|0,v=a+32|0,p=a+36|0,S=a+40|0,k=a+44|0,R=a+48|0,y=a+52|0,O=a+56|0,g=a+60|0,_=0,l=0|f[d>>2],s=0|f[e>>2],d=0|f[a>>2];X=0|ee(i,w),d=d+(0|f[n+(X<<2)>>2])&3,s=s+(0|f[o+(X<<2)>>2])&3,X=0|ee(i,w),V=(0|f[E>>2])+(0|f[n+(X<<2)>>2])&3,f[E>>2]=V,X=(0|f[M>>2])+(0|f[o+(X<<2)>>2])&3,f[M>>2]=X,B=0|ee(i,w),Y=(0|f[T>>2])+(0|f[n+(B<<2)>>2])&3,f[T>>2]=Y,B=(0|f[h>>2])+(0|f[o+(B<<2)>>2])&3,f[h>>2]=B,H=0|ee(i,w),x=(0|f[A>>2])+(0|f[n+(H<<2)>>2])&3,f[A>>2]=x,H=(0|f[b>>2])+(0|f[o+(H<<2)>>2])&3,f[b>>2]=H,F=0|ee(i,w),U=(0|f[v>>2])+(0|f[n+(F<<2)>>2])&3,f[v>>2]=U,F=(0|f[p>>2])+(0|f[o+(F<<2)>>2])&3,f[p>>2]=F,L=0|ee(i,w),D=(0|f[S>>2])+(0|f[n+(L<<2)>>2])&3,f[S>>2]=D,L=(0|f[k>>2])+(0|f[o+(L<<2)>>2])&3,f[k>>2]=L,P=0|ee(i,w),I=(0|f[R>>2])+(0|f[n+(P<<2)>>2])&3,f[R>>2]=I,P=(0|f[y>>2])+(0|f[o+(P<<2)>>2])&3,f[y>>2]=P,C=0|ee(i,w),N=(0|f[O>>2])+(0|f[n+(C<<2)>>2])&3,f[O>>2]=N,C=(0|f[g>>2])+(0|f[o+(C<<2)>>2])&3,f[g>>2]=C,f[l>>2]=(0|c[1441+s>>0])<<2|0|c[1441+d>>0]|(0|c[1441+V>>0])<<4|(0|c[1441+X>>0])<<6|(0|c[1441+Y>>0])<<8|(0|c[1441+B>>0])<<10|(0|c[1441+x>>0])<<12|(0|c[1441+H>>0])<<14|(0|c[1441+U>>0])<<16|(0|c[1441+F>>0])<<18|(0|c[1441+D>>0])<<20|(0|c[1441+L>>0])<<22|(0|c[1441+I>>0])<<24|(0|c[1441+P>>0])<<26|(0|c[1441+N>>0])<<28|(0|c[1441+C>>0])<<30,!((_=_+1|0)>>>0>=r>>>0);)l=l+4|0;f[a>>2]=d,f[e>>2]=s,l=1}}while(0)}else l=0;return ve(w),m=u,0|(V=l)}function q(e,r,i,n,o,a,u,l){r|=0,i|=0,n|=0,o|=0,a|=0,u|=0,l|=0;var s,_,E,M,T,h,A,b,v,p,S,k,R,y,O,g,w,C,N,P,I,L,D,F,U=0,H=0,x=0,B=0,Y=0,X=0,K=0,V=0,G=0,W=0,z=0,j=0,J=0,Z=0,q=0,Q=0,$=0,re=0;if(F=m,m=m+592|0,I=F+48|0,D=F+32|0,L=F+16|0,P=F,C=F+80|0,N=F+64|0,h=0|f[(T=(e|=0)+272|0)>>2],re=0|f[e+88>>2],A=(0|c[re+63>>0])<<8|0|c[re+64>>0],b=255&(re=0|t[re+17>>0]),!(re<<24>>24))return m=F,1;v=0==(0|l),S=(p=u+-1|0)<<4,k=l+-1|0,R=n<<1,y=e+92|0,O=e+116|0,g=e+164|0,w=e+268|0,re=e+212|0,M=0==(1&o|0),E=0==(1&a|0),_=e+288|0,s=e+284|0,$=0,o=0,i=0,a=1;do{if(!v)for(q=0|f[r+($<<2)>>2],Q=0;;){if(U=0==(0|(Z=1&Q)),J=(Z<<5^32)-16|0,Z=(Z<<1^2)-1|0,(0|(e=U?0:p))!=(0|(z=U?u:-1)))for(j=E|(0|Q)!=(0|k),W=U?q:q+S|0;;){1==(0|a)&&(a=512|ee(y,O)),G=7&a,a>>>=3,H=0|c[1539+G>>0],U=0;do{i=(V=(K=(X=(0|ee(y,g))+i|0)-h|0)>>31)&X|K&~V,(0|f[T>>2])>>>0<=i>>>0&&(f[P>>2]=866,f[P+4>>2]=910,f[P+8>>2]=1497,ze(C,812,P),De(C)),f[N+(U<<2)>>2]=d[(0|f[w>>2])+(i<<1)>>1],U=U+1|0}while(U>>>0<H>>>0);for(V=M|(0|e)!=(0|p),X=0,K=W;Y=j|0==(0|X),H=X<<1,B=(B=(x=(U=(0|ee(y,re))+o|0)-A|0)>>31)&U|x&~B,V?(Y&&(o=0|c[1547+(G<<2)+H>>0],U=3*B|0,(0|f[_>>2])>>>0<=U>>>0&&(f[L>>2]=866,f[L+4>>2]=910,f[L+8>>2]=1497,ze(C,812,L),De(C)),x=(0|f[s>>2])+(U<<1)|0,f[K>>2]=(0|d[x>>1])<<16|f[N+(o<<2)>>2],f[K+4>>2]=(0|d[x+4>>1])<<16|0|d[x+2>>1]),x=K+8|0,o=(o=(B=(U=(0|ee(y,re))+B|0)-A|0)>>31)&U|B&~o,Y&&(U=0|c[1547+(G<<2)+(1|H)>>0],H=3*o|0,(0|f[_>>2])>>>0<=H>>>0&&(f[I>>2]=866,f[I+4>>2]=910,f[I+8>>2]=1497,ze(C,812,I),De(C)),Y=(0|f[s>>2])+(H<<1)|0,f[x>>2]=(0|d[Y>>1])<<16|f[N+(U<<2)>>2],f[K+12>>2]=(0|d[Y+4>>1])<<16|0|d[Y+2>>1])):(Y&&(o=0|c[1547+(G<<2)+H>>0],U=3*B|0,(0|f[_>>2])>>>0<=U>>>0&&(f[D>>2]=866,f[D+4>>2]=910,f[D+8>>2]=1497,ze(C,812,D),De(C)),Y=(0|f[s>>2])+(U<<1)|0,f[K>>2]=(0|d[Y>>1])<<16|f[N+(o<<2)>>2],f[K+4>>2]=(0|d[Y+4>>1])<<16|0|d[Y+2>>1]),o=(o=(Y=(B=(0|ee(y,re))+B|0)-A|0)>>31)&B|Y&~o),2!=(0|(X=X+1|0));)K=K+n|0;if((0|(e=Z+e|0))==(0|z))break;W=W+J|0}if((0|(Q=Q+1|0))==(0|l))break;q=q+R|0}$=$+1|0}while((0|$)!=(0|b));return m=F,1}function Q(e,r,i,n,t){t|=0;var o=0,a=0,u=0,l=0,c=0,s=0,d=0,_=0,E=0,M=0;if(s=e|=0,a=i|=0,u=_=n|=0,!(c=l=r|=0))return o=0!=(0|t),u?o?(f[t>>2]=0|e,f[t+4>>2]=0&r,0|(p=_=0,t=0)):0|(p=_=0,t=0):(o&&(f[t>>2]=(s>>>0)%(a>>>0),f[t+4>>2]=0),0|(p=_=0,t=(s>>>0)/(a>>>0)>>>0));o=0==(0|u);do{if(a){if(!o){if((o=(0|k(0|u))-(0|k(0|c))|0)>>>0<=31){a=d=o+1|0,e=s>>>(d>>>0)&(r=o-31>>31)|c<<(u=31-o|0),r&=c>>>(d>>>0),o=0,u=s<<u;break}return t?(f[t>>2]=0|e,f[t+4>>2]=l|0&r,0|(p=_=0,t=0)):0|(p=_=0,t=0)}if((o=a-1|0)&a|0){a=u=33+(0|k(0|a))-(0|k(0|c))|0,e=(d=32-u|0)-1>>31&c>>>((E=u-32|0)>>>0)|(c<<d|s>>>(u>>>0))&(r=E>>31),r&=c>>>(u>>>0),o=s<<(M=64-u|0)&(l=d>>31),u=(c<<M|s>>>(E>>>0))&l|s<<d&u-33>>31;break}return 0|t&&(f[t>>2]=o&s,f[t+4>>2]=0),1==(0|a)?0|(p=E=l|0&r,M=0|e):(M=0|Xe(0|a),0|(p=E=c>>>(M>>>0)|0,M=c<<32-M|s>>>(M>>>0)|0))}if(o)return 0|t&&(f[t>>2]=(c>>>0)%(a>>>0),f[t+4>>2]=0),0|(p=E=0,M=(c>>>0)/(a>>>0)>>>0);if(!s)return 0|t&&(f[t>>2]=0,f[t+4>>2]=(c>>>0)%(u>>>0)),0|(p=E=0,M=(c>>>0)/(u>>>0)>>>0);if(!((o=u-1|0)&u))return 0|t&&(f[t>>2]=0|e,f[t+4>>2]=o&c|0&r),E=0,M=c>>>((0|Xe(0|u))>>>0),0|(p=E,M);if((o=(0|k(0|u))-(0|k(0|c))|0)>>>0<=30){a=r=o+1|0,e=c<<(u=31-o|0)|s>>>(r>>>0),r=c>>>(r>>>0),o=0,u=s<<u;break}return t?(f[t>>2]=0|e,f[t+4>>2]=l|0&r,0|(p=E=0,M=0)):0|(p=E=0,M=0)}while(0);if(a){c=0|$e(0|(d=0|i),0|(s=_|0&n),-1,-1),i=p,l=u,u=0;do{n=l,l=o>>>31|l<<1,o=u|o<<1,qe(0|c,0|i,0|(n=e<<1|n>>>31|0),0|(_=e>>>31|r<<1|0)),u=1&(E=(M=p)>>31|((0|M)<0?-1:0)<<1),e=0|qe(0|n,0|_,E&d|0,(((0|M)<0?-1:0)>>31|((0|M)<0?-1:0)<<1)&s|0),r=p,a=a-1|0}while(0!=(0|a));c=l,l=0}else c=u,l=0,u=0;return a=0,0|t&&(f[t>>2]=e,f[t+4>>2]=r),0|(p=E=(0|o)>>>31|(c|a)<<1|0&(a<<1|o>>>31)|l,M=-2&(o<<1|0)|u)}function $(e){var r,i,n,o,a,u=0,l=0,s=0,d=0,_=0,E=0,M=0,T=0;if(a=m,m=m+576|0,E=a,d=a+64|0,T=a+16|0,u=0|f[(s=(e|=0)+88|0)>>2],o=(0|c[u+39>>0])<<8|0|c[u+40>>0],i=e+236|0,(0|(l=0|f[(_=e+240|0)>>2]))!=(0|o)){if(l>>>0<=o>>>0){do{if((0|f[e+244>>2])>>>0<o>>>0){if(0|te(i,o,(l+1|0)==(0|o),4,0)){u=0|f[_>>2];break}return t[e+248>>0]=1,m=a,0|(T=0)}u=l}while(0);ke((0|f[i>>2])+(u<<2)|0,0,o-u<<2|0),u=0|f[s>>2]}f[_>>2]=o}if(n=e+92|0,l=(0|f[e+4>>2])+((0|c[u+34>>0])<<8|(0|c[u+33>>0])<<16|0|c[u+35>>0])|0,!(u=(0|c[u+37>>0])<<8|(0|c[u+36>>0])<<16|0|c[u+38>>0]))return m=a,0|(T=0);if(f[n>>2]=l,f[e+96>>2]=l,f[e+104>>2]=u,f[e+100>>2]=l+u,f[e+108>>2]=0,f[e+112>>2]=0,M=T+20|0,f[T>>2]=0,f[T+4>>2]=0,f[T+8>>2]=0,f[T+12>>2]=0,t[T+16>>0]=0,r=T+24|0,f[T+44>>2]=0,f[M>>2]=0,f[M+4>>2]=0,f[M+8>>2]=0,f[M+12>>2]=0,f[M+16>>2]=0,t[M+20>>0]=0,0|K(n,T)&&0|K(n,r))if(0|f[_>>2]||(f[E>>2]=866,f[E+4>>2]=910,f[E+8>>2]=1497,ze(d,812,E),De(d)),o)for(E=0,M=0,l=0|f[i>>2],s=0,e=0,u=0,d=0,_=0;;){if(E=(0|ee(n,T))+E&31,_=(0|ee(n,r))+_&63,d=(0|ee(n,T))+d&31,u=(0|ee(n,T))+u|0,e=(0|ee(n,r))+e&63,s=(0|ee(n,T))+s&31,f[l>>2]=_<<5|E<<11|d|u<<27|e<<21|s<<16,(M=M+1|0)>>>0>=o>>>0){u=1;break}l=l+4|0,u&=31}else u=1;else u=0;return ve(T+24|0),ve(T),m=a,0|(T=u)}function ee(e,r){e|=0;var i,n,t,o,a,u,l,s,_=0,E=0,M=0,T=0,h=0,A=0;s=m,m=m+576|0,t=s+48|0,a=s+32|0,o=s+16|0,n=s,l=s+64|0,u=0|f[(r|=0)+20>>2],(0|(i=0|f[(A=e+20|0)>>2]))<24?(E=(_=0|f[(h=e+4|0)>>2])>>>0<(M=0|f[e+8>>2])>>>0,(0|i)<16?(E?(T=(0|c[_>>0])<<8,_=_+1|0):T=0,_>>>0<M>>>0?(M=0|c[_>>0],_=_+1|0):M=0,f[h>>2]=_,f[A>>2]=i+16,E=16,_=M|T):(E?(f[h>>2]=_+1,_=0|c[_>>0]):_=0,f[A>>2]=i+8,E=24),M=f[(h=e+16|0)>>2]|_<<E-i,f[h>>2]=M):(h=M=e+16|0,M=0|f[M>>2]),T=1+(M>>>16)|0;do{if(!(T>>>0<=(0|f[u+16>>2])>>>0)){for(E=0|f[u+20>>2];T>>>0>(0|f[u+28+((_=E+-1|0)<<2)>>2])>>>0;)E=E+1|0;if((_=(M>>>(32-E|0))+(0|f[u+96+(_<<2)>>2])|0)>>>0<(0|f[r>>2])>>>0){_=0|d[(0|f[u+176>>2])+(_<<1)>>1];break}return f[t>>2]=866,f[t+4>>2]=3275,f[t+8>>2]=1348,ze(l,812,t),De(l),m=s,0|(A=0)}-1==(0|(E=0|f[(0|f[u+168>>2])+(M>>>(32-(0|f[u+8>>2])|0)<<2)>>2]))&&(f[n>>2]=866,f[n+4>>2]=3253,f[n+8>>2]=1393,ze(l,812,n),De(l)),_=65535&E,E>>>=16,(0|f[r+8>>2])>>>0<=_>>>0&&(f[o>>2]=866,f[o+4>>2]=909,f[o+8>>2]=1497,ze(l,812,o),De(l)),(0|c[(0|f[r+4>>2])+_>>0])!=(0|E)&&(f[a>>2]=866,f[a+4>>2]=3257,f[a+8>>2]=1410,ze(l,812,a),De(l))}while(0);return f[h>>2]=f[h>>2]<<E,f[A>>2]=(0|f[A>>2])-E,m=s,0|(A=_)}function re(e){var r,i,n,o,a,u=0,l=0,c=0;if(a=m,m=m+576|0,c=a+48|0,n=a+32|0,i=a+16|0,r=a,o=a+64|0,f[(e|=0)>>2]=0,0|(l=0|f[(u=e+284|0)>>2])&&(7&l?(f[r>>2]=866,f[r+4>>2]=2506,f[r+8>>2]=1232,ze(o,812,r),De(o)):ge(l,0,0,1,0),f[u>>2]=0,f[e+288>>2]=0,f[e+292>>2]=0),t[e+296>>0]=0,0|(l=0|f[(u=e+268|0)>>2])&&(7&l?(f[i>>2]=866,f[i+4>>2]=2506,f[i+8>>2]=1232,ze(o,812,i),De(o)):ge(l,0,0,1,0),f[u>>2]=0,f[e+272>>2]=0,f[e+276>>2]=0),t[e+280>>0]=0,0|(l=0|f[(u=e+252|0)>>2])&&(7&l?(f[n>>2]=866,f[n+4>>2]=2506,f[n+8>>2]=1232,ze(o,812,n),De(o)):ge(l,0,0,1,0),f[u>>2]=0,f[e+256>>2]=0,f[e+260>>2]=0),t[e+264>>0]=0,!(l=0|f[(u=e+236|0)>>2]))return t[(c=e+248|0)>>0]=0,ve(c=e+212|0),ve(c=e+188|0),ve(c=e+164|0),ve(c=e+140|0),ve(c=e+116|0),void(m=a);7&l?(f[c>>2]=866,f[c+4>>2]=2506,f[c+8>>2]=1232,ze(o,812,c),De(o)):ge(l,0,0,1,0),f[u>>2]=0,f[e+240>>2]=0,f[e+244>>2]=0,t[(c=e+248|0)>>0]=0,ve(c=e+212|0),ve(c=e+188|0),ve(c=e+164|0),ve(c=e+140|0),ve(c=e+116|0),m=a}function ie(e,r,i){e|=0,r|=0,i|=0;var n=0,t=0,o=0;e:do{if(r>>>0<=20){switch(0|r){case 9:n=3+(0|f[i>>2])&-4,r=0|f[n>>2],f[i>>2]=n+4,f[e>>2]=r;break e;case 10:n=3+(0|f[i>>2])&-4,r=0|f[n>>2],f[i>>2]=n+4,f[(n=e)>>2]=r,f[n+4>>2]=((0|r)<0)<<31>>31;break e;case 11:n=3+(0|f[i>>2])&-4,r=0|f[n>>2],f[i>>2]=n+4,f[(n=e)>>2]=r,f[n+4>>2]=0;break e;case 12:n=7+(0|f[i>>2])&-8,t=0|f[(r=n)>>2],r=0|f[r+4>>2],f[i>>2]=n+8,f[(n=e)>>2]=t,f[n+4>>2]=r;break e;case 13:t=3+(0|f[i>>2])&-4,n=0|f[t>>2],f[i>>2]=t+4,n=(65535&n)<<16>>16,f[(t=e)>>2]=n,f[t+4>>2]=((0|n)<0)<<31>>31;break e;case 14:t=3+(0|f[i>>2])&-4,n=0|f[t>>2],f[i>>2]=t+4,f[(t=e)>>2]=65535&n,f[t+4>>2]=0;break e;case 15:t=3+(0|f[i>>2])&-4,n=0|f[t>>2],f[i>>2]=t+4,n=(255&n)<<24>>24,f[(t=e)>>2]=n,f[t+4>>2]=((0|n)<0)<<31>>31;break e;case 16:t=3+(0|f[i>>2])&-4,n=0|f[t>>2],f[i>>2]=t+4,f[(t=e)>>2]=255&n,f[t+4>>2]=0;break e;case 17:case 18:t=7+(0|f[i>>2])&-8,o=+T[t>>3],f[i>>2]=t+8,T[e>>3]=o;break e;default:break e}}}while(0)}function ne(e){var r,i,n,o,u=0,l=0,s=0,d=0,_=0;if(o=m,m=m+560|0,r=o,s=o+40|0,_=o+16|0,l=0|f[(e|=0)+88>>2],i=(0|c[l+55>>0])<<8|0|c[l+56>>0],n=e+92|0,u=(0|f[e+4>>2])+((0|c[l+50>>0])<<8|(0|c[l+49>>0])<<16|0|c[l+51>>0])|0,!(l=(0|c[l+53>>0])<<8|(0|c[l+52>>0])<<16|0|c[l+54>>0]))return m=o,0|(_=0);f[n>>2]=u,f[e+96>>2]=u,f[e+104>>2]=l,f[e+100>>2]=u+l,f[e+108>>2]=0,f[e+112>>2]=0,f[_+20>>2]=0,f[_>>2]=0,f[_+4>>2]=0,f[_+8>>2]=0,f[_+12>>2]=0,t[_+16>>0]=0;e:do{if(0|K(n,_)){if(d=e+268|0,(0|(u=0|f[(l=e+272|0)>>2]))!=(0|i)){if(u>>>0<=i>>>0){do{if((0|f[e+276>>2])>>>0<i>>>0){if(0|te(d,i,(u+1|0)==(0|i),2,0)){u=0|f[l>>2];break}t[e+280>>0]=1,u=0;break e}}while(0);ke((0|f[d>>2])+(u<<1)|0,0,i-u<<1|0)}f[l>>2]=i}if(!i){f[r>>2]=866,f[r+4>>2]=910,f[r+8>>2]=1497,ze(s,812,r),De(s),u=1;break}for(l=0,e=0,s=0,u=0|f[d>>2];;){if(s=(d=0|ee(n,_))+s&255,e=(0|ee(n,_))+e&255,a[u>>1]=e<<8|s,(l=l+1|0)>>>0>=i>>>0){u=1;break}u=u+2|0}}else u=0}while(0);return ve(_),m=o,0|(_=u)}function te(e,r,i,n,t){r|=0,i|=0,n|=0,t|=0;var o,a,u,l,c,s,d=0,_=0,E=0,M=0;if(s=m,m=m+576|0,l=s+48|0,o=s+32|0,_=s+16|0,d=s,u=s+64|0,c=s+60|0,M=(e|=0)+8|0,(0|f[(a=e+4|0)>>2])>>>0>(0|f[M>>2])>>>0&&(f[d>>2]=866,f[d+4>>2]=2123,f[d+8>>2]=845,ze(u,812,d),De(u)),(2147418112/(n>>>0)|0)>>>0<=r>>>0&&(f[_>>2]=866,f[_+4>>2]=2124,f[_+8>>2]=885,ze(u,812,_),De(u)),(d=0|f[M>>2])>>>0>=r>>>0)return m=s,0|(M=1);if(i&&0!=((E=r+-1|0)&r|0)?(r=E>>>16|E,r|=r>>>8,r|=r>>>4,(r=1+((r|=r>>>2)>>>1|r)|0)?i=9:(r=0,i=10)):i=9,9==(0|i)&&r>>>0<=d>>>0&&(i=10),10==(0|i)&&(f[o>>2]=866,f[o+4>>2]=2133,f[o+8>>2]=933,ze(u,812,o),De(u)),E=0|S(r,n),t)if(_=0|pe(E,c)){kr[0&t](_,0|f[e>>2],0|f[a>>2]),d=0|f[e>>2];do{if(0|d){if(7&d){f[l>>2]=866,f[l+4>>2]=2506,f[l+8>>2]=1232,ze(u,812,l),De(u);break}ge(d,0,0,1,0);break}}while(0);f[e>>2]=_,i=20}else r=0;else d=0|function(e,r,i,n){r|=0,i|=0,n|=0;var t=0,o=0,a=0,u=0,l=0,c=0;if(c=m,m=m+560|0,l=c+32|0,o=c+16|0,t=c,a=c+48|0,u=c+44|0,7&(e=e|0)|0)return f[t>>2]=866,f[t+4>>2]=2506,f[t+8>>2]=1210,ze(a,812,t),De(a),m=c,0|(l=0);if(r>>>0>2147418112)return f[o>>2]=866,f[o+4>>2]=2506,f[o+8>>2]=1103,ze(a,812,o),De(a),m=c,0|(l=0);f[u>>2]=r,e=0|ge(e,r,u,n,0),0|i&&(f[i>>2]=f[u>>2]);7&e|0&&(f[l>>2]=866,f[l+4>>2]=2558,f[l+8>>2]=1156,ze(a,812,l),De(a));return m=c,0|(l=e)}(0|f[e>>2],E,c,1),d?(f[e>>2]=d,i=20):r=0;return 20==(0|i)&&((d=0|f[c>>2])>>>0>E>>>0&&(r=(d>>>0)/(n>>>0)|0),f[M>>2]=r,r=1),m=s,0|(M=r)}function oe(e,r){var i,n,o,a,u,l,c,s,d,_,E=0,M=0;if(_=m,m=m+528|0,d=_,l=_+16|0,0==(0|(e|=0))|(r|=0)>>>0<62)return m=_,0|(M=0);if(!(c=0|pe(300,0)))return m=_,0|(M=0);f[c>>2]=519686845,f[c+4>>2]=0,f[c+8>>2]=0,s=c+88|0,i=c+136|0,n=c+160|0,o=c+184|0,a=c+208|0,u=c+232|0,f[(E=c+252|0)>>2]=0,f[E+4>>2]=0,f[E+8>>2]=0,t[E+12>>0]=0,f[(E=c+268|0)>>2]=0,f[E+4>>2]=0,f[E+8>>2]=0,t[E+12>>0]=0,f[(E=c+284|0)>>2]=0,f[E+4>>2]=0,f[E+8>>2]=0,t[E+12>>0]=0,M=(E=s)+44|0;do{f[E>>2]=0,E=E+4|0}while((0|E)<(0|M));return t[s+44>>0]=0,f[i>>2]=0,f[i+4>>2]=0,f[i+8>>2]=0,f[i+12>>2]=0,f[i+16>>2]=0,t[i+20>>0]=0,f[n>>2]=0,f[n+4>>2]=0,f[n+8>>2]=0,f[n+12>>2]=0,f[n+16>>2]=0,t[n+20>>0]=0,f[o>>2]=0,f[o+4>>2]=0,f[o+8>>2]=0,f[o+12>>2]=0,f[o+16>>2]=0,t[o+20>>0]=0,f[a>>2]=0,f[a+4>>2]=0,f[a+8>>2]=0,f[a+12>>2]=0,f[a+16>>2]=0,t[a+20>>0]=0,f[u>>2]=0,f[u+4>>2]=0,f[u+8>>2]=0,f[u+12>>2]=0,t[u+16>>0]=0,0|Ae(c,e,r)?(m=_,0|(M=c)):(re(c),7&c?(f[d>>2]=866,f[d+4>>2]=2506,f[d+8>>2]=1232,ze(l,812,d),De(l),m=_,0|(M=0)):(ge(c,0,0,1,0),m=_,0|(M=0)))}function ae(e,r,i,n,o,a,u){r|=0,i|=0,n|=0,o|=0,a|=0,u|=0;var l,s=0,d=0,_=0;if(_=0|f[(e|=0)+88>>2],s=(((s=(c[_+12>>0]<<8|c[_+13>>0])>>>u)>>>0>1?s:1)+3|0)>>>2,d=(((d=(c[_+14>>0]<<8|c[_+15>>0])>>>u)>>>0>1?d:1)+3|0)>>>2,u=0|t[(_=_+18|0)>>0],u=0|S(s,u<<24>>24==0|u<<24>>24==9?8:16),a){if(!(0==(3&a|0)&u>>>0<=a>>>0))return 0|(o=0);u=a}if((0|S(u,d))>>>0>o>>>0)return 0|(o=0);if(a=(s+1|0)>>>1,l=(d+1|0)>>>1,!i)return 0|(o=0);switch(f[e+92>>2]=r,f[e+96>>2]=r,f[e+104>>2]=i,f[e+100>>2]=r+i,f[e+108>>2]=0,f[e+112>>2]=0,0|t[_>>0]){case 0:if(!(0|function(e,r,i,n,o,a,u,l){r|=0,i|=0,n|=0,o|=0,a|=0,u|=0,l|=0;var s,d,_,E,M,T,h,A,b,v,p,k,R,y,O,g,w,C,N,P,I,L,D,F,U,H,x,B,Y,X,K,V,G=0,W=0,z=0,j=0,J=0,Z=0,q=0,Q=0,$=0,re=0,ie=0,ne=0,te=0,oe=0,ae=0,ue=0,fe=0,le=0;if(V=m,m=m+656|0,X=V+112|0,B=V+96|0,x=V+80|0,H=V+64|0,U=V+48|0,K=V+32|0,Y=V+16|0,F=V,L=V+144|0,D=V+128|0,y=0|f[(R=240+(e|=0)|0)>>2],g=0|f[(O=e+256|0)>>2],w=255&(ue=0|t[17+(0|f[e+88>>2])>>0]),!(ue<<24>>24))return m=V,1;N=0==(0|l),I=(P=u+-1|0)<<4,ue=l+-1|0,h=0!=(1&a|0),A=n<<1,b=e+92|0,v=e+116|0,p=e+140|0,k=e+236|0,T=0!=(1&o|0),M=e+188|0,s=e+252|0,d=1+(C=n>>>2)|0,_=C+2|0,E=C+3|0,ae=0,a=0,i=0,o=1;do{if(!N)for(te=0|f[r+(ae<<2)>>2],oe=0;;){if(W=0==(0|(ie=1&oe)),re=(ie<<5^32)-16|0,ie=(ie<<1^2)-1|0,ne=h&(e=(0|oe)==(0|ue)),(0|(G=W?0:P))!=(0|($=W?u:-1)))for(Q=h&e^1,q=W?te:te+I|0;;){1==(0|o)&&(o=512|ee(b,v)),Z=7&o,o>>>=3,W=0|c[1539+Z>>0],e=0;do{i=(J=(j=(z=(0|ee(b,p))+i|0)-y|0)>>31)&z|j&~J,(0|f[R>>2])>>>0<=i>>>0&&(f[F>>2]=866,f[F+4>>2]=910,f[F+8>>2]=1497,ze(L,812,F),De(L)),f[D+(e<<2)>>2]=f[(0|f[k>>2])+(i<<2)>>2],e=e+1|0}while(e>>>0<W>>>0);if(ne|(J=T&(0|G)==(0|P))){j=0;do{e=q+(0|S(j,n))|0,z=0==(0|j)|Q,W=j<<1,a=(a=(fe=(le=(0|ee(b,M))+a|0)-g|0)>>31)&le|fe&~a;do{if(J){if(!z){a=(a=(le=(fe=(0|ee(b,M))+a|0)-g|0)>>31)&fe|le&~a;break}f[e>>2]=f[D+((0|c[1547+(Z<<2)+W>>0])<<2)>>2],(0|f[O>>2])>>>0<=a>>>0&&(f[B>>2]=866,f[B+4>>2]=910,f[B+8>>2]=1497,ze(L,812,B),De(L)),f[e+4>>2]=f[(0|f[s>>2])+(a<<2)>>2],a=(a=(le=(fe=(0|ee(b,M))+a|0)-g|0)>>31)&fe|le&~a}else z&&(f[e>>2]=f[D+((0|c[1547+(Z<<2)+W>>0])<<2)>>2],(0|f[O>>2])>>>0<=a>>>0&&(f[x>>2]=866,f[x+4>>2]=910,f[x+8>>2]=1497,ze(L,812,x),De(L)),f[e+4>>2]=f[(0|f[s>>2])+(a<<2)>>2]),e=e+8|0,a=(a=(le=(fe=(0|ee(b,M))+a|0)-g|0)>>31)&fe|le&~a,z&&(f[e>>2]=f[D+((0|c[1547+(Z<<2)+(1|W)>>0])<<2)>>2],(0|f[O>>2])>>>0<=a>>>0&&(f[X>>2]=866,f[X+4>>2]=910,f[X+8>>2]=1497,ze(L,812,X),De(L)),f[e+4>>2]=f[(0|f[s>>2])+(a<<2)>>2])}while(0);j=j+1|0}while(2!=(0|j))}else f[q>>2]=f[D+((0|c[1547+(Z<<2)>>0])<<2)>>2],a=(a=(le=(fe=(0|ee(b,M))+a|0)-g|0)>>31)&fe|le&~a,(0|f[O>>2])>>>0<=a>>>0&&(f[Y>>2]=866,f[Y+4>>2]=910,f[Y+8>>2]=1497,ze(L,812,Y),De(L)),f[q+4>>2]=f[(0|f[s>>2])+(a<<2)>>2],f[q+8>>2]=f[D+((0|c[1547+(Z<<2)+1>>0])<<2)>>2],a=(a=(le=(fe=(0|ee(b,M))+a|0)-g|0)>>31)&fe|le&~a,(0|f[O>>2])>>>0<=a>>>0&&(f[K>>2]=866,f[K+4>>2]=910,f[K+8>>2]=1497,ze(L,812,K),De(L)),f[q+12>>2]=f[(0|f[s>>2])+(a<<2)>>2],f[q+(C<<2)>>2]=f[D+((0|c[1547+(Z<<2)+2>>0])<<2)>>2],a=(a=(le=(fe=(0|ee(b,M))+a|0)-g|0)>>31)&fe|le&~a,(0|f[O>>2])>>>0<=a>>>0&&(f[U>>2]=866,f[U+4>>2]=910,f[U+8>>2]=1497,ze(L,812,U),De(L)),f[q+(d<<2)>>2]=f[(0|f[s>>2])+(a<<2)>>2],f[q+(_<<2)>>2]=f[D+((0|c[1547+(Z<<2)+3>>0])<<2)>>2],a=(a=(le=(fe=(0|ee(b,M))+a|0)-g|0)>>31)&fe|le&~a,(0|f[O>>2])>>>0<=a>>>0&&(f[H>>2]=866,f[H+4>>2]=910,f[H+8>>2]=1497,ze(L,812,H),De(L)),f[q+(E<<2)>>2]=f[(0|f[s>>2])+(a<<2)>>2];if((0|(G=ie+G|0))==(0|$))break;q=q+re|0}if((0|(oe=oe+1|0))==(0|l))break;te=te+A|0}ae=ae+1|0}while((0|ae)!=(0|w));return m=V,1}(e,n,o,u,s,d,a,l)))return 0|(o=0);break;case 4:case 6:case 5:case 3:case 2:if(!(0|z(e,n,o,u,s,d,a,l)))return 0|(o=0);break;case 9:if(!(0|q(e,n,o,u,s,d,a,l)))return 0|(o=0);break;case 8:case 7:if(!(0|G(e,n,o,u,s,d,a,l)))return 0|(o=0);break;default:return 0|(o=0)}return 0|(o=1)}function ue(e,r,i){e|=0,r|=0;var n,o,a=0;if((0|(i|=0))>=8192)return 0|F(0|e,0|r,0|i);if(o=0|e,n=e+i|0,(3&e)==(3&r)){for(;3&e;){if(!i)return 0|o;t[e>>0]=0|t[r>>0],e=e+1|0,r=r+1|0,i=i-1|0}for(a=(i=-4&n|0)-64|0;(0|e)<=(0|a);)f[e>>2]=f[r>>2],f[e+4>>2]=f[r+4>>2],f[e+8>>2]=f[r+8>>2],f[e+12>>2]=f[r+12>>2],f[e+16>>2]=f[r+16>>2],f[e+20>>2]=f[r+20>>2],f[e+24>>2]=f[r+24>>2],f[e+28>>2]=f[r+28>>2],f[e+32>>2]=f[r+32>>2],f[e+36>>2]=f[r+36>>2],f[e+40>>2]=f[r+40>>2],f[e+44>>2]=f[r+44>>2],f[e+48>>2]=f[r+48>>2],f[e+52>>2]=f[r+52>>2],f[e+56>>2]=f[r+56>>2],f[e+60>>2]=f[r+60>>2],e=e+64|0,r=r+64|0;for(;(0|e)<(0|i);)f[e>>2]=f[r>>2],e=e+4|0,r=r+4|0}else for(i=n-4|0;(0|e)<(0|i);)t[e>>0]=0|t[r>>0],t[e+1>>0]=0|t[r+1>>0],t[e+2>>0]=0|t[r+2>>0],t[e+3>>0]=0|t[r+3>>0],e=e+4|0,r=r+4|0;for(;(0|e)<(0|n);)t[e>>0]=0|t[r>>0],e=e+1|0,r=r+1|0;return 0|o}function fe(e,r,i){r|=0,i|=0;var n,t,o,a,u,l=0,c=0,s=0,d=0,_=0,E=0;u=m,m=m+48|0,a=u+16|0,s=u,c=u+32|0,l=0|f[(t=(e|=0)+28|0)>>2],f[c>>2]=l,l=(0|f[(o=e+20|0)>>2])-l|0,f[c+4>>2]=l,f[c+8>>2]=r,f[c+12>>2]=i,l=l+i|0,n=e+60|0,f[s>>2]=f[n>>2],f[s+4>>2]=c,f[s+8>>2]=2,s=0|er(0|x(146,0|s));e:do{if((0|l)!=(0|s)){for(r=2;!((0|s)<0);)if(l=l-s|0,r=((_=s>>>0>(E=0|f[c+4>>2])>>>0)<<31>>31)+r|0,E=s-(_?E:0)|0,f[(c=_?c+8|0:c)>>2]=(0|f[c>>2])+E,f[(_=c+4|0)>>2]=(0|f[_>>2])-E,f[a>>2]=f[n>>2],f[a+4>>2]=c,f[a+8>>2]=r,(0|l)==(0|(s=0|er(0|x(146,0|a))))){d=3;break e}f[e+16>>2]=0,f[t>>2]=0,f[o>>2]=0,f[e>>2]=32|f[e>>2],i=2==(0|r)?0:i-(0|f[c+4>>2])|0}else d=3}while(0);return 3==(0|d)&&(E=0|f[e+44>>2],f[e+16>>2]=E+(0|f[e+48>>2]),f[t>>2]=E,f[o>>2]=E),m=u,0|i}function le(e,r,i){e|=0,r|=0,i|=0;var n,o,a,u,l,c=0,s=0,d=0,_=0,E=0,M=0,T=0;l=m,m=m+224|0,n=l+120|0,a=l,u=l+136|0,s=(c=o=l+80|0)+40|0;do{f[c>>2]=0,c=c+4|0}while((0|c)<(0|s));return f[n>>2]=f[i>>2],(0|X(0,r,n,a,o))<0?i=-1:(f[e+76>>2],T=32&(i=0|f[e>>2]),(0|t[e+74>>0])<1&&(f[e>>2]=-33&i),0|f[(c=e+48|0)>>2]?i=0|X(e,r,n,a,o):(d=0|f[(s=e+44|0)>>2],f[s>>2]=u,f[(_=e+28|0)>>2]=u,f[(E=e+20|0)>>2]=u,f[c>>2]=80,f[(M=e+16|0)>>2]=u+80,i=0|X(e,r,n,a,o),d&&(mr[7&f[e+36>>2]](e,0,0),i=0==(0|f[E>>2])?-1:i,f[s>>2]=d,f[c>>2]=0,f[M>>2]=0,f[_>>2]=0,f[E>>2]=0)),c=0|f[e>>2],f[e>>2]=c|T,i=0==(32&c|0)?i:-1),m=l,0|i}function ce(e,r,i,n){r|=0,i|=0,n|=0;var o,u,l,c,s,d,_,E=0,M=0;_=m,m=m+64|0,s=_,M=0|f[(e|=0)>>2],d=e+(0|f[M+-8>>2])|0,M=0|f[M+-4>>2],f[s>>2]=i,f[s+4>>2]=e,f[s+8>>2]=r,f[s+12>>2]=n,r=s+20|0,n=s+24|0,o=s+28|0,u=s+32|0,l=s+40|0,c=(E=e=s+16|0)+36|0;do{f[E>>2]=0,E=E+4|0}while((0|E)<(0|c));a[e+36>>1]=0,t[e+38>>0]=0;e:do{if(0|or(M,i))f[s+48>>2]=1,yr[3&f[20+(0|f[M>>2])>>2]](M,s,d,d,1,0),e=1==(0|f[n>>2])?d:0;else{switch(vr[3&f[24+(0|f[M>>2])>>2]](M,s,d,1,0),0|f[s+36>>2]){case 0:e=1==(0|f[l>>2])&1==(0|f[o>>2])&1==(0|f[u>>2])?0|f[r>>2]:0;break e;case 1:break;default:e=0;break e}if(1!=(0|f[n>>2])&&!(0==(0|f[l>>2])&1==(0|f[o>>2])&1==(0|f[u>>2]))){e=0;break}e=0|f[e>>2]}}while(0);return m=_,0|e}function se(e){var r,i=0,n=0,t=0,o=0,a=0,u=0,l=0;if(r=m,m=m+544|0,u=r+16|0,i=r,o=r+32|0,((n=0|f[(a=(e|=0)+8|0)>>2])+-1|0)>>>0>=8192&&(f[i>>2]=866,f[i+4>>2]=3006,f[i+8>>2]=1257,ze(o,812,i),De(o)),f[e>>2]=n,(i=0|f[(t=e+20|0)>>2])?l=n:((i=0|pe(180,0))?(f[(l=i+164|0)>>2]=0,f[l+4>>2]=0,f[l+8>>2]=0,f[l+12>>2]=0):i=0,f[t>>2]=i,l=0|f[e>>2]),0|f[a>>2]?u=l:(f[u>>2]=866,f[u+4>>2]=910,f[u+8>>2]=1497,ze(o,812,u),De(o),u=0|f[e>>2]),o=0|f[e+4>>2],!(u>>>0>16))return e=0|V(i,l,o,e=0),m=r,0|e;for(n=u,t=0;a=t+1|0,n>>>0>3;)n>>>=1,t=a;return e=0|V(i,l,o,e=255&((e=t+2+(32!=(0|a)&1<<a>>>0<u>>>0&1)|0)>>>0<11?e:11)),m=r,0|e}function de(e,r,i){r|=0,i|=0;var n,o=0,a=0,u=0,l=0,c=0,s=0,d=0,_=0,E=0;n=1794895138+(0|f[(e|=0)>>2])|0,u=0|ar(0|f[e+8>>2],n),o=0|ar(0|f[e+12>>2],n),a=0|ar(0|f[e+16>>2],n);e:do{if(u>>>0<r>>>2>>>0&&(E=r-(u<<2)|0,o>>>0<E>>>0&a>>>0<E>>>0)&&0==(3&(a|o)|0)){for(E=o>>>2,_=a>>>2,d=0;;){if(o=0|ar(0|f[e+((a=(l=(s=d+(c=u>>>1)|0)<<1)+E|0)<<2)>>2],n),!((a=0|ar(0|f[e+(a+1<<2)>>2],n))>>>0<r>>>0&o>>>0<(r-a|0)>>>0)){o=0;break e}if(0|t[e+(a+o)>>0]){o=0;break e}if(!(o=0|He(i,e+a|0)))break;if(o=(0|o)<0,1==(0|u)){o=0;break e}d=o?d:s,u=o?c:u-c|0}a=0|ar(0|f[e+((o=l+_|0)<<2)>>2],n),o=(o=0|ar(0|f[e+(o+1<<2)>>2],n))>>>0<r>>>0&a>>>0<(r-o|0)>>>0&&0==(0|t[e+(o+a)>>0])?e+o|0:0}else o=0}while(0);return 0|o}function _e(e){var r,i,n,t,o,a,u=0,l=0;a=m,m=m+576|0,t=a+48|0,o=a+32|0,i=a+16|0,r=a,n=a+64|0,u=0|f[(e|=0)+168>>2];do{if(0|u){if(l=0|f[u+-4>>2],u=u+-8|0,0!=(0|l)&&(0|l)==(0|~f[u>>2])||(f[r>>2]=866,f[r+4>>2]=651,f[r+8>>2]=1579,ze(n,812,r),De(n)),7&u){f[i>>2]=866,f[i+4>>2]=2506,f[i+8>>2]=1232,ze(n,812,i),De(n);break}ge(u,0,0,1,0);break}}while(0);if(u=0|f[e+176>>2])return l=0|f[u+-4>>2],u=u+-8|0,0!=(0|l)&&(0|l)==(0|~f[u>>2])||(f[o>>2]=866,f[o+4>>2]=651,f[o+8>>2]=1579,ze(n,812,o),De(n)),7&u?(f[t>>2]=866,f[t+4>>2]=2506,f[t+8>>2]=1232,ze(n,812,t),De(n),void(m=a)):(ge(u,0,0,1,0),void(m=a));m=a}function Ee(e,r,i){var n;return 0!=(0|(e|=0))&(r|=0)>>>0>73&0!=(0|(i|=0))?40!=(0|f[i>>2])||18552!=((0|c[e>>0])<<8|0|c[e+1>>0]|0)||((0|c[e+2>>0])<<8|0|c[e+3>>0])>>>0<74||((0|c[e+7>>0])<<16|(0|c[e+6>>0])<<24|(0|c[e+8>>0])<<8|0|c[e+9>>0])>>>0>r>>>0?0|(i=0):(f[i+4>>2]=(0|c[e+12>>0])<<8|0|c[e+13>>0],f[i+8>>2]=(0|c[e+14>>0])<<8|0|c[e+15>>0],f[i+12>>2]=c[e+16>>0],f[i+16>>2]=c[e+17>>0],r=e+18|0,f[(n=i+32|0)>>2]=c[r>>0],f[n+4>>2]=0,r=0|t[r>>0],f[i+20>>2]=r<<24>>24==0|r<<24>>24==9?8:16,f[i+24>>2]=(0|c[e+26>>0])<<16|(0|c[e+25>>0])<<24|(0|c[e+27>>0])<<8|0|c[e+28>>0],f[i+28>>2]=(0|c[e+30>>0])<<16|(0|c[e+29>>0])<<24|(0|c[e+31>>0])<<8|0|c[e+32>>0],0|(i=1)):0|(i=0)}function Me(e,r){e|=0;var i,n,t,o=0,a=0,u=0,l=0,s=0;if(t=m,m=m+544|0,s=t+16|0,o=t,l=t+32|0,(r|=0)>>>0>=33&&(f[o>>2]=866,f[o+4>>2]=3199,f[o+8>>2]=1350,ze(l,812,o),De(l)),(0|(o=0|f[(n=e+20|0)>>2]))>=(0|r))return u=a=e+16|0,l=o,s=(a=0|f[a>>2])>>>(s=32-r|0),a<<=r,f[u>>2]=a,r=l-r|0,f[n>>2]=r,m=t,0|s;a=e+4|0,u=e+8|0,i=e+16|0;do{(0|(e=0|f[a>>2]))==(0|f[u>>2])?e=0:(f[a>>2]=e+1,e=0|c[e>>0]),o=o+8|0,f[n>>2]=o,(0|o)>=33&&(f[s>>2]=866,f[s+4>>2]=3208,f[s+8>>2]=1366,ze(l,812,s),De(l),o=0|f[n>>2]),e=e<<32-o|f[i>>2],f[i>>2]=e}while((0|o)<(0|r));return s=e>>>(s=32-r|0),l=e<<r,f[i>>2]=l,r=o-r|0,f[n>>2]=r,m=t,0|s}function Te(e,r,i){e|=0;var n=0,o=0,a=0,u=0;a=255&(r|=0),n=0!=(0|(i|=0));e:do{if(n&0!=(3&e|0))for(o=255&r;;){if((0|t[e>>0])==o<<24>>24){u=6;break e}if(!((n=0!=(0|(i=i+-1|0)))&0!=(3&(e=e+1|0)|0))){u=5;break}}else u=5}while(0);5==(0|u)&&(n?u=6:i=0);e:do{if(6==(0|u)&&(o=255&r,(0|t[e>>0])!=o<<24>>24)){n=0|S(a,16843009);r:do{if(i>>>0>3){for(;!((-2139062144&(a=f[e>>2]^n)^-2139062144)&a+-16843009|0);)if(e=e+4|0,(i=i+-4|0)>>>0<=3){u=11;break r}}else u=11}while(0);if(11==(0|u)&&!i){i=0;break}for(;;){if((0|t[e>>0])==o<<24>>24)break e;if(e=e+1|0,!(i=i+-1|0)){i=0;break}}}}while(0);return 0|(0|i?e:0)}function he(e,r,i,n,t){r|=0,i|=0,n|=0,t|=0;var o,a,u,l=0,s=0,d=0;return u=m,m=m+528|0,d=u,s=u+16|0,o=0|f[(e|=0)+88>>2],a=(0|c[o+70+(t<<2)+1>>0])<<16|(0|c[o+70+(t<<2)>>0])<<24|(0|c[o+70+(t<<2)+2>>0])<<8|0|c[o+70+(t<<2)+3>>0],(l=(l=t+1|0)>>>0<(0|c[o+16>>0])>>>0?(0|c[o+70+(l<<2)+1>>0])<<16|(0|c[o+70+(l<<2)>>0])<<24|(0|c[o+70+(l<<2)+2>>0])<<8|0|c[o+70+(l<<2)+3>>0]:0|f[e+8>>2])>>>0>a>>>0?(d=0|ae(e,s=(s=0|f[(s=e+4|0)>>2])+a|0,d=l-a|0,r,i,n,t),m=u,0|d):(f[d>>2]=866,f[d+4>>2]=3694,f[d+8>>2]=1508,ze(s,812,d),De(s),d=0|ae(e,s=(s=0|f[(s=e+4|0)>>2])+a|0,d=l-a|0,r,i,n,t),m=u,0|d)}function Ae(e,r,i){e|=0;var n=0,t=0;if(!(0==(0|(r|=0))|(i|=0)>>>0<74||18552!=((0|c[r>>0])<<8|0|c[r+1>>0]|0))&&((0|c[r+2>>0])<<8|0|c[r+3>>0])>>>0>=74&&((0|c[r+7>>0])<<16|(0|c[r+6>>0])<<24|(0|c[r+8>>0])<<8|0|c[r+9>>0])>>>0<=i>>>0){if(f[(n=e+88|0)>>2]=r,f[e+4>>2]=r,f[e+8>>2]=i,!(0|function(e){var r,i=0,n=0,t=0;if(t=92+(e|=0)|0,n=0|f[(r=e+88|0)>>2],i=(0|f[e+4>>2])+((0|c[n+68>>0])<<8|(0|c[n+67>>0])<<16|0|c[n+69>>0])|0,!(n=(0|c[n+65>>0])<<8|0|c[n+66>>0]))return 0;if(f[t>>2]=i,f[e+96>>2]=i,f[e+104>>2]=n,f[e+100>>2]=i+n,f[e+108>>2]=0,f[e+112>>2]=0,!(0|K(t,e+116|0)))return 0;i=0|f[r>>2];do{if((0|c[i+39>>0])<<8|0|c[i+40>>0]){if(!(0|K(t,e+140|0)))return 0;if(0|K(t,e+188|0)){i=0|f[r>>2];break}return 0}if(!((0|c[i+55>>0])<<8|0|c[i+56>>0]))return 0}while(0);if((0|c[i+55>>0])<<8|0|c[i+56>>0]|0){if(!(0|K(t,e+164|0)))return 0;if(!(0|K(t,e+212|0)))return 0}return 1}(e)))return 0|(t=0);if(r=0|f[n>>2],(0|c[r+39>>0])<<8|0|c[r+40>>0]?0|$(e)&&0|Z(e)&&(r=0|f[n>>2],t=11):t=11,11==(0|t)){if(!((0|c[r+55>>0])<<8|0|c[r+56>>0]))return 0|(t=1);if(0|ne(e)&&0|J(e))return 0|(t=1)}return 0|(t=0)}return f[e+88>>2]=0,0|(t=0)}function be(e,r,i){e|=0,r|=0;var n=0,o=0,a=0,u=0,l=0;(o=0|f[(n=(i|=0)+16|0)>>2])?a=5:0|Ue(i)?n=0:(o=0|f[n>>2],a=5);e:do{if(5==(0|a)){if(n=u=0|f[(l=i+20|0)>>2],(o-u|0)>>>0<r>>>0){n=0|mr[7&f[i+36>>2]](i,e,r);break}r:do{if((0|t[i+75>>0])>-1){for(u=r;;){if(!u){a=0,o=e;break r}if(10==(0|t[e+(o=u+-1|0)>>0]))break;u=o}if((n=0|mr[7&f[i+36>>2]](i,e,u))>>>0<u>>>0)break e;a=u,o=e+u|0,r=r-u|0,n=0|f[l>>2]}else a=0,o=e}while(0);ue(0|n,0|o,0|r),f[l>>2]=(0|f[l>>2])+r,n=a+r|0}}while(0);return 0|n}function me(e,r,i){e|=0,r|=0,i|=0;do{if(e){if(r>>>0<128){t[e>>0]=r,e=1;break}if(i=188+(0|Tr())|0,!(0|f[f[i>>2]>>2])){if(57216==(-128&r|0)){t[e>>0]=r,e=1;break}e=0|Mr(),f[e>>2]=84,e=-1;break}if(r>>>0<2048){t[e>>0]=r>>>6|192,t[e+1>>0]=63&r|128,e=2;break}if(r>>>0<55296|57344==(-8192&r|0)){t[e>>0]=r>>>12|224,t[e+1>>0]=r>>>6&63|128,t[e+2>>0]=63&r|128,e=3;break}if((r+-65536|0)>>>0<1048576){t[e>>0]=r>>>18|240,t[e+1>>0]=r>>>12&63|128,t[e+2>>0]=r>>>6&63|128,t[e+3>>0]=63&r|128,e=4;break}e=0|Mr(),f[e>>2]=84,e=-1;break}e=1}while(0);return 0|e}function ve(e){var r,i,n=0,o=0,a=0;i=m,m=m+544|0,a=i+16|0,o=i,r=i+32|0,n=0|f[(e|=0)+20>>2];do{if(0|n){if(_e(n),7&n){f[o>>2]=866,f[o+4>>2]=2506,f[o+8>>2]=1232,ze(r,812,o),De(r);break}ge(n,0,0,1,0);break}}while(0);if(!(o=0|f[(n=e+4|0)>>2]))return t[(a=e+16|0)>>0]=0,void(m=i);7&o?(f[a>>2]=866,f[a+4>>2]=2506,f[a+8>>2]=1232,ze(r,812,a),De(r)):ge(o,0,0,1,0),f[n>>2]=0,f[e+8>>2]=0,f[e+12>>2]=0,t[(a=e+16|0)>>0]=0,m=i}function pe(e,r){r|=0;var i,n,t,o,a=0,u=0,l=0;return o=m,m=m+560|0,l=o+32|0,t=o+16|0,a=o,n=o+48|0,i=o+44|0,(u=0|(u=(e|=0)+3&-4)?u:4)>>>0>2147418112?(f[a>>2]=866,f[a+4>>2]=2506,f[a+8>>2]=1103,ze(n,812,a),De(n),m=o,0|(l=0)):(f[i>>2]=u,e=0|ge(0,u,i,1,0),a=0|f[i>>2],0|r&&(f[r>>2]=a),0==(0|e)|a>>>0<u>>>0?(f[t>>2]=866,f[t+4>>2]=2506,f[t+8>>2]=1129,ze(n,812,t),De(n),e=0):7&e&&(f[l>>2]=866,f[l+4>>2]=2533,f[l+8>>2]=1156,ze(n,812,l),De(n)),m=o,0|(l=e))}function Se(e,r,i,n){e|=0,r|=0,i|=0,n|=0;var o,a,u=0,l=0,c=0,s=0,d=0;a=m,m=m+128|0,u=a+124|0,c=604,o=(l=d=a)+124|0;do{f[l>>2]=f[c>>2],l=l+4|0,c=c+4|0}while((0|l)<(0|o));return(r+-1|0)>>>0>2147483646?r?(r=0|Mr(),f[r>>2]=75,r=-1):(e=u,r=1,s=4):s=4,4==(0|s)&&(s=r>>>0>(s=-2-e|0)>>>0?s:r,f[d+48>>2]=s,f[(u=d+20|0)>>2]=e,f[d+44>>2]=e,r=e+s|0,f[(e=d+16|0)>>2]=r,f[d+28>>2]=r,r=0|le(d,i,n),s&&(d=0|f[u>>2],t[d+(((0|d)==(0|f[e>>2]))<<31>>31)>>0]=0)),m=a,0|r}function ke(e,r,i){r|=0;var n,o=0,a=0,u=0;if(n=(e|=0)+(i|=0)|0,r&=255,(0|i)>=67){for(;3&e;)t[e>>0]=r,e=e+1|0;for(a=(o=-4&n|0)-64|0,u=r|r<<8|r<<16|r<<24;(0|e)<=(0|a);)f[e>>2]=u,f[e+4>>2]=u,f[e+8>>2]=u,f[e+12>>2]=u,f[e+16>>2]=u,f[e+20>>2]=u,f[e+24>>2]=u,f[e+28>>2]=u,f[e+32>>2]=u,f[e+36>>2]=u,f[e+40>>2]=u,f[e+44>>2]=u,f[e+48>>2]=u,f[e+52>>2]=u,f[e+56>>2]=u,f[e+60>>2]=u,e=e+64|0;for(;(0|e)<(0|o);)f[e>>2]=u,e=e+4|0}for(;(0|e)<(0|n);)t[e>>0]=r,e=e+1|0;return n-i|0}function Re(e,r,i,n,o){e|=0,i|=0,n|=0,o|=0;var a=0,u=0,l=0,c=0;t[(r|=0)+53>>0]=1;do{if((0|f[r+4>>2])==(0|n)){if(t[r+52>>0]=1,l=r+54|0,c=r+48|0,u=r+24|0,e=r+36|0,!(a=0|f[(n=r+16|0)>>2])){if(f[n>>2]=i,f[u>>2]=o,f[e>>2]=1,!(1==(0|f[c>>2])&1==(0|o)))break;t[l>>0]=1;break}if((0|a)!=(0|i)){f[e>>2]=1+(0|f[e>>2]),t[l>>0]=1;break}2==(0|(e=0|f[u>>2]))&&(f[u>>2]=o,e=o),1==(0|f[c>>2])&1==(0|e)&&(t[l>>0]=1)}}while(0)}function ye(e,r){e|=0;var i,n,o,a=0,u=0,l=0,s=0;o=m,m=m+16|0,n=255&(r|=0),t[(i=o)>>0]=n,(l=0|f[(u=e+16|0)>>2])?s=4:0|Ue(e)?a=-1:(l=0|f[u>>2],s=4);do{if(4==(0|s)){if((u=0|f[(s=e+20|0)>>2])>>>0<l>>>0&&(0|(a=255&r))!=(0|t[e+75>>0])){f[s>>2]=u+1,t[u>>0]=n;break}a=1==(0|mr[7&f[e+36>>2]](e,i,1))?0|c[i>>0]:-1}}while(0);return m=o,0|a}function Oe(e,r){var i,n=0,o=0,a=0,u=0;i=255&(e|=0),n=255&e,f[(r|=0)+76>>2],u=3;do{if(3==(0|u)){if((0|n)!=(0|t[r+75>>0])&&(a=0|f[(o=r+20|0)>>2])>>>0<(0|f[r+16>>2])>>>0){f[o>>2]=a+1,t[a>>0]=i;break}n=0|ye(r,e)}}while(0);return 0|n}function ge(e,r,i,n,t){e|=0,r|=0,i|=0,n|=0,t|=0;do{if(e){if(!r){if(W(e),!i){r=0;break}f[i>>2]=0,r=0;break}n?e=0==(0|(r=0|Ie(e,r)))?e:r:r=0,i&&(t=0|We(e),f[i>>2]=t)}else r=0|B(r),i&&(e=r?0|We(r):0,f[i>>2]=e)}while(0);return 0|r}function we(e){var r,i=0,n=0;r=e|=0;e:do{if(3&r)for(i=r;;){if(!(0|t[e>>0])){e=i;break e}if(!(3&(i=e=e+1|0))){n=4;break}}else n=4}while(0);if(4==(0|n)){for(;!((-2139062144&(i=0|f[e>>2])^-2139062144)&i+-16843009);)e=e+4|0;if((255&i)<<24>>24)do{e=e+1|0}while(0!=(0|t[e>>0]))}return e-r|0}function Ce(e,r){e=+e,r|=0;var i,n,t=0;switch(T[b>>3]=e,2047&(n=0|Je(0|(t=0|f[b>>2]),0|(i=0|f[b+4>>2]),52))){case 0:0!=e?(e=+Ce(0x10000000000000000*e,r),t=(0|f[r>>2])-64|0):t=0,f[r>>2]=t;break;case 2047:break;default:f[r>>2]=(2047&n)-1022,f[b>>2]=t,f[b+4>>2]=-2146435073&i|1071644672,e=+T[b>>3]}return+e}function Ne(e,r){e|=0,r|=0;var i=0,n=0;for(n=0;;){if((0|c[2140+n>>0])==(0|e)){e=2;break}if(87==(0|(i=n+1|0))){i=2228,n=87,e=5;break}n=i}if(2==(0|e)&&(n?(i=2228,e=5):i=2228),5==(0|e))for(;;){do{e=i,i=i+1|0}while(0!=(0|t[e>>0]));if(!(n=n+-1|0))break;e=5}return 0|function(e,r){return 0|function(e,r){e|=0,r=(r|=0)?0|de(0|f[r>>2],0|f[r+4>>2],e):0;return 0|(0|r?r:e)}(e|=0,r|=0)}(i,0|f[r+20>>2])}function Pe(e,r,i){i|=0;var n=0;if((r|=0)>>>0>0|0==(0|r)&(e|=0)>>>0>4294967295){for(;n=0|Ge(0|e,0|r,10,0),t[(i=i+-1|0)>>0]=255&n|48,n=e,e=0|ur(0|e,0|r,10,0),r>>>0>9|9==(0|r)&n>>>0>4294967295;)r=p;r=e}else r=e;if(r)for(;t[(i=i+-1|0)>>0]=48|(r>>>0)%10,!(r>>>0<10);)r=(r>>>0)/10|0;return 0|i}function Ie(e,r){r|=0;var i=0,n=0;return(e|=0)?r>>>0>4294967231?(r=0|Mr(),f[r>>2]=12,0|(r=0)):(i=0|function(e,r){r|=0;var i,n,t=0,o=0,a=0,u=0,l=0,c=0,s=0,d=0;if(i=(e|=0)+(t=-8&(s=0|f[(d=e+4|0)>>2]))|0,!(3&s))return r>>>0<256?0|(e=0):t>>>0>=(r+4|0)>>>0&&(t-r|0)>>>0<=f[1264]<<1>>>0?0|e:0|(e=0);if(t>>>0>=r>>>0)return(t=t-r|0)>>>0<=15||(c=e+r|0,f[d>>2]=1&s|r|2,f[c+4>>2]=3|t,f[(d=c+t+4|0)>>2]=1|f[d>>2],j(c,t)),0|e;if((0|i)==(0|f[1150]))return t=(c=(0|f[1147])+t|0)-r|0,o=e+r|0,c>>>0<=r>>>0?0|(e=0):(f[d>>2]=1&s|r|2,f[o+4>>2]=1|t,f[1150]=o,f[1147]=t,0|e);if((0|i)==(0|f[1149]))return(a=(0|f[1146])+t|0)>>>0<r>>>0?0|(e=0):(o=1&s,(t=a-r|0)>>>0>15?(c=(s=e+r|0)+t|0,f[d>>2]=o|r|2,f[s+4>>2]=1|t,f[c>>2]=t,f[(o=c+4|0)>>2]=-2&f[o>>2],o=s):(f[d>>2]=o|a|2,f[(o=e+a+4|0)>>2]=1|f[o>>2],o=0,t=0),f[1146]=t,f[1149]=o,0|e);if(2&(o=0|f[i+4>>2])|0)return 0;if((n=(-8&o)+t|0)>>>0<r>>>0)return 0;c=n-r|0,a=o>>>3;do{if(o>>>0<256){if(o=0|f[i+8>>2],(0|(t=0|f[i+12>>2]))==(0|o)){f[1144]=f[1144]&~(1<<a);break}f[o+12>>2]=t,f[t+8>>2]=o;break}l=0|f[i+24>>2],t=0|f[i+12>>2];do{if((0|t)==(0|i)){if(t=0|f[(o=4+(a=i+16|0)|0)>>2])u=o;else{if(!(t=0|f[a>>2])){a=0;break}u=a}for(;;)if(0|(o=0|f[(a=t+20|0)>>2]))t=o,u=a;else{if(!(a=0|f[(o=t+16|0)>>2]))break;t=a,u=o}f[u>>2]=0,a=t}else a=0|f[i+8>>2],f[a+12>>2]=t,f[t+8>>2]=a,a=t}while(0);if(0|l){if(t=0|f[i+28>>2],(0|i)==(0|f[(o=4880+(t<<2)|0)>>2])){if(f[o>>2]=a,!a){f[1145]=f[1145]&~(1<<t);break}}else if(f[l+16+(((0|f[l+16>>2])!=(0|i)&1)<<2)>>2]=a,!a)break;f[a+24>>2]=l,0|(o=0|f[(t=i+16|0)>>2])&&(f[a+16>>2]=o,f[o+24>>2]=a),0|(t=0|f[t+4>>2])&&(f[a+20>>2]=t,f[t+24>>2]=a)}}while(0);return t=1&s,c>>>0<16?(f[d>>2]=n|t|2,f[(d=e+n+4|0)>>2]=1|f[d>>2],0|e):(s=e+r|0,f[d>>2]=t|r|2,f[s+4>>2]=3|c,f[(d=s+c+4|0)>>2]=1|f[d>>2],j(s,c),0|e)}(e+-8|0,r>>>0<11?16:r+11&-8),0|i?0|(r=i+8|0):(i=0|B(r))?(ue(0|i,0|e,0|((n=(-8&(n=0|f[e+-4>>2]))-(0==(3&n|0)?8:4)|0)>>>0<r>>>0?n:r)),W(e),0|(r=i)):0|(r=0)):0|(r=0|B(r))}function Le(e,r,i,n){e|=0,i|=0,n|=0;var o,a,u;o=0|f[(e=(r|=0)+16|0)>>2],a=r+36|0,u=r+24|0;do{if(o){if((0|o)!=(0|i)){f[a>>2]=1+(0|f[a>>2]),f[u>>2]=2,t[r+54>>0]=1;break}2==(0|f[u>>2])&&(f[u>>2]=n)}else f[e>>2]=i,f[u>>2]=n,f[a>>2]=1}while(0)}function De(e){e|=0;var r,i=0,n=0;r=0|f[119],f[r+76>>2];do{if((0|rr(e,r))<0)e=1;else{if(10!=(0|t[r+75>>0])&&(n=0|f[(i=r+20|0)>>2])>>>0<(0|f[r+16>>2])>>>0){f[i>>2]=n+1,t[n>>0]=10,e=0;break}e=(0|ye(r,10))<0}}while(0);return e<<31>>31|0}function Fe(e,r,i,n,t){e|=0,r|=0;var o,a;if(a=m,m=m+256|0,o=a,(0|(i|=0))>(0|(n|=0))&0==(73728&(t|=0)|0)){if(ke(0|o,0|r,0|((t=i-n|0)>>>0<256?t:256)),t>>>0>255){r=i-n|0;do{ir(e,o,256),t=t+-256|0}while(t>>>0>255);t=255&r}ir(e,o,t)}m=a}function Ue(e){var r=0,i=0;return i=0|t[(r=(e|=0)+74|0)>>0],t[r>>0]=i+255|i,8&(r=0|f[e>>2])?(f[e>>2]=32|r,e=-1):(f[e+8>>2]=0,f[e+4>>2]=0,i=0|f[e+44>>2],f[e+28>>2]=i,f[e+20>>2]=i,f[e+16>>2]=i+(0|f[e+48>>2]),e=0),0|e}function He(e,r){r|=0;var i=0,n=0;if(i=0|t[(e|=0)>>0],n=0|t[r>>0],i<<24>>24==0||i<<24>>24!=n<<24>>24)e=n;else{do{r=r+1|0,i=0|t[(e=e+1|0)>>0],n=0|t[r>>0]}while(i<<24>>24!=0&&i<<24>>24==n<<24>>24);e=n}return(255&i)-(255&e)|0}function xe(e){var r,i;return(0|(i=(e|=0)+15&-16|0))>0&(0|(e=(r=0|f[A>>2])+i|0))<(0|r)|(0|e)<0?(g(),I(12),-1):(f[A>>2]=e,(0|e)>(0|O())&&0==(0|y())?(f[A>>2]=r,I(12),-1):0|r)}function Be(e){var r=0,i=0,n=0;if(i=0|f[(e|=0)>>2],(n=(0|t[i>>0])-48|0)>>>0<10){r=0;do{r=n+(10*r|0)|0,i=i+1|0,f[e>>2]=i,n=(0|t[i>>0])-48|0}while(n>>>0<10)}else r=0;return 0|r}function Ye(e,r,i,n){if(i|=0,n|=0,!(0==(0|(e|=0))&0==(0|(r|=0))))do{t[(i=i+-1|0)>>0]=0|c[2122+(15&e)>>0]|n,e=0|Je(0|e,0|r,4),r=p}while(!(0==(0|e)&0==(0|r)));return 0|i}function Xe(e){var r=0;return(0|(r=0|t[v+(255&(e|=0))>>0]))<8?0|r:(0|(r=0|t[v+(e>>8&255)>>0]))<8?r+8|0:(0|(r=0|t[v+(e>>16&255)>>0]))<8?r+16|0:24+(0|t[v+(e>>>24)>>0])|0}function Ke(e,r,i,n){i|=0,n|=0;var t=0;(0|f[(r|=0)+4>>2])==(0|i)&&1!=(0|f[(t=r+28|0)>>2])&&(f[t>>2]=n)}function Ve(e,r,i){if(i|=0,!(0==(0|(e|=0))&0==(0|(r|=0))))do{t[(i=i+-1|0)>>0]=7&e|48,e=0|Je(0|e,0|r,3),r=p}while(!(0==(0|e)&0==(0|r)));return 0|i}function Ge(e,r,i,n){var t,o;return o=m,m=m+16|0,Q(e|=0,r|=0,i|=0,n|=0,t=0|o),m=o,0|(p=0|f[t+4>>2],0|f[t>>2])}function We(e){var r=0;return(e|=0)?0|(1==(0|(e=3&(r=0|f[e+-4>>2])))?0:(-8&r)-(0==(0|e)?8:4)|0):0}function ze(e,r,i){e|=0,r|=0,i|=0;var n,t;return n=m,m=m+16|0,f[(t=n)>>2]=i,i=0|function(e,r,i){return 0|Se(e|=0,2147483647,r|=0,i|=0)}(e,r,t),m=n,0|i}function je(e,r,i){return e|=0,r|=0,(0|(i|=0))<32?(p=r<<i|(e&(1<<i)-1<<32-i)>>>32-i,e<<i):(p=e<<i-32,0)}function Je(e,r,i){return e|=0,r|=0,(0|(i|=0))<32?(p=r>>>i,e>>>i|(r&(1<<i)-1)<<32-i):(p=0,r>>>i-32|0)}function Ze(e,r){e|=0,r|=0;var i;i=m,m=m+16|0,f[i>>2]=r,le(r=0|f[26],e,i),Oe(10,r),L()}function qe(e,r,i,n){return 0|(p=n=(r|=0)-(n|=0)-((i|=0)>>>0>(e|=0)>>>0|0)>>>0,e-i>>>0|0)}function Qe(e){e=+e;var r;return T[b>>3]=e,r=0|f[b>>2],p=0|f[b+4>>2],0|r}function $e(e,r,i,n){return 0|(p=(r|=0)+(n|=0)+((i=(e|=0)+(i|=0)>>>0)>>>0<e>>>0|0)>>>0,0|i)}function er(e){var r=0;return(e|=0)>>>0>4294963200&&(r=0|Mr(),f[r>>2]=0-e,e=-1),0|e}function rr(e,r){r|=0;var i;return((0|function(e,r,i,n){var t;return e|=0,n|=0,t=0|S(i|=0,r|=0),i=0==(0|r)?0:i,(0|(f[n+76>>2],e=0|be(e,t,n)))!=(0|t)&&(i=(e>>>0)/(r>>>0)|0),0|i}(e|=0,1,i=0|we(e),r))!=(0|i))<<31>>31|0}function ir(e,r,i){r|=0,i|=0,32&f[(e|=0)>>2]||be(r,i,e)}function nr(e){e|=0;var r;return r=188+(0|Tr())|0,0|Ne(e,0|f[r>>2])}function tr(e,r){return r|=0,0|(e=(e|=0)?0|me(e,r,0):0)}function or(e,r,i){return(0|(e|=0))==(0|(r|=0))|0}function ar(e,r){r|=0;var i;return i=0|fr(0|(e|=0)),0|(0==(0|r)?e:i)}function ur(e,r,i,n){return 0|Q(e|=0,r|=0,i|=0,n|=0,0)}function fr(e){return(255&(e|=0))<<24|(e>>8&255)<<16|(e>>16&255)<<8|e>>>24|0}function lr(e,r,i,n,t,o){R(6)}function cr(e,r,i,n,t){R(1)}function sr(e){!function(e){W(e|=0)}(e|=0)}function dr(e,r,i,n){R(7)}function _r(e,r,i){return R(0),0}function Er(e,r){return+ +Ce(e=+e,r|=0)}function Mr(){return 64+(0|Tr())|0}function Tr(){return 232}function hr(e){}function Ar(e){R(2)}function br(){R(5)}r.__ZSt18uncaught_exceptionv;var mr=[_r,fe,function(e,r,i){var n,t,o;return e|=0,r|=0,i|=0,t=m,m=m+32|0,n=t+20|0,f[(o=t)>>2]=f[e+60>>2],f[o+4>>2]=0,f[o+8>>2]=r,f[o+12>>2]=n,f[o+16>>2]=i,(0|er(0|H(140,0|o)))<0?(f[n>>2]=-1,e=-1):e=0|f[n>>2],m=t,0|e},function(e,r,i){r|=0,i|=0;var n,o=0;return n=m,m=m+32|0,o=n,f[(e|=0)+36>>2]=1,0==(64&f[e>>2]|0)&&(f[o>>2]=f[e+60>>2],f[o+4>>2]=21523,f[o+8>>2]=n+16,0|C(54,0|o))&&(t[e+75>>0]=-1),o=0|fe(e,r,i),m=n,0|o},function(e,r,i){var n,t;return r|=0,i|=0,ue(0|(t=0|f[(n=(e|=0)+20|0)>>2]),0|r,0|(e=(e=(0|f[e+16>>2])-t|0)>>>0>i>>>0?i:e)),f[n>>2]=(0|f[n>>2])+e,0|i},function(e,r,i){i|=0;var n,t,o=0,a=0;if(t=m,m=m+64|0,n=t,0|or(e|=0,r|=0))r=1;else if(0!=(0|r)&&0!=(0|(a=0|ce(r,32,16,0)))){o=(r=n+4|0)+52|0;do{f[r>>2]=0,r=r+4|0}while((0|r)<(0|o));f[n>>2]=a,f[n+8>>2]=e,f[n+12>>2]=-1,f[n+48>>2]=1,Or[3&f[28+(0|f[a>>2])>>2]](a,n,0|f[i>>2],1),1==(0|f[n+24>>2])?(f[i>>2]=f[n+16>>2],r=1):r=0}else r=0;return m=t,0|r},_r,_r],vr=[cr,function(e,r,i,n,o){e|=0,r|=0,i|=0,n|=0;var a=0;do{if(0|or(e,0|f[r+8>>2]))Ke(0,r,i,n);else if(0|or(e,0|f[r>>2])){if(e=r+32|0,(0|f[r+16>>2])!=(0|i)&&(0|f[(a=r+20|0)>>2])!=(0|i)){f[e>>2]=n,f[a>>2]=i,f[(n=r+40|0)>>2]=1+(0|f[n>>2]),1==(0|f[r+36>>2])&&2==(0|f[r+24>>2])&&(t[r+54>>0]=1),f[r+44>>2]=4;break}1==(0|n)&&(f[e>>2]=1)}}while(0)},function(e,r,i,n,o){e|=0,r|=0,i|=0,n|=0,o|=0;var a=0,u=0,l=0,c=0;do{if(0|or(e,0|f[r+8>>2]))Ke(0,r,i,n);else{if(a=e+8|0,!(0|or(e,0|f[r>>2]))){l=0|f[a>>2],vr[3&f[24+(0|f[l>>2])>>2]](l,r,i,n,o);break}if(e=r+32|0,(0|f[r+16>>2])!=(0|i)&&(0|f[(u=r+20|0)>>2])!=(0|i)){if(f[e>>2]=n,4==(0|f[(n=r+44|0)>>2]))break;t[(e=r+52|0)>>0]=0,t[(c=r+53|0)>>0]=0,a=0|f[a>>2],yr[3&f[20+(0|f[a>>2])>>2]](a,r,i,i,1,o),0|t[c>>0]?0|t[e>>0]?e=3:(e=3,l=11):(e=4,l=11),11==(0|l)&&(f[u>>2]=i,f[(c=r+40|0)>>2]=1+(0|f[c>>2]),1==(0|f[r+36>>2])&&2==(0|f[r+24>>2])&&(t[r+54>>0]=1)),f[n>>2]=e;break}1==(0|n)&&(f[e>>2]=1)}}while(0)},cr],pr=[Ar,hr,sr,hr,hr,sr,function(e){var r;r=m,m=m+16|0,W(e|=0),0|N(0|f[1285],0)?Ze(4406,r):m=r},Ar],Sr=[function(e){return R(3),0},function(e){var r,i;return r=m,m=m+16|0,i=r,e=0|function(e){return 0|(e|=0)}(0|f[(e|=0)+60>>2]),f[i>>2]=e,e=0|er(0|P(6,0|i)),m=r,0|e}],kr=[function(e,r,i){R(4)}],Rr=[br,function(){var e,r,i,n=0,t=0,o=0,a=0,u=0;a=m,m=m+48|0,i=a+32|0,e=a+24|0,u=a+16|0,r=a,a=a+36|0,n=0|function(){var e=0,r=0;if(e=m,m=m+16|0,!(0|D(5136,2)))return r=0|w(0|f[1285]),m=e,0|r;Ze(4307,e);return 0}(),0|n&&0|(o=0|f[n>>2])&&(1126902528==(-256&(t=0|f[(n=o+48|0)>>2])|0)&1129074247==(0|(n=0|f[n+4>>2]))||(f[e>>2]=4168,Ze(4118,e)),n=1126902529==(0|t)&1129074247==(0|n)?0|f[o+44>>2]:o+80|0,f[a>>2]=n,o=0|f[o>>2],n=0|f[o+4>>2],0|mr[7&f[16+(0|f[2])>>2]](8,o,a)?(u=0|f[a>>2],u=0|Sr[1&f[8+(0|f[u>>2])>>2]](u),f[r>>2]=4168,f[r+4>>2]=n,f[r+8>>2]=u,Ze(4032,r)):(f[u>>2]=4168,f[u+4>>2]=n,Ze(4077,u))),Ze(4156,i)},function(){var e;e=m,m=m+16|0,0|U(5140,6)?Ze(4356,e):m=e},br],yr=[lr,function(e,r,i,n,t,o){i|=0,n|=0,t|=0,0|or(e|=0,0|f[(r|=0)+8>>2])&&Re(0,r,i,n,t)},function(e,r,i,n,t,o){i|=0,n|=0,t|=0,o|=0,0|or(e|=0,0|f[(r|=0)+8>>2])?Re(0,r,i,n,t):(e=0|f[e+8>>2],yr[3&f[20+(0|f[e>>2])>>2]](e,r,i,n,t,o))},lr],Or=[dr,function(e,r,i,n){i|=0,n|=0,0|or(e|=0,0|f[(r|=0)+8>>2])&&Le(0,r,i,n)},function(e,r,i,n){i|=0,n|=0,0|or(e|=0,0|f[(r|=0)+8>>2])?Le(0,r,i,n):(e=0|f[e+8>>2],Or[3&f[28+(0|f[e>>2])>>2]](e,r,i,n))},dr];return{stackSave:function(){return 0|m},_i64Subtract:qe,_crn_get_bytes_per_block:function(e,r){e|=0,r|=0;var i,n,t,o=0;switch(t=m,m=m+576|0,n=t+40|0,i=t+56|0,f[(o=t)>>2]=40,Ee(e,r,o),e=0|f[(r=o+32|0)+4>>2],0|f[r>>2]){case 0:case 9:case 10:if(!e)return m=t,0|(o=8);e=14;break;case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:e=e?14:13;break;default:e=14}return 13==(0|e)?(m=t,0|(o=16)):14==(0|e)?(f[n>>2]=866,f[n+4>>2]=2672,f[n+8>>2]=1251,ze(i,812,n),De(i),m=t,0|(o=0)):0},setThrew:function(e,r){},dynCall_viii:function(e,r,i,n){r|=0,i|=0,n|=0,kr[0&(e|=0)](0|r,0|i,0|n)},_bitshift64Lshr:Je,_bitshift64Shl:je,dynCall_viiii:function(e,r,i,n,t){r|=0,i|=0,n|=0,t|=0,Or[3&(e|=0)](0|r,0|i,0|n,0|t)},setTempRet0:function(e){p=e|=0},_crn_decompress:function(e,r,i,n,t,o){e|=0,r|=0,i|=0,n|=0,t|=0,o|=0;var a,u,l,c,s=0,d=0,_=0,E=0,M=0;switch(c=m,m=m+592|0,l=c+56|0,_=c+40|0,a=c+72|0,u=c+68|0,f[(M=c)>>2]=40,Ee(e,r,M),s=(0|f[M+4>>2])>>>t,d=(0|f[M+8>>2])>>>t,n=0|f[(M=M+32|0)+4>>2],0|f[M>>2]){case 0:case 9:case 10:n?E=14:M=8;break;case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:E=n?14:13;break;default:E=14}13==(0|E)?M=16:14==(0|E)&&(f[_>>2]=866,f[_+4>>2]=2672,f[_+8>>2]=1251,ze(a,812,_),De(a),M=0),f[u>>2]=i,E=0|oe(e,r),r=o+t|0;do{if(r>>>0>t>>>0){if(!E){for(n=i;n=n+(0|S(0|S((s+3|0)>>>2,M),(d+3|0)>>>2))|0,(0|(t=t+1|0))!=(0|r);)d>>>=1,s>>>=1;f[u>>2]=n;break}for(e=d,n=i;d=0|S((s+3|0)>>>2,M),t>>>0>15|(_=0|S(d,(e+3|0)>>>2))>>>0<8||519686845!=(0|f[E>>2])||(he(E,u,_,d,t),n=0|f[u>>2]),n=n+_|0,f[u>>2]=n,(0|(t=t+1|0))!=(0|r);)e>>>=1,s>>>=1}}while(0);if(E){if(519686845==(0|f[E>>2]))return re(E),7&E?(f[l>>2]=866,f[l+4>>2]=2506,f[l+8>>2]=1232,ze(a,812,l),De(a),void(m=c)):(ge(E,0,0,1,0),void(m=c));m=c}else m=c},_memset:ke,_sbrk:xe,_memcpy:ue,stackAlloc:function(e){var r;return r=m,m=(m=m+(e|=0)|0)+15&-16,0|r},_crn_get_height:function(e,r){var i,n;return e|=0,r|=0,n=m,m=m+48|0,f[(i=n)>>2]=40,Ee(e,r,i),m=n,0|f[i+8>>2]},dynCall_vi:function(e,r){r|=0,pr[7&(e|=0)](0|r)},getTempRet0:function(){return 0|p},_crn_get_levels:function(e,r){var i,n;return e|=0,r|=0,n=m,m=m+48|0,f[(i=n)>>2]=40,Ee(e,r,i),m=n,0|f[i+12>>2]},_crn_get_uncompressed_size:function(e,r,i){e|=0,r|=0,i|=0;var n,t,o,a,u=0,l=0;switch(a=m,m=m+576|0,o=a+40|0,t=a+56|0,f[(l=a)>>2]=40,Ee(e,r,l),n=(3+((0|f[l+4>>2])>>>i)|0)>>>2,r=(3+((0|f[l+8>>2])>>>i)|0)>>>2,e=0|f[(i=l+32|0)+4>>2],0|f[i>>2]){case 0:case 9:case 10:e?u=14:e=8;break;case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:u=e?14:13;break;default:u=14}return 13==(0|u)?e=16:14==(0|u)&&(f[o>>2]=866,f[o+4>>2]=2672,f[o+8>>2]=1251,ze(t,812,o),De(t),e=0),l=0|S(0|S(r,n),e),m=a,0|l},_i64Add:$e,dynCall_iiii:function(e,r,i,n){return r|=0,i|=0,n|=0,0|mr[7&(e|=0)](0|r,0|i,0|n)},_emscripten_get_global_libc:function(){return 5072},dynCall_ii:function(e,r){return r|=0,0|Sr[1&(e|=0)](0|r)},___udivdi3:ur,_llvm_bswap_i32:fr,dynCall_viiiii:function(e,r,i,n,t,o){r|=0,i|=0,n|=0,t|=0,o|=0,vr[3&(e|=0)](0|r,0|i,0|n,0|t,0|o)},___cxa_can_catch:function(e,r,i){var n,t;return e|=0,r|=0,i|=0,t=m,m=m+16|0,f[(n=t)>>2]=f[i>>2],(e=0|mr[7&f[16+(0|f[e>>2])>>2]](e,r,n))&&(f[i>>2]=f[n>>2]),m=t,1&e|0},_free:W,runPostSets:function(){},dynCall_viiiiii:function(e,r,i,n,t,o,a){r|=0,i|=0,n|=0,t|=0,o|=0,a|=0,yr[3&(e|=0)](0|r,0|i,0|n,0|t,0|o,0|a)},establishStackSpace:function(e,r){m=e|=0},___uremdi3:Ge,___cxa_is_pointer_type:function(e){return 1&(e=(e|=0)?0!=(0|ce(e,32,88,0)):0)|0},stackRestore:function(e){m=e|=0},_malloc:B,_emscripten_replace_memory:function(e){return!(16777215&h(e)||h(e)<=16777215||h(e)>2147483648)&&(t=new n(e),a=new o(e),f=new u(e),c=new l(e),d=new s(e),new _(e),new E(e),T=new M(e),i=e,!0)},dynCall_v:function(e){Rr[3&(e|=0)]()},_crn_get_width:function(e,r){var i,n;return e|=0,r|=0,n=m,m=m+48|0,f[(i=n)>>2]=40,Ee(e,r,i),m=n,0|f[i+4>>2]},_crn_get_dxt_format:function(e,r){var i,n;return e|=0,r|=0,n=m,m=m+48|0,f[(i=n)>>2]=40,Ee(e,r,i),m=n,0|f[i+32>>2]}}}(Module.asmGlobalArg,Module.asmLibraryArg,buffer);Module.stackSave=asm.stackSave,Module.getTempRet0=asm.getTempRet0,Module._memset=asm._memset,Module.setThrew=asm.setThrew,Module._bitshift64Lshr=asm._bitshift64Lshr,Module._bitshift64Shl=asm._bitshift64Shl,Module.setTempRet0=asm.setTempRet0,Module._crn_decompress=asm._crn_decompress,Module._crn_get_bytes_per_block=asm._crn_get_bytes_per_block,Module._sbrk=asm._sbrk,Module._memcpy=asm._memcpy,Module.stackAlloc=asm.stackAlloc,Module._crn_get_height=asm._crn_get_height,Module._i64Subtract=asm._i64Subtract,Module._crn_get_levels=asm._crn_get_levels,Module._crn_get_uncompressed_size=asm._crn_get_uncompressed_size,Module._i64Add=asm._i64Add,Module._emscripten_get_global_libc=asm._emscripten_get_global_libc,Module.___udivdi3=asm.___udivdi3,Module._llvm_bswap_i32=asm._llvm_bswap_i32,Module.___cxa_can_catch=asm.___cxa_can_catch;var _free=Module._free=asm._free;Module.runPostSets=asm.runPostSets,Module.establishStackSpace=asm.establishStackSpace,Module.___uremdi3=asm.___uremdi3,Module.___cxa_is_pointer_type=asm.___cxa_is_pointer_type,Module.stackRestore=asm.stackRestore;var _malloc=Module._malloc=asm._malloc,_emscripten_replace_memory=Module._emscripten_replace_memory=asm._emscripten_replace_memory,initialStackTop;function ExitStatus(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function run(e){function r(){Module.calledRun||(Module.calledRun=!0,ABORT||(ensureInitRuntime(),preMain(),Module.onRuntimeInitialized&&Module.onRuntimeInitialized(),Module._main&&shouldRunNow&&Module.callMain(e),postRun()))}e=e||Module.arguments,runDependencies>0||(preRun(),runDependencies>0||Module.calledRun||(Module.setStatus?(Module.setStatus("Running..."),setTimeout((function(){setTimeout((function(){Module.setStatus("")}),1),r()}),1)):r()))}function exit(e,r){r&&Module.noExitRuntime||(Module.noExitRuntime||(ABORT=!0,STACKTOP=initialStackTop,exitRuntime(),Module.onExit&&Module.onExit(e)),ENVIRONMENT_IS_NODE&&process.exit(e),Module.quit(e,new ExitStatus(e)))}Module._crn_get_width=asm._crn_get_width,Module._crn_get_dxt_format=asm._crn_get_dxt_format,Module.dynCall_iiii=asm.dynCall_iiii,Module.dynCall_viiiii=asm.dynCall_viiiii,Module.dynCall_vi=asm.dynCall_vi,Module.dynCall_ii=asm.dynCall_ii,Module.dynCall_viii=asm.dynCall_viii,Module.dynCall_v=asm.dynCall_v,Module.dynCall_viiiiii=asm.dynCall_viiiiii,Module.dynCall_viiii=asm.dynCall_viiii,Runtime.stackAlloc=Module.stackAlloc,Runtime.stackSave=Module.stackSave,Runtime.stackRestore=Module.stackRestore,Runtime.establishStackSpace=Module.establishStackSpace,Runtime.setTempRet0=Module.setTempRet0,Runtime.getTempRet0=Module.getTempRet0,Module.asm=asm,ExitStatus.prototype=new Error,ExitStatus.prototype.constructor=ExitStatus,dependenciesFulfilled=function e(){Module.calledRun||run(),Module.calledRun||(dependenciesFulfilled=e)},Module.callMain=Module.callMain=function(e){e=e||[],ensureInitRuntime();var r=e.length+1;function i(){for(var e=0;e<3;e++)n.push(0)}var n=[allocate(intArrayFromString(Module.thisProgram),"i8",ALLOC_NORMAL)];i();for(var t=0;t<r-1;t+=1)n.push(allocate(intArrayFromString(e[t]),"i8",ALLOC_NORMAL)),i();n.push(0),n=allocate(n,"i32",ALLOC_NORMAL);try{exit(Module._main(r,n,0),!0)}catch(e){if(e instanceof ExitStatus)return;if("SimulateInfiniteLoop"==e)return void(Module.noExitRuntime=!0);var o=e;e&&"object"==typeof e&&e.stack&&(o=[e,e.stack]),Module.printErr("exception thrown: "+o),Module.quit(1,e)}},Module.run=Module.run=run,Module.exit=Module.exit=exit;var abortDecorators=[];function abort(e){Module.onAbort&&Module.onAbort(e),void 0!==e?(Module.print(e),Module.printErr(e),e=JSON.stringify(e)):e="",ABORT=!0;var r="abort("+e+") at "+stackTrace()+"\nIf this abort() is unexpected, build with -s ASSERTIONS=1 which can give more information.";throw abortDecorators&&abortDecorators.forEach((function(i){r=i(r,e)})),r}if(Module.abort=Module.abort=abort,Module.preInit)for("function"==typeof Module.preInit&&(Module.preInit=[Module.preInit]);Module.preInit.length>0;)Module.preInit.pop()();var shouldRunNow=!0;Module.noInitialRun&&(shouldRunNow=!1),Module.noExitRuntime=!0,run();var crunch=Module,CRN_FORMAT={cCRNFmtInvalid:-1,cCRNFmtDXT1:0,cCRNFmtDXT3:1,cCRNFmtDXT5:2},DXT_FORMAT_MAP={},dst,dxtData;
/**
     * @license
     *
     * Copyright (c) 2014, Brandon Jones. All rights reserved.
     *
     * Redistribution and use in source and binary forms, with or without modification,
     * are permitted provided that the following conditions are met:
     *
     *  * Redistributions of source code must retain the above copyright notice, this
     *  list of conditions and the following disclaimer.
     *  * Redistributions in binary form must reproduce the above copyright notice,
     *  this list of conditions and the following disclaimer in the documentation
     *  and/or other materials provided with the distribution.
     *
     * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
     * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
     * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
     * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR
     * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
     * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
     * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
     * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
     * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
     * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
     */DXT_FORMAT_MAP[CRN_FORMAT.cCRNFmtDXT1]=PixelFormat.PixelFormat.RGB_DXT1,DXT_FORMAT_MAP[CRN_FORMAT.cCRNFmtDXT3]=PixelFormat.PixelFormat.RGBA_DXT3,DXT_FORMAT_MAP[CRN_FORMAT.cCRNFmtDXT5]=PixelFormat.PixelFormat.RGBA_DXT5;var cachedDstSize=0;function arrayBufferCopy(e,r,i,n){var t,o=i/4,a=n%4,u=new Uint32Array(e.buffer,0,(n-a)/4),f=new Uint32Array(r.buffer);for(t=0;t<u.length;t++)f[o+t]=u[t];for(t=n-a;t<n;t++)r[i+t]=e[t]}function transcodeCRNToDXT(e,r){var i=e.byteLength,n=new Uint8Array(e),t=crunch._malloc(i);arrayBufferCopy(n,crunch.HEAPU8,t,i);var o=crunch._crn_get_dxt_format(t,i),a=DXT_FORMAT_MAP[o];if(!when.defined(a))throw new RuntimeError.RuntimeError("Unsupported compressed format.");var u,f=crunch._crn_get_levels(t,i),l=crunch._crn_get_width(t,i),c=crunch._crn_get_height(t,i),s=0;for(u=0;u<f;++u)s+=PixelFormat.PixelFormat.compressedTextureSizeInBytes(a,l>>u,c>>u);cachedDstSize<s&&(when.defined(dst)&&crunch._free(dst),dst=crunch._malloc(s),dxtData=new Uint8Array(crunch.HEAPU8.buffer,dst,s),cachedDstSize=s),crunch._crn_decompress(t,i,dst,s,0,f),crunch._free(t);var d=PixelFormat.PixelFormat.compressedTextureSizeInBytes(a,l,c),_=dxtData.subarray(0,d),E=new Uint8Array(d);return E.set(_,0),r.push(E.buffer),new CompressedTextureBuffer.CompressedTextureBuffer(a,l,c,E)}var transcodeCRNToDXTprevious=createTaskProcessorWorker(transcodeCRNToDXT);return transcodeCRNToDXTprevious}));
