import linePng from "../../../assets/images/space_line.png";
const defaultColor = Cesium.Color.DEEPSKYBLUE;
const defaultSpeed = 4;

function LineTrailWallMaterialProperty(options) {
  options = Cesium.defaultValue(options, Cesium.defaultValue.EMPTY_OBJECT);

  this._definitionChanged = new Cesium.Event();
  this._color = undefined;
  this._colorSubscription = undefined;
  this._speed = undefined;
  this._speedSubscription = undefined;

  this.color = options.color;
  this.speed = options.speed;
}

Object.defineProperties(LineTrailWallMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return Cesium.Property.isConstant(this._color) && Cesium.Property.isConstant(this._speed);
    },
  },
  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    },
  },
  color: Cesium.createPropertyDescriptor("color"),
  speed: Cesium.createPropertyDescriptor("speed"),
});

LineTrailWallMaterialProperty.prototype.getType = function (time) {
  return "LineTrailWall";
};

LineTrailWallMaterialProperty.prototype.getValue = function (time, result) {
  if (!Cesium.defined(result)) {
    result = {};
  }
  result.color = Cesium.Property.getValueOrClonedDefault(this._color, time, defaultColor, result.color);
  result.speed = Cesium.Property.getValueOrClonedDefault(this._speed, time, defaultSpeed, result.speed);
  return result;
};

LineTrailWallMaterialProperty.prototype.equals = function (other) {
  return (
    this === other || //
    (other instanceof LineTrailWallMaterialProperty && //
      Cesium.Property.equals(this._color, other._color) && //
      Cesium.Property.equals(this._speed, other._speed))
  );
};

Cesium.LineTrailWallMaterialProperty = LineTrailWallMaterialProperty;

const type = "LineTrailWall";

const source = `
uniform sampler2D image;
uniform float speed;
uniform vec4 color;
uniform vec2 repeat;

czm_material czm_getMaterial(czm_materialInput materialInput){
  czm_material material = czm_getDefaultMaterial(materialInput);
  float perDis = 1.0 / repeat.y / 3.0  ;
  vec2 st = materialInput.st * repeat;
  float time = fract(czm_frameNumber * speed / 1000.0);
  vec4 colorImage = texture2D(image, vec2(st.s, fract(st.t - time)));
  material.alpha =  colorImage.a * smoothstep(.2 ,1. ,distance(st.t * perDis ,1. + perDis ));
  material.diffuse = max(color.rgb * material.alpha * 1.5, color.rgb);
  material.emission = max(color.rgb * material.alpha * 1.5, color.rgb);
  return material;
}
`;

Cesium.Material._materialCache.addMaterial(type, {
  fabric: {
    type,
    uniforms: {
      color: defaultColor,
      image: linePng,
      speed: defaultSpeed,
      repeat: new Cesium.Cartesian2(2, 2),
    },
    source,
  },
  translucent: function (material) {
    return true;
  },
});
