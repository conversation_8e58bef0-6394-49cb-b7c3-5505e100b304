import { request } from "./request";

export function getGrainData(params) {
  return request.get("/api/grain", {
    params,
  });
}

export function getVillageData(params) {
  return request.get("/api/village", {
    params,
  });
}

export function getChartData(params) {
  return request.get("/api/chart", {
    params,
  });
}

export function getMapData(params) {
  return request.get("/api/map", {
    params,
  });
}

export function getGeoServerData(data) {
  return request.post("/geoserver/zhdd/wfs", data, { headers: { "Content-Type": "text/plain; charset=UTF-8" } });
}

export function getJiangtuanMapData(params) {
  return request.get("/api/jiangtuanmap", {
    params,
  });
}

export function getMarkDetail(id) {
  return request.get(`/api/dialog/${id}`);
}

export function getInfo() {
  return request.get("/getInfo");
}

//获取本地动态页面配置
export function getLocalDynamicPage() {
  return request.get("/config/dynamicPage.json");
}
//获取动态页面配置
export function getDynamicPage(id) {
  return request.get(`/api/dynamicPage/${id}`);
}
//同步动态页面配置
export function syncDynamicPage(id, pages) {
  return request.post(`/api/dynamicPage/${id}`, pages);
}
