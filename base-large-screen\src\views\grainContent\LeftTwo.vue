<template>
  <div class="box">
    <GrainPartTitle title="库存信息" :position="1" />
    <div class="block-title">
      <div class="text">成品模块</div>
    </div>
    <div class="line">
      <div class="item" v-for="(item, index) in pageData.inventory && pageData.inventory.product">
        <div class="value">{{ item.value }}</div>
        <img class="icon" :src="item.icon" alt="icon" />
        <div class="name">{{ item.name }}</div>
      </div>
    </div>
    <div class="block-title">
      <div class="text">生产材料</div>
    </div>
    <div class="line">
      <div class="item" v-for="(item, index) in pageData.inventory && pageData.inventory.material">
        <div class="value">{{ item.value }}</div>
        <img class="icon" :src="item.icon" alt="icon" />
        <div class="name">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>
<script setup>
import GrainPartTitle from "../../components/common/GrainPartTitle.vue"
import { useGrainStore } from "../../stores/modules/grain";

const { pageData } = useGrainStore()
</script>
<style scoped lang="scss">
.box {
  height: 304px;
  width: 450px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.block-title {
  width: 190px;
  height: 24px;
  background: linear-gradient(90deg, #8FCEFB 0%, #2983C3 1%, rgba(60, 124, 189, 0) 100%);
  display: flex;
  align-items: center;
  padding-left: 16px;
  margin-left: 26px;
  margin-top: 4px;

  .text {
    font-size: 14px;
    font-family: Alibaba PuHuiTi;
    font-weight: 400;
    color: #F4FAFE;

    background: linear-gradient(0deg, #C6F5FF 0%, #E5FAFF 93.5791015625%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.line {
  width: 368px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-left: 34px;

  .item {
    width: 87px;
    height: 104px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .value {
      font-size: 17px;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;

      background: linear-gradient(179deg, rgba(40, 217, 255, 0.5) 0%, rgb(255, 255, 255) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .icon {
      width: 87px;
      height: 66px;
      margin-top: -12px;
    }

    .name {
      font-size: 13px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #F4FAFE;
    }
  }
}
</style>