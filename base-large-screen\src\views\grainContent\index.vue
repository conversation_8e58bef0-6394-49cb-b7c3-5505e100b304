<template>
  <LayoutBox :parts="parts" :focus="true" focusColor="#01375d"></LayoutBox>
</template>
<script setup>
import Title from './Title.vue'
import TimeWeather from './TimeWeather.vue'
import LeftOne from './LeftOne.vue'
import LeftTwo from './LeftTwo.vue'
import LeftThree from './LeftThree.vue'
import BottomLeft from './BottomLeft.vue'
import BottomRight from './BottomRight.vue'
import RightOne from './RightOne.vue'
import RightTwo from './RightTwo.vue'
import RightThree from './RightThree.vue'

const parts = [
  {
    component: Title,
    positionStyle: {
      top: "32px"
    },
    animation: {
      type: "TopSlide",
      startTime: 0,
    }
  },
  {
    component: TimeWeather,
    positionStyle: {
      top: "34px",
      right: "61px"
    },
    animation: {
      type: "TopSlide",
      startTime: 0,
    }
  },
  {
    component: LeftOne,
    positionStyle: {
      top: "88px",
      left: "41px"
    },
    animation: {
      type: "TopSlide",
      startTime: 0,
    }
  },
  {
    component: LeftTwo,
    positionStyle: {
      top: "477px",
      left: "41px"
    },
    animation: {
      type: "TopSlide",
      startTime: 0.5,
    }
  },
  {
    component: LeftThree,
    positionStyle: {
      top: "782px",
      left: "41px"
    },
    animation: {
      type: "TopSlide",
      startTime: 1,
    }
  },
  {
    component: BottomLeft,
    positionStyle: {
      top: "782px",
      left: "504px"
    },
    animation: {
      type: "LeftSlide",
      startTime: 1.5,
    }
  },
  {
    component: BottomRight,
    positionStyle: {
      top: "782px",
      right: "504px"
    },
    animation: {
      type: "RightSlide",
      startTime: 1.5,
    }
  },
  {
    component: RightOne,
    positionStyle: {
      top: "88px",
      right: "41px"
    },
    animation: {
      type: "TopSlide",
      startTime: 0,
    }
  },
  {
    component: RightTwo,
    positionStyle: {
      top: "477px",
      right: "41px"
    },
    animation: {
      type: "TopSlide",
      startTime: 0.5,
    }
  },
  {
    component: RightThree,
    positionStyle: {
      top: "782px",
      right: "41px"
    },
    animation: {
      type: "TopSlide",
      startTime: 1,
    }
  }
]

</script>
<style scoped>
@font-face {
  font-family: YouSheBiaoTiHei;
  src: url('../../assets/fonts/YouSheBiaoTiHei-2.ttf')
}
</style>