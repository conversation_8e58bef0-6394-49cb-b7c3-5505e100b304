//This file is automatically rebuilt by the Cesium build process.
export default "/**\n\
 * A built-in GLSL floating-point constant for <code>pi/4</code>.\n\
 *\n\
 * @alias czm_piOverFour\n\
 * @glslConstant\n\
 *\n\
 * @see CesiumMath.PI_OVER_FOUR\n\
 *\n\
 * @example\n\
 * // GLSL declaration\n\
 * const float czm_piOverFour = ...;\n\
 *\n\
 * // Example\n\
 * float pi = 4.0 * czm_piOverFour;\n\
 */\n\
const float czm_piOverFour = 0.7853981633974483;\n\
";
