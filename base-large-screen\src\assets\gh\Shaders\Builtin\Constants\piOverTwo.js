//This file is automatically rebuilt by the Cesium build process.
export default "/**\n\
 * A built-in GLSL floating-point constant for <code>pi/2</code>.\n\
 *\n\
 * @alias czm_piOverTwo\n\
 * @glslConstant\n\
 *\n\
 * @see CesiumMath.PI_OVER_TWO\n\
 *\n\
 * @example\n\
 * // GLSL declaration\n\
 * const float czm_piOverTwo = ...;\n\
 *\n\
 * // Example\n\
 * float pi = 2.0 * czm_piOverTwo;\n\
 */\n\
const float czm_piOverTwo = 1.5707963267948966;\n\
";
