define(["exports","./ArcType-29cf2197","./arrayRemoveDuplicates-d2f048c5","./Cartesian2-47311507","./Cartographic-3309dd0d","./ComponentDatatype-c140a87d","./when-b60132fc","./EllipsoidRhumbLine-ed1a6bf4","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./GeometryPipeline-44c6c124","./IndexDatatype-8a5eead4","./Math-119be1a3","./FeatureDetection-806b12f0","./PolygonPipeline-d328cdf1"],(function(e,t,r,i,n,a,o,s,u,l,h,c,f,p,d){"use strict";function y(){this._array=[],this._offset=0,this._length=0}Object.defineProperties(y.prototype,{length:{get:function(){return this._length}}}),y.prototype.enqueue=function(e){this._array.push(e),this._length++},y.prototype.dequeue=function(){if(0!==this._length){var e=this._array,t=this._offset,r=e[t];return e[t]=void 0,++t>10&&2*t>e.length&&(this._array=e.slice(t),t=0),this._offset=t,this._length--,r}},y.prototype.peek=function(){if(0!==this._length)return this._array[this._offset]},y.prototype.contains=function(e){return-1!==this._array.indexOf(e)},y.prototype.clear=function(){this._array.length=this._offset=this._length=0},y.prototype.sort=function(e){this._offset>0&&(this._array=this._array.slice(this._offset),this._offset=0),this._array.sort(e)};var g={computeHierarchyPackedLength:function(e){for(var t=0,r=[e];r.length>0;){var i=r.pop();if(o.defined(i)){t+=2;var a=i.positions,s=i.holes;if(o.defined(a)&&(t+=a.length*n.Cartesian3.packedLength),o.defined(s))for(var u=s.length,l=0;l<u;++l)r.push(s[l])}}return t},packPolygonHierarchy:function(e,t,r){for(var i=[e];i.length>0;){var a=i.pop();if(o.defined(a)){var s=a.positions,u=a.holes;if(t[r++]=o.defined(s)?s.length:0,t[r++]=o.defined(u)?u.length:0,o.defined(s))for(var l=s.length,h=0;h<l;++h,r+=3)n.Cartesian3.pack(s[h],t,r);if(o.defined(u))for(var c=u.length,f=0;f<c;++f)i.push(u[f])}}return r},unpackPolygonHierarchy:function(e,t){for(var r=e[t++],i=e[t++],a=new Array(r),o=i>0?new Array(i):void 0,s=0;s<r;++s,t+=n.Cartesian3.packedLength)a[s]=n.Cartesian3.unpack(e,t);for(var u=0;u<i;++u)o[u]=g.unpackPolygonHierarchy(e,t),t=o[u].startingIndex,delete o[u].startingIndex;return{positions:a,holes:o,startingIndex:t}}},v=new n.Cartesian3;function m(e,t,r,i){return n.Cartesian3.subtract(t,e,v),n.Cartesian3.multiplyByScalar(v,r/i,v),n.Cartesian3.add(e,v,v),[v.x,v.y,v.z]}g.subdivideLineCount=function(e,t,r){var i=n.Cartesian3.distance(e,t)/r,a=Math.max(0,Math.ceil(f.CesiumMath.log2(i)));return Math.pow(2,a)};var C=new n.Cartographic,b=new n.Cartographic,w=new n.Cartographic,I=new n.Cartesian3;g.subdivideRhumbLineCount=function(e,t,r,i){var n=e.cartesianToCartographic(t,C),a=e.cartesianToCartographic(r,b),o=new s.EllipsoidRhumbLine(n,a,e).surfaceDistance/i,u=Math.max(0,Math.ceil(f.CesiumMath.log2(o)));return Math.pow(2,u)},g.subdivideLine=function(e,t,r,i){var a=g.subdivideLineCount(e,t,r),s=n.Cartesian3.distance(e,t),u=s/a;o.defined(i)||(i=[]);var l=i;l.length=3*a;for(var h=0,c=0;c<a;c++){var f=m(e,t,c*u,s);l[h++]=f[0],l[h++]=f[1],l[h++]=f[2]}return l},g.subdivideRhumbLine=function(e,t,r,i,n){var a=e.cartesianToCartographic(t,C),u=e.cartesianToCartographic(r,b),l=new s.EllipsoidRhumbLine(a,u,e),h=l.surfaceDistance/i,c=Math.max(0,Math.ceil(f.CesiumMath.log2(h))),p=Math.pow(2,c),d=l.surfaceDistance/p;o.defined(n)||(n=[]);var y=n;y.length=3*p;for(var g=0,v=0;v<p;v++){var m=l.interpolateUsingSurfaceDistance(v*d,w),T=e.cartographicToCartesian(m,I);y[g++]=T.x,y[g++]=T.y,y[g++]=T.z}return y};var T=new n.Cartesian3,E=new n.Cartesian3,P=new n.Cartesian3,x=new n.Cartesian3;g.scaleToGeodeticHeightExtruded=function(e,t,r,a,s){a=o.defaultValue(a,i.Ellipsoid.WGS84);var u=T,l=E,h=P,c=x;if(o.defined(e)&&o.defined(e.attributes)&&o.defined(e.attributes.position))for(var f=e.attributes.position.values,p=f.length/2,d=0;d<p;d+=3)n.Cartesian3.fromArray(f,d,h),a.geodeticSurfaceNormal(h,u),c=a.scaleToGeodeticSurface(h,c),l=n.Cartesian3.multiplyByScalar(u,r,l),l=n.Cartesian3.add(c,l,l),f[d+p]=l.x,f[d+1+p]=l.y,f[d+2+p]=l.z,s&&(c=n.Cartesian3.clone(h,c)),l=n.Cartesian3.multiplyByScalar(u,t,l),l=n.Cartesian3.add(c,l,l),f[d]=l.x,f[d+1]=l.y,f[d+2]=l.z;return e},g.polygonOutlinesFromHierarchy=function(e,t,i){var a,s,u,l=[],h=new y;for(h.enqueue(e);0!==h.length;){var c=h.dequeue(),f=c.positions;if(t)for(u=f.length,a=0;a<u;a++)i.scaleToGeodeticSurface(f[a],f[a]);if(!((f=r.arrayRemoveDuplicates(f,n.Cartesian3.equalsEpsilon,!0)).length<3)){var p=c.holes?c.holes.length:0;for(a=0;a<p;a++){var d=c.holes[a],g=d.positions;if(t)for(u=g.length,s=0;s<u;++s)i.scaleToGeodeticSurface(g[s],g[s]);if(!((g=r.arrayRemoveDuplicates(g,n.Cartesian3.equalsEpsilon,!0)).length<3)){l.push(g);var v=0;for(o.defined(d.holes)&&(v=d.holes.length),s=0;s<v;s++)h.enqueue(d.holes[s])}}l.push(f)}}return l};var _=new n.Cartesian3(6378137,6378137,6378137);g.polygonsFromHierarchy=function(e,t,i,a){var s=[],u=[],l=new y;for(l.enqueue(e);0!==l.length;){var h,c,p,g=l.dequeue(),v=g.positions,m=g.holes,C=v.slice();if(i)for(c=v.length,h=0;h<c;h++)a.scaleToGeodeticSurface(v[h],C[h]);if(o.defined(a)&&!n.Cartesian3.equals(a._radii,_)&&(p=f.CesiumMath.EPSILON7),!((v=r.arrayRemoveDuplicates(C,n.Cartesian3.equalsEpsilon,!0,p)).length<3)){var b=t(v);if(o.defined(b)){var w=[],I=d.PolygonPipeline.computeWindingOrder2D(b);I===d.WindingOrder.CLOCKWISE&&(b.reverse(),v=v.slice().reverse());var T,E=v.slice(),P=o.defined(m)?m.length:0,x=[];for(h=0;h<P;h++){var A=m[h],L=A.positions;if(i)for(c=L.length,T=0;T<c;++T)a.scaleToGeodeticSurface(L[T],L[T]);if(!((L=r.arrayRemoveDuplicates(L,n.Cartesian3.equalsEpsilon,!0,f.CesiumMath.EPSILON7)).length<3)){var M=t(L);if(o.defined(M)){(I=d.PolygonPipeline.computeWindingOrder2D(M))===d.WindingOrder.CLOCKWISE&&(M.reverse(),L=L.slice().reverse()),x.push(L),w.push(E.length),E=E.concat(L),b=b.concat(M);var D=0;for(o.defined(A.holes)&&(D=A.holes.length),T=0;T<D;T++)l.enqueue(A.holes[T])}}}s.push({outerRing:v,holes:x}),u.push({positions:E,positions2D:b,holes:w})}}}return{hierarchy:s,polygons:u}};var A=new i.Cartesian2,L=new n.Cartesian3,M=new u.Quaternion,D=new p.Matrix3;g.computeBoundingRectangle=function(e,t,r,i,a){for(var s=u.Quaternion.fromAxisAngle(e,i,M),l=p.Matrix3.fromQuaternion(s,D),h=Number.POSITIVE_INFINITY,c=Number.NEGATIVE_INFINITY,f=Number.POSITIVE_INFINITY,d=Number.NEGATIVE_INFINITY,y=r.length,g=0;g<y;++g){var v=n.Cartesian3.clone(r[g],L);p.Matrix3.multiplyByVector(l,v,v);var m=t(v,A);o.defined(m)&&(h=Math.min(h,m.x),c=Math.max(c,m.x),f=Math.min(f,m.y),d=Math.max(d,m.y))}return a.x=h,a.y=f,a.width=c-h,a.height=d-f,a},g.createGeometryFromPositions=function(e,r,i,n,o,s){var l=d.PolygonPipeline.triangulate(r.positions2D,r.holes);l.length<3&&(l=[0,1,2]);var c=r.positions;if(n){for(var f=c.length,y=new Array(3*f),g=0,v=0;v<f;v++){var m=c[v];y[g++]=m.x,y[g++]=m.y,y[g++]=m.z}var C=new u.Geometry({attributes:{position:new u.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:y})},indices:l,primitiveType:p.PrimitiveType.TRIANGLES});return o.normal?h.GeometryPipeline.computeNormal(C):C}return s===t.ArcType.GEODESIC?d.PolygonPipeline.computeSubdivision(e,c,l,i):s===t.ArcType.RHUMB?d.PolygonPipeline.computeRhumbLineSubdivision(e,c,l,i):void 0};var G=[],S=new n.Cartesian3,N=new n.Cartesian3;g.computeWallGeometry=function(e,r,i,o,s,h){var d,y,v,m,C,b=h?1:0,w=e.length,I=0;if(o)for(y=3*(w-b)*2,d=new Array(2*y),v=0;v<w-b;v++)m=e[v],C=e[(v+1)%w],d[I]=d[I+y]=m.x,d[++I]=d[I+y]=m.y,d[++I]=d[I+y]=m.z,d[++I]=d[I+y]=C.x,d[++I]=d[I+y]=C.y,d[++I]=d[I+y]=C.z,++I;else{var T=f.CesiumMath.chordLength(i,r.maximumRadius),E=0;if(s===t.ArcType.GEODESIC)for(v=0;v<w;v++)E+=g.subdivideLineCount(e[v],e[(v+1)%w],T);else if(s===t.ArcType.RHUMB)for(v=0;v<w;v++)E+=g.subdivideRhumbLineCount(r,e[v],e[(v+1)%w],T);for(y=3*(E+w),d=new Array(2*y),v=0;v<w;v++){var P;m=e[v],C=e[(v+1)%w],s===t.ArcType.GEODESIC?P=g.subdivideLine(m,C,T,G):s===t.ArcType.RHUMB&&(P=g.subdivideRhumbLine(r,m,C,T,G));for(var x=P.length,_=0;_<x;++_,++I)d[I]=P[_],d[I+y]=P[_];d[I]=C.x,d[I+y]=C.x,d[++I]=C.y,d[I+y]=C.y,d[++I]=C.z,d[I+y]=C.z,++I}}w=d.length;var A=c.IndexDatatype.createTypedArray(w/3,w-6*(e.length-b)),L=0;for(w/=6,v=0;v<w;v++){var M=v,D=M+1,R=M+w,O=R+1;m=n.Cartesian3.fromArray(d,3*M,S),C=n.Cartesian3.fromArray(d,3*D,N),n.Cartesian3.equalsEpsilon(m,C,f.CesiumMath.EPSILON10,f.CesiumMath.EPSILON10)||(A[L++]=M,A[L++]=R,A[L++]=D,A[L++]=D,A[L++]=R,A[L++]=O)}return new u.Geometry({attributes:new l.GeometryAttributes({position:new u.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:d})}),indices:A,primitiveType:p.PrimitiveType.TRIANGLES})},e.PolygonGeometryLibrary=g}));
