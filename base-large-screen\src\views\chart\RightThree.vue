<template>
  <div class="box">
    <PartTitle title="漏斗图" :icon="icon" :position="1.5" />
    <EchartsContainer :option="chartOption" style="width:470px;height:250px;" />
  </div>
</template>
<script setup>
import { onMounted, shallowRef, inject } from "vue";
import { storeToRefs } from 'pinia'
import PartTitle from "../../components/common/PartTitle.vue"
import EchartsContainer from "../../components/common/EchartsContainer.vue"
import icon from "../../assets/images/store.svg"
import { useChartStore } from "../../stores/modules/chart";

const { chartData } = useChartStore()

const chartOption = shallowRef({})

const pageTl = inject('pageTl')
const isEnter = inject('isEnter')

function initTl() {
  if (pageTl) {
    pageTl.add(setOption, 1.5)
  } else {
    setOption()
  }
}

function setOption() {
  if (isEnter && !isEnter.value) {
    return
  }
  const data = []
  if (chartData.rightThree && chartData.rightThree.length) {
    chartData.rightThree.forEach(item => {
      data.push({ name: item.key, value: item.value })
    })
  }
  chartOption.value = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c}%'
    },
    series: [
      {
        name: 'Funnel',
        type: 'funnel',
        left: '10%',
        top: 20,
        bottom: 20,
        width: '80%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside'
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 20
          }
        },
        data
      }
    ]
  }
}

onMounted(() => {
  initTl()
})

</script>
<style scoped>
.box {
  width: 500px;
  height: 290px;
  display: flex;
  flex-direction: column;
  background-color: #f5f5d5;
}
</style>
