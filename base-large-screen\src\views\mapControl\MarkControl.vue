<template>
  <div class="box">
    <el-checkbox v-for="(item, index) in mapStore.markData" :model-value="item.visible" :label="item.name"
      @change="onCheckChange(index, $event)" />
  </div>
</template>
<script setup>
import { useMapStore } from "../../stores/modules/map";

const mapStore = useMapStore();

function onCheckChange(index, value) {
  const newData = [...mapStore.markData]
  newData[index] = { ...mapStore.markData[index], visible: value }
  mapStore.setMarkData(newData)
}
</script>
<style scoped>
.box {
  display: flex;
  flex-direction: column;
  align-items: baseline;
  background-color: #fff;
  padding: 5px 10px;
}
</style>