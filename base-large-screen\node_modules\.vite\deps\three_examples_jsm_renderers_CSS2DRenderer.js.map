{"version": 3, "sources": ["../../three/examples/jsm/renderers/CSS2DRenderer.js"], "sourcesContent": ["import {\n\tMatrix4,\n\tObject3D,\n\tVector2,\n\tVector3\n} from 'three';\n\nclass CSS2DObject extends Object3D {\n\n\tconstructor( element = document.createElement( 'div' ) ) {\n\n\t\tsuper();\n\n\t\tthis.isCSS2DObject = true;\n\n\t\tthis.element = element;\n\n\t\tthis.element.style.position = 'absolute';\n\t\tthis.element.style.userSelect = 'none';\n\n\t\tthis.element.setAttribute( 'draggable', false );\n\n\t\tthis.center = new Vector2( 0.5, 0.5 ); // ( 0, 0 ) is the lower left; ( 1, 1 ) is the top right\n\n\t\tthis.addEventListener( 'removed', function () {\n\n\t\t\tthis.traverse( function ( object ) {\n\n\t\t\t\tif ( object.element instanceof Element && object.element.parentNode !== null ) {\n\n\t\t\t\t\tobject.element.parentNode.removeChild( object.element );\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t} );\n\n\t}\n\n\tcopy( source, recursive ) {\n\n\t\tsuper.copy( source, recursive );\n\n\t\tthis.element = source.element.cloneNode( true );\n\n\t\tthis.center = source.center;\n\n\t\treturn this;\n\n\t}\n\n}\n\n//\n\nconst _vector = new Vector3();\nconst _viewMatrix = new Matrix4();\nconst _viewProjectionMatrix = new Matrix4();\nconst _a = new Vector3();\nconst _b = new Vector3();\n\nclass CSS2DRenderer {\n\n\tconstructor( parameters = {} ) {\n\n\t\tconst _this = this;\n\n\t\tlet _width, _height;\n\t\tlet _widthHalf, _heightHalf;\n\n\t\tconst cache = {\n\t\t\tobjects: new WeakMap()\n\t\t};\n\n\t\tconst domElement = parameters.element !== undefined ? parameters.element : document.createElement( 'div' );\n\n\t\tdomElement.style.overflow = 'hidden';\n\n\t\tthis.domElement = domElement;\n\n\t\tthis.getSize = function () {\n\n\t\t\treturn {\n\t\t\t\twidth: _width,\n\t\t\t\theight: _height\n\t\t\t};\n\n\t\t};\n\n\t\tthis.render = function ( scene, camera ) {\n\n\t\t\tif ( scene.matrixWorldAutoUpdate === true ) scene.updateMatrixWorld();\n\t\t\tif ( camera.parent === null && camera.matrixWorldAutoUpdate === true ) camera.updateMatrixWorld();\n\n\t\t\t_viewMatrix.copy( camera.matrixWorldInverse );\n\t\t\t_viewProjectionMatrix.multiplyMatrices( camera.projectionMatrix, _viewMatrix );\n\n\t\t\trenderObject( scene, scene, camera );\n\t\t\tzOrder( scene );\n\n\t\t};\n\n\t\tthis.setSize = function ( width, height ) {\n\n\t\t\t_width = width;\n\t\t\t_height = height;\n\n\t\t\t_widthHalf = _width / 2;\n\t\t\t_heightHalf = _height / 2;\n\n\t\t\tdomElement.style.width = width + 'px';\n\t\t\tdomElement.style.height = height + 'px';\n\n\t\t};\n\n\t\tfunction renderObject( object, scene, camera ) {\n\n\t\t\tif ( object.isCSS2DObject ) {\n\n\t\t\t\t_vector.setFromMatrixPosition( object.matrixWorld );\n\t\t\t\t_vector.applyMatrix4( _viewProjectionMatrix );\n\n\t\t\t\tconst visible = ( object.visible === true ) && ( _vector.z >= - 1 && _vector.z <= 1 ) && ( object.layers.test( camera.layers ) === true );\n\t\t\t\tobject.element.style.display = ( visible === true ) ? '' : 'none';\n\n\t\t\t\tif ( visible === true ) {\n\n\t\t\t\t\tobject.onBeforeRender( _this, scene, camera );\n\n\t\t\t\t\tconst element = object.element;\n\n\t\t\t\t\telement.style.transform = 'translate(' + ( - 100 * object.center.x ) + '%,' + ( - 100 * object.center.y ) + '%)' + 'translate(' + ( _vector.x * _widthHalf + _widthHalf ) + 'px,' + ( - _vector.y * _heightHalf + _heightHalf ) + 'px)';\n\n\t\t\t\t\tif ( element.parentNode !== domElement ) {\n\n\t\t\t\t\t\tdomElement.appendChild( element );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tobject.onAfterRender( _this, scene, camera );\n\n\t\t\t\t}\n\n\t\t\t\tconst objectData = {\n\t\t\t\t\tdistanceToCameraSquared: getDistanceToSquared( camera, object )\n\t\t\t\t};\n\n\t\t\t\tcache.objects.set( object, objectData );\n\n\t\t\t}\n\n\t\t\tfor ( let i = 0, l = object.children.length; i < l; i ++ ) {\n\n\t\t\t\trenderObject( object.children[ i ], scene, camera );\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction getDistanceToSquared( object1, object2 ) {\n\n\t\t\t_a.setFromMatrixPosition( object1.matrixWorld );\n\t\t\t_b.setFromMatrixPosition( object2.matrixWorld );\n\n\t\t\treturn _a.distanceToSquared( _b );\n\n\t\t}\n\n\t\tfunction filterAndFlatten( scene ) {\n\n\t\t\tconst result = [];\n\n\t\t\tscene.traverse( function ( object ) {\n\n\t\t\t\tif ( object.isCSS2DObject ) result.push( object );\n\n\t\t\t} );\n\n\t\t\treturn result;\n\n\t\t}\n\n\t\tfunction zOrder( scene ) {\n\n\t\t\tconst sorted = filterAndFlatten( scene ).sort( function ( a, b ) {\n\n\t\t\t\tif ( a.renderOrder !== b.renderOrder ) {\n\n\t\t\t\t\treturn b.renderOrder - a.renderOrder;\n\n\t\t\t\t}\n\n\t\t\t\tconst distanceA = cache.objects.get( a ).distanceToCameraSquared;\n\t\t\t\tconst distanceB = cache.objects.get( b ).distanceToCameraSquared;\n\n\t\t\t\treturn distanceA - distanceB;\n\n\t\t\t} );\n\n\t\t\tconst zMax = sorted.length;\n\n\t\t\tfor ( let i = 0, l = sorted.length; i < l; i ++ ) {\n\n\t\t\t\tsorted[ i ].element.style.zIndex = zMax - i;\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n}\n\nexport { CSS2DObject, CSS2DRenderer };\n"], "mappings": ";;;;;;;;;AAOA,IAAM,cAAN,cAA0B,SAAS;AAAA,EAElC,YAAa,UAAU,SAAS,cAAe,KAAM,GAAI;AAExD,UAAM;AAEN,SAAK,gBAAgB;AAErB,SAAK,UAAU;AAEf,SAAK,QAAQ,MAAM,WAAW;AAC9B,SAAK,QAAQ,MAAM,aAAa;AAEhC,SAAK,QAAQ,aAAc,aAAa,KAAM;AAE9C,SAAK,SAAS,IAAI,QAAS,KAAK,GAAI;AAEpC,SAAK,iBAAkB,WAAW,WAAY;AAE7C,WAAK,SAAU,SAAW,QAAS;AAElC,YAAK,OAAO,mBAAmB,WAAW,OAAO,QAAQ,eAAe,MAAO;AAE9E,iBAAO,QAAQ,WAAW,YAAa,OAAO,OAAQ;AAAA,QAEvD;AAAA,MAED,CAAE;AAAA,IAEH,CAAE;AAAA,EAEH;AAAA,EAEA,KAAM,QAAQ,WAAY;AAEzB,UAAM,KAAM,QAAQ,SAAU;AAE9B,SAAK,UAAU,OAAO,QAAQ,UAAW,IAAK;AAE9C,SAAK,SAAS,OAAO;AAErB,WAAO;AAAA,EAER;AAED;AAIA,IAAM,UAAU,IAAI,QAAQ;AAC5B,IAAM,cAAc,IAAI,QAAQ;AAChC,IAAM,wBAAwB,IAAI,QAAQ;AAC1C,IAAM,KAAK,IAAI,QAAQ;AACvB,IAAM,KAAK,IAAI,QAAQ;AAEvB,IAAM,gBAAN,MAAoB;AAAA,EAEnB,YAAa,aAAa,CAAC,GAAI;AAE9B,UAAM,QAAQ;AAEd,QAAI,QAAQ;AACZ,QAAI,YAAY;AAEhB,UAAM,QAAQ;AAAA,MACb,SAAS,oBAAI,QAAQ;AAAA,IACtB;AAEA,UAAM,aAAa,WAAW,YAAY,SAAY,WAAW,UAAU,SAAS,cAAe,KAAM;AAEzG,eAAW,MAAM,WAAW;AAE5B,SAAK,aAAa;AAElB,SAAK,UAAU,WAAY;AAE1B,aAAO;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,IAED;AAEA,SAAK,SAAS,SAAW,OAAO,QAAS;AAExC,UAAK,MAAM,0BAA0B;AAAO,cAAM,kBAAkB;AACpE,UAAK,OAAO,WAAW,QAAQ,OAAO,0BAA0B;AAAO,eAAO,kBAAkB;AAEhG,kBAAY,KAAM,OAAO,kBAAmB;AAC5C,4BAAsB,iBAAkB,OAAO,kBAAkB,WAAY;AAE7E,mBAAc,OAAO,OAAO,MAAO;AACnC,aAAQ,KAAM;AAAA,IAEf;AAEA,SAAK,UAAU,SAAW,OAAO,QAAS;AAEzC,eAAS;AACT,gBAAU;AAEV,mBAAa,SAAS;AACtB,oBAAc,UAAU;AAExB,iBAAW,MAAM,QAAQ,QAAQ;AACjC,iBAAW,MAAM,SAAS,SAAS;AAAA,IAEpC;AAEA,aAAS,aAAc,QAAQ,OAAO,QAAS;AAE9C,UAAK,OAAO,eAAgB;AAE3B,gBAAQ,sBAAuB,OAAO,WAAY;AAClD,gBAAQ,aAAc,qBAAsB;AAE5C,cAAM,UAAY,OAAO,YAAY,SAAY,QAAQ,KAAK,MAAO,QAAQ,KAAK,MAAS,OAAO,OAAO,KAAM,OAAO,MAAO,MAAM;AACnI,eAAO,QAAQ,MAAM,UAAY,YAAY,OAAS,KAAK;AAE3D,YAAK,YAAY,MAAO;AAEvB,iBAAO,eAAgB,OAAO,OAAO,MAAO;AAE5C,gBAAM,UAAU,OAAO;AAEvB,kBAAQ,MAAM,YAAY,eAAiB,OAAQ,OAAO,OAAO,IAAM,OAAS,OAAQ,OAAO,OAAO,IAAM,kBAAwB,QAAQ,IAAI,aAAa,cAAe,SAAU,CAAE,QAAQ,IAAI,cAAc,eAAgB;AAElO,cAAK,QAAQ,eAAe,YAAa;AAExC,uBAAW,YAAa,OAAQ;AAAA,UAEjC;AAEA,iBAAO,cAAe,OAAO,OAAO,MAAO;AAAA,QAE5C;AAEA,cAAM,aAAa;AAAA,UAClB,yBAAyB,qBAAsB,QAAQ,MAAO;AAAA,QAC/D;AAEA,cAAM,QAAQ,IAAK,QAAQ,UAAW;AAAA,MAEvC;AAEA,eAAU,IAAI,GAAG,IAAI,OAAO,SAAS,QAAQ,IAAI,GAAG,KAAO;AAE1D,qBAAc,OAAO,SAAU,CAAE,GAAG,OAAO,MAAO;AAAA,MAEnD;AAAA,IAED;AAEA,aAAS,qBAAsB,SAAS,SAAU;AAEjD,SAAG,sBAAuB,QAAQ,WAAY;AAC9C,SAAG,sBAAuB,QAAQ,WAAY;AAE9C,aAAO,GAAG,kBAAmB,EAAG;AAAA,IAEjC;AAEA,aAAS,iBAAkB,OAAQ;AAElC,YAAM,SAAS,CAAC;AAEhB,YAAM,SAAU,SAAW,QAAS;AAEnC,YAAK,OAAO;AAAgB,iBAAO,KAAM,MAAO;AAAA,MAEjD,CAAE;AAEF,aAAO;AAAA,IAER;AAEA,aAAS,OAAQ,OAAQ;AAExB,YAAM,SAAS,iBAAkB,KAAM,EAAE,KAAM,SAAW,GAAG,GAAI;AAEhE,YAAK,EAAE,gBAAgB,EAAE,aAAc;AAEtC,iBAAO,EAAE,cAAc,EAAE;AAAA,QAE1B;AAEA,cAAM,YAAY,MAAM,QAAQ,IAAK,CAAE,EAAE;AACzC,cAAM,YAAY,MAAM,QAAQ,IAAK,CAAE,EAAE;AAEzC,eAAO,YAAY;AAAA,MAEpB,CAAE;AAEF,YAAM,OAAO,OAAO;AAEpB,eAAU,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAO;AAEjD,eAAQ,CAAE,EAAE,QAAQ,MAAM,SAAS,OAAO;AAAA,MAE3C;AAAA,IAED;AAAA,EAED;AAED;", "names": []}