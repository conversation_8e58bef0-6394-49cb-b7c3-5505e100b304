<template>
  <div class="button" @click="onClick" :class="{ active: route.name === props.name }">
    <slot></slot>
  </div>
</template>
<script setup>
import { computed, onMounted, inject, ref } from "vue";
import { useRouter, useRoute } from "vue-router";

const router = useRouter();
const route = useRoute();

const props = defineProps({
  name: {
    type: String,
  }
})

function onClick() {
  if (!props.name) {
    return
  }
  router.push({
    name: props.name
  })
}
</script>
<style scoped>
.button {
  --bg: #e74c3c;
  --text-color: #fff;
  position: relative;
  border: none;
  background: var(--bg);
  color: var(--text-color);
  padding: 0.5em 1em;
  margin: 0 1em;
  font-size: 19px;
  font-weight: bold;
  text-transform: uppercase;
  transition: 0.2s;
  border-radius: 5px;
  opacity: 0.8;
  letter-spacing: 1px;
  box-shadow: #c0392b 0px 7px 2px, #000 0px 8px 5px;
  cursor: pointer
}

.button:hover {
  opacity: 1;
}

.button:active {
  top: 4px;
  box-shadow: #c0392b 0px 3px 2px, #000 0px 3px 5px;
}

.active {
  opacity: 1;
  top: 4px;
  box-shadow: #c0392b 0px 3px 2px, #000 0px 3px 5px;
}
</style>
