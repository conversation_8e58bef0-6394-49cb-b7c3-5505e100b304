define(["exports","./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./buildModuleUrl-8958744c","./Cartesian2-47311507","./Cartesian4-3ca25aab","./IntersectionTests-a793ed08","./FeatureDetection-806b12f0","./Plane-a3d8b3d2","./GeometryAttribute-06a41648"],(function(e,n,t,i,a,r,s,o,m,l,c){"use strict";function u(e,t,a){this.minimum=n.Cartesian3.clone(i.defaultValue(e,n.Cartesian3.ZERO)),this.maximum=n.Cartesian3.clone(i.defaultValue(t,n.Cartesian3.ZERO)),a=i.defined(a)?n.Cartesian3.clone(a):n.Cartesian3.midpoint(this.minimum,this.maximum,new n.Cartesian3),this.center=a}u.fromPoints=function(e,t){if(i.defined(t)||(t=new u),!i.defined(e)||0===e.length)return t.minimum=n.Cartesian3.clone(n.Cartesian3.ZERO,t.minimum),t.maximum=n.Cartesian3.clone(n.Cartesian3.ZERO,t.maximum),t.center=n.Cartesian3.clone(n.Cartesian3.ZERO,t.center),t;for(var a=e[0].x,r=e[0].y,s=e[0].z,o=e[0].x,m=e[0].y,l=e[0].z,c=e.length,d=1;d<c;d++){var f=e[d],h=f.x,C=f.y,p=f.z;a=Math.min(h,a),o=Math.max(h,o),r=Math.min(C,r),m=Math.max(C,m),s=Math.min(p,s),l=Math.max(p,l)}var x=t.minimum;x.x=a,x.y=r,x.z=s;var y=t.maximum;return y.x=o,y.y=m,y.z=l,t.center=n.Cartesian3.midpoint(x,y,t.center),t},u.clone=function(e,t){if(i.defined(e))return i.defined(t)?(t.minimum=n.Cartesian3.clone(e.minimum,t.minimum),t.maximum=n.Cartesian3.clone(e.maximum,t.maximum),t.center=n.Cartesian3.clone(e.center,t.center),t):new u(e.minimum,e.maximum,e.center)},u.equals=function(e,t){return e===t||i.defined(e)&&i.defined(t)&&n.Cartesian3.equals(e.center,t.center)&&n.Cartesian3.equals(e.minimum,t.minimum)&&n.Cartesian3.equals(e.maximum,t.maximum)};var d=new n.Cartesian3;u.intersectPlane=function(e,t){d=n.Cartesian3.subtract(e.maximum,e.minimum,d);var i=n.Cartesian3.multiplyByScalar(d,.5,d),r=t.normal,s=i.x*Math.abs(r.x)+i.y*Math.abs(r.y)+i.z*Math.abs(r.z),o=n.Cartesian3.dot(e.center,r)+t.distance;return o-s>0?a.Intersect.INSIDE:o+s<0?a.Intersect.OUTSIDE:a.Intersect.INTERSECTING},u.prototype.clone=function(e){return u.clone(this,e)},u.prototype.intersectPlane=function(e){return u.intersectPlane(this,e)},u.prototype.equals=function(e){return u.equals(this,e)};var f=new s.Cartesian4;function h(e,t){e=(t=i.defaultValue(t,r.Ellipsoid.WGS84)).scaleToGeodeticSurface(e);var a=c.Transforms.eastNorthUpToFixedFrame(e,t);this._ellipsoid=t,this._origin=e,this._xAxis=n.Cartesian3.fromCartesian4(m.Matrix4.getColumn(a,0,f)),this._yAxis=n.Cartesian3.fromCartesian4(m.Matrix4.getColumn(a,1,f));var s=n.Cartesian3.fromCartesian4(m.Matrix4.getColumn(a,2,f));this._plane=l.Plane.fromPointNormal(e,s)}Object.defineProperties(h.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},origin:{get:function(){return this._origin}},plane:{get:function(){return this._plane}},xAxis:{get:function(){return this._xAxis}},yAxis:{get:function(){return this._yAxis}},zAxis:{get:function(){return this._plane.normal}}});var C=new u;h.fromPoints=function(e,n){return new h(u.fromPoints(e,C).center,n)};var p=new o.Ray,x=new n.Cartesian3;h.prototype.projectPointOntoPlane=function(e,t){var a=p;a.origin=e,n.Cartesian3.normalize(e,a.direction);var s=o.IntersectionTests.rayPlane(a,this._plane,x);if(i.defined(s)||(n.Cartesian3.negate(a.direction,a.direction),s=o.IntersectionTests.rayPlane(a,this._plane,x)),i.defined(s)){var m=n.Cartesian3.subtract(s,this._origin,s),l=n.Cartesian3.dot(this._xAxis,m),c=n.Cartesian3.dot(this._yAxis,m);return i.defined(t)?(t.x=l,t.y=c,t):new r.Cartesian2(l,c)}},h.prototype.projectPointsOntoPlane=function(e,n){i.defined(n)||(n=[]);for(var t=0,a=e.length,r=0;r<a;r++){var s=this.projectPointOntoPlane(e[r],n[t]);i.defined(s)&&(n[t]=s,t++)}return n.length=t,n},h.prototype.projectPointToNearestOnPlane=function(e,t){i.defined(t)||(t=new r.Cartesian2);var a=p;a.origin=e,n.Cartesian3.clone(this._plane.normal,a.direction);var s=o.IntersectionTests.rayPlane(a,this._plane,x);i.defined(s)||(n.Cartesian3.negate(a.direction,a.direction),s=o.IntersectionTests.rayPlane(a,this._plane,x));var m=n.Cartesian3.subtract(s,this._origin,s),l=n.Cartesian3.dot(this._xAxis,m),c=n.Cartesian3.dot(this._yAxis,m);return t.x=l,t.y=c,t},h.prototype.projectPointsToNearestOnPlane=function(e,n){i.defined(n)||(n=[]);var t=e.length;n.length=t;for(var a=0;a<t;a++)n[a]=this.projectPointToNearestOnPlane(e[a],n[a]);return n};var y=new n.Cartesian3;h.prototype.projectPointOntoEllipsoid=function(e,t){i.defined(t)||(t=new n.Cartesian3);var a=this._ellipsoid,r=this._origin,s=this._xAxis,o=this._yAxis,m=y;return n.Cartesian3.multiplyByScalar(s,e.x,m),t=n.Cartesian3.add(r,m,t),n.Cartesian3.multiplyByScalar(o,e.y,m),n.Cartesian3.add(t,m,t),a.scaleToGeocentricSurface(t,t),t},h.prototype.projectPointsOntoEllipsoid=function(e,n){var t=e.length;i.defined(n)?n.length=t:n=new Array(t);for(var a=0;a<t;++a)n[a]=this.projectPointOntoEllipsoid(e[a],n[a]);return n},e.AxisAlignedBoundingBox=u,e.EllipsoidTangentPlane=h}));
