define(["exports","./Check-7b2a090c","./when-b60132fc","./Math-119be1a3"],(function(n,e,t,r){"use strict";function a(n,e,r,a){this.x=t.defaultValue(n,0),this.y=t.defaultValue(e,0),this.z=t.defaultValue(r,0),this.w=t.defaultValue(a,0)}a.fromElements=function(n,e,r,u,i){return t.defined(i)?(i.x=n,i.y=e,i.z=r,i.w=u,i):new a(n,e,r,u)},a.fromColor=function(n,e){return t.defined(e)?(e.x=n.red,e.y=n.green,e.z=n.blue,e.w=n.alpha,e):new a(n.red,n.green,n.blue,n.alpha)},a.clone=function(n,e){if(t.defined(n))return t.defined(e)?(e.x=n.x,e.y=n.y,e.z=n.z,e.w=n.w,e):new a(n.x,n.y,n.z,n.w)},a.packedLength=4,a.pack=function(n,e,r){return r=t.defaultValue(r,0),e[r++]=n.x,e[r++]=n.y,e[r++]=n.z,e[r]=n.w,e},a.unpack=function(n,e,r){return e=t.defaultValue(e,0),t.defined(r)||(r=new a),r.x=n[e++],r.y=n[e++],r.z=n[e++],r.w=n[e],r},a.packArray=function(n,r){var u=n.length,i=4*u;if(t.defined(r)){if(!Array.isArray(r)&&r.length!==i)throw new e.DeveloperError("If result is a typed array, it must have exactly array.length * 4 elements");r.length!==i&&(r.length=i)}else r=new Array(i);for(var o=0;o<u;++o)a.pack(n[o],r,4*o);return r},a.unpackArray=function(n,e){var r=n.length;t.defined(e)?e.length=r/4:e=new Array(r/4);for(var u=0;u<r;u+=4){var i=u/4;e[i]=a.unpack(n,u,e[i])}return e},a.fromArray=a.unpack,a.maximumComponent=function(n){return Math.max(n.x,n.y,n.z,n.w)},a.minimumComponent=function(n){return Math.min(n.x,n.y,n.z,n.w)},a.minimumByComponent=function(n,e,t){return t.x=Math.min(n.x,e.x),t.y=Math.min(n.y,e.y),t.z=Math.min(n.z,e.z),t.w=Math.min(n.w,e.w),t},a.maximumByComponent=function(n,e,t){return t.x=Math.max(n.x,e.x),t.y=Math.max(n.y,e.y),t.z=Math.max(n.z,e.z),t.w=Math.max(n.w,e.w),t},a.magnitudeSquared=function(n){return n.x*n.x+n.y*n.y+n.z*n.z+n.w*n.w},a.magnitude=function(n){return Math.sqrt(a.magnitudeSquared(n))};var u=new a;a.distance=function(n,e){return a.subtract(n,e,u),a.magnitude(u)},a.distanceSquared=function(n,e){return a.subtract(n,e,u),a.magnitudeSquared(u)},a.normalize=function(n,e){var t=a.magnitude(n);return e.x=n.x/t,e.y=n.y/t,e.z=n.z/t,e.w=n.w/t,e},a.dot=function(n,e){return n.x*e.x+n.y*e.y+n.z*e.z+n.w*e.w},a.multiplyComponents=function(n,e,t){return t.x=n.x*e.x,t.y=n.y*e.y,t.z=n.z*e.z,t.w=n.w*e.w,t},a.divideComponents=function(n,e,t){return t.x=n.x/e.x,t.y=n.y/e.y,t.z=n.z/e.z,t.w=n.w/e.w,t},a.add=function(n,e,t){return t.x=n.x+e.x,t.y=n.y+e.y,t.z=n.z+e.z,t.w=n.w+e.w,t},a.subtract=function(n,e,t){return t.x=n.x-e.x,t.y=n.y-e.y,t.z=n.z-e.z,t.w=n.w-e.w,t},a.multiplyByScalar=function(n,e,t){return t.x=n.x*e,t.y=n.y*e,t.z=n.z*e,t.w=n.w*e,t},a.divideByScalar=function(n,e,t){return t.x=n.x/e,t.y=n.y/e,t.z=n.z/e,t.w=n.w/e,t},a.negate=function(n,e){return e.x=-n.x,e.y=-n.y,e.z=-n.z,e.w=-n.w,e},a.abs=function(n,e){return e.x=Math.abs(n.x),e.y=Math.abs(n.y),e.z=Math.abs(n.z),e.w=Math.abs(n.w),e};var i=new a;a.lerp=function(n,e,t,r){return a.multiplyByScalar(e,t,i),r=a.multiplyByScalar(n,1-t,r),a.add(i,r,r)};var o=new a;a.mostOrthogonalAxis=function(n,e){var t=a.normalize(n,o);return a.abs(t,t),e=t.x<=t.y?t.x<=t.z?t.x<=t.w?a.clone(a.UNIT_X,e):a.clone(a.UNIT_W,e):t.z<=t.w?a.clone(a.UNIT_Z,e):a.clone(a.UNIT_W,e):t.y<=t.z?t.y<=t.w?a.clone(a.UNIT_Y,e):a.clone(a.UNIT_W,e):t.z<=t.w?a.clone(a.UNIT_Z,e):a.clone(a.UNIT_W,e)},a.equals=function(n,e){return n===e||t.defined(n)&&t.defined(e)&&n.x===e.x&&n.y===e.y&&n.z===e.z&&n.w===e.w},a.equalsArray=function(n,e,t){return n.x===e[t]&&n.y===e[t+1]&&n.z===e[t+2]&&n.w===e[t+3]},a.equalsEpsilon=function(n,e,a,u){return n===e||t.defined(n)&&t.defined(e)&&r.CesiumMath.equalsEpsilon(n.x,e.x,a,u)&&r.CesiumMath.equalsEpsilon(n.y,e.y,a,u)&&r.CesiumMath.equalsEpsilon(n.z,e.z,a,u)&&r.CesiumMath.equalsEpsilon(n.w,e.w,a,u)},a.ZERO=Object.freeze(new a(0,0,0,0)),a.UNIT_X=Object.freeze(new a(1,0,0,0)),a.UNIT_Y=Object.freeze(new a(0,1,0,0)),a.UNIT_Z=Object.freeze(new a(0,0,1,0)),a.UNIT_W=Object.freeze(new a(0,0,0,1)),a.prototype.clone=function(n){return a.clone(this,n)},a.prototype.equals=function(n){return a.equals(this,n)},a.prototype.equalsEpsilon=function(n,e,t){return a.equalsEpsilon(this,n,e,t)},a.prototype.toString=function(){return"("+this.x+", "+this.y+", "+this.z+", "+this.w+")"};var l=new Float32Array(1),y=256;a.packFloat=function(n,e){if(t.defined(e)||(e=new a),l[0]=n,0===(n=l[0]))return a.clone(a.ZERO,e);var u,i=n<0?1:0;isFinite(n)?(n=Math.abs(n),u=Math.floor(r.CesiumMath.logBase(n,10))+1,n/=Math.pow(10,u)):(n=.1,u=38);var o=n*y;return e.x=Math.floor(o),o=(o-e.x)*y,e.y=Math.floor(o),o=(o-e.y)*y,e.z=Math.floor(o),e.w=2*(u+38)+i,e},a.unpackFloat=function(n){var e=n.w/2,t=Math.floor(e),r=2*(e-t);if(r=-(r=2*r-1),(t-=38)>=38)return r<0?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY;var a=r*n.x*.00390625;return a+=r*n.y*152587890625e-16,(a+=r*n.z*5.960464477539063e-8)*Math.pow(10,t)},n.Cartesian4=a}));
