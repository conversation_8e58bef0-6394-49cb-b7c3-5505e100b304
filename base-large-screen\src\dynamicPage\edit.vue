<template>
  <template v-if="pagesConfig && pagesConfig.length">
    <div ref="mainDom" class="main-panel" :style="boxStyle" @mouseenter="onEditMouseenter" @mousemove="onEditMousemove"
      @mousedown="onEditMousedown" @dragstart="onDragstart">
      <template v-for="(item, index) in pages[pageIndex].layers[pageLayer]" :key="item.key">
        <component :is="Components[item.key]" :style="compStyle(item, index)" @mousedown="onCompMousedown(index, $event)"
          @mouseup="onCompMouseup(index, $event)" :title="item.title" />
      </template>
    </div>
    <div v-if="!pages[pageIndex].layers[pageLayer] || !pages[pageIndex].layers[pageLayer].length" class="tip-text">
      左侧拖选组件，右侧页面配置
    </div>
    <div class="right-hover" @mouseover="mouseHover('right')"></div>
    <div class="right-panel" :class="{ 'right-active': drawer === 'right' }">
      <el-form label-width="90px">
        <el-form-item label="页面路由">
          <el-radio-group v-model="pageIndex" @change="pageChange">
            <el-radio v-for="(page, index) in pages" :label="index" border class="route-radio">
              {{ page.name }}--
              <span style="color:#ff8345;flex: 1;">{{ page.routePath }}</span>
              <el-icon @click="editPage(index, $event)" :size="25">
                <Edit />
              </el-icon>
              <el-icon @click="copyPage(index, $event)" :size="25" style="margin-left: 10px;">
                <CopyDocument />
              </el-icon>
            </el-radio>
          </el-radio-group>
          <el-button type="primary" :icon="Plus" @click="addPage" plain>增加路由</el-button>
        </el-form-item>
        <el-form-item label="页面层级">
          <el-radio-group v-model="pageLayer">
            <el-radio-button label="firstLayer">1</el-radio-button>
            <el-radio-button label="secondLayer">2</el-radio-button>
            <el-radio-button label="thirdLayer">3</el-radio-button>
            <el-radio-button label="fourthLayer">4</el-radio-button>
            <el-radio-button label="fifthLayer">5</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="设计尺寸">
          <el-text size="large">width：</el-text>
          <el-input-number style="width: 100px;" v-model="pages[pageIndex].pageSize.width" :min="0"
            controls-position="right" />
          <el-text size="large" style="margin-left: 20px;">height：</el-text>
          <el-input-number style="width: 100px;" v-model="pages[pageIndex].pageSize.height" :min="0"
            controls-position="right" />
        </el-form-item>
        <el-form-item label="显示适配">
          <el-radio-group v-model="pages[pageIndex].layout">
            <el-radio-button label="default">等比缩放</el-radio-button>
            <el-radio-button label="adapt">占满全屏</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="syncConfig">同步配置</el-button>
          <el-button type="info" @click="downloadConfig">下载配置到本地</el-button>
          <el-button type="success" @click="previewConfig">预览页面</el-button>
        </el-form-item>
      </el-form>
      <div class="tip">
        <el-text type="warning" size="large">使用提示：</el-text>
        <el-text type="primary">为防止误操作，所有操作都不会自动保存，需手动“同步配置”</el-text>
        <el-text type="primary">页面5层设计，请合理分配组件的层级结构</el-text>
        <el-text type="primary">页面第1层设计为只能放一个全屏组件，适合放置全屏地图或者背景图</el-text>
        <el-text type="primary">页面第3层适合放置数据需要切换的模块</el-text>
        <el-text type="primary">Enter按键聚焦组件或切换聚焦组件（不能鼠标点选可使用此快捷键）</el-text>
        <el-text type="primary">Delete按键可快捷删除当前聚焦组件</el-text>
      </div>
    </div>
    <div class="left-hover" @mouseover="mouseHover('left')"></div>
    <div class="left-panel" :class="{ 'left-active': drawer === 'left' }" @dragstart="onDragstart">
      <div class="action">
        <el-input v-model="compName" placeholder="输入组件名称" clearable>
          <template #prepend>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>
      <div class="action">
        <el-select v-model="category" placeholder="组件分组" style="width: 170px" clearable>
          <el-option v-for="item in Categorys" :key="item.name" :label="item.name" :value="item.name" />
        </el-select>
        <el-select v-model="compType" placeholder="组件类别" style="width: 170px" clearable>
          <el-option label="普通组件" value="common" />
          <el-option label="导航组件" value="navigation" />
          <el-option label="地图组件" value="map" />
          <el-option label="全屏组件" value="fullScreen" />
        </el-select>
      </div>
      <div class="content">
        <template v-for="(item, key) in CompsConfig">
          <div class="component" v-if="showComp(item)" @mousedown="onSourceMousedown(key, item.type)">
            <div class="header">
              <span class="name">{{ item.name }}</span>
              <span class="category">{{ item.category }}</span>
              <span class="type" v-if="item.type === 'fullScreen'"
                style="color: #4558ff;border-color: #4558ff;">全屏组件</span>
              <span class="type" v-else-if="item.type === 'navigation'"
                style="color: #ffae45;border-color: #ffae45;">导航组件</span>
              <span class="type" v-else-if="item.type === 'map'" style="color: #13ce66;border-color: #13ce66;">地图组件</span>
              <span class="type" v-else>普通组件</span>
            </div>
            <div v-if="item.type === 'map'" class="map-tip">地图组件不提供预览</div>
            <div v-else-if="item.type === 'navigation'" class="map-tip">导航组件不提供预览</div>
            <component v-else :is="Components[key]"
              :style="{ width: '350px', height: `${350 / (item.aspectRatio || 1)}px` }" />
          </div>
        </template>
      </div>
    </div>
    <div class="edit-panel" v-if="focusIndex !== null" :class="[editBottom ? 'edit-bottom' : 'edit-top']">
      <div class="box">
        <span>入场动画</span>
        <el-form label-width="40px">
          <el-form-item label="动画">
            <el-select v-model="pages[pageIndex].layers[pageLayer][focusIndex].animation.type" style="width: 120px"
              clearable>
              <el-option v-for=" item  in  Animations " :value="item.value" :label="item.label">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间">
            <el-input-number style="width: 120px;"
              v-model="pages[pageIndex].layers[pageLayer][focusIndex].animation.startTime" :min="0" :precision="2"
              :step="0.1" :max="10" controls-position="right" />
          </el-form-item>
        </el-form>
      </div>
      <div class="box box-border">
        <span>位置</span>
        <el-form label-width="68px">
          <el-form-item label="定位方式">
            <el-radio-group :model-value="positionType" @change="positionTypeChange">
              <el-radio-button label="left">左</el-radio-button>
              <el-radio-button label="center">中</el-radio-button>
              <el-radio-button label="right">右</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="left:" v-if="pages[pageIndex].layers[pageLayer][focusIndex].style.left">
            <el-input style="width:135px" v-model="pages[pageIndex].layers[pageLayer][focusIndex].style.left" />
          </el-form-item>
          <el-form-item label="right:" v-if="pages[pageIndex].layers[pageLayer][focusIndex].style.right">
            <el-input style="width:135px" v-model="pages[pageIndex].layers[pageLayer][focusIndex].style.right" />
          </el-form-item>
          <el-form-item label="top:">
            <el-input style="width:135px" v-model="pages[pageIndex].layers[pageLayer][focusIndex].style.top" />
          </el-form-item>
        </el-form>
      </div>
      <div class="box box-border">
        <span>尺寸</span>
        <el-form label-width="55px">
          <el-form-item label="width:">
            <el-input style="width:135px" v-model="pages[pageIndex].layers[pageLayer][focusIndex].style.width"
              @input="sizeChange('width')" />
          </el-form-item>
          <el-form-item label="height:">
            <el-input style="width:135px" v-model="pages[pageIndex].layers[pageLayer][focusIndex].style.height"
              @input="sizeChange('height')" />
          </el-form-item>
          <el-form-item v-if="compAspectRatio" label="保持比例" label-width="72px">
            <el-switch v-model="bindAspectRatio" />
          </el-form-item>
        </el-form>
      </div>
      <div class="box box-border">
        <span>是否支持聚焦</span>
        <el-switch v-model="pages[pageIndex].layers[pageLayer][focusIndex].focus" style="align-self: center;" />
        <span>模块标题</span>
        <el-input style="width:135px" v-model="pages[pageIndex].layers[pageLayer][focusIndex].title" />
      </div>
      <div class="box box-border">
        <span style="font-size: 13px; line-height: 20px;">自定义样式</span>
        <JSONEditor :data="getCustomStyle(pages[pageIndex].layers[pageLayer][focusIndex])" @change="customStyleChange">
        </JSONEditor>
      </div>
      <div class="box box-border" style="justify-content: space-between;">
        <el-button @click="deleteComp" type="danger" :icon="Delete" circle size="large" style="margin: 40px 10px;" />
        <el-switch v-model="editBottom" inline-prompt
          style="--el-switch-on-color: #13ce66; --el-switch-off-color: #409eff" active-text="靠下" inactive-text="靠上" />
      </div>
    </div>
  </template>
  <div v-else>未找到可配置的大屏页面</div>
  <Teleport v-if="caretDom" :to="caretDom">
    <el-icon v-if="positionType === 'center' || positionType === 'right'" class="caret-left caret"
      @mousedown="onCaretMousedown('left', $event)">
      <CaretLeft />
    </el-icon>
    <el-icon v-if="positionType === 'center' || positionType === 'left'" class="caret-right caret"
      @mousedown="onCaretMousedown('right', $event)">
      <CaretRight />
    </el-icon>
    <el-icon class="caret-bottom caret" @mousedown="onCaretMousedown('bottom', $event)">
      <CaretBottom />
    </el-icon>
  </Teleport>
  <el-dialog v-model="editPageVisible" :title="editPageIndex === null ? '新增页面' : '修改页面'" destroy-on-close>
    <el-form ref="editForm" :model="editPageConfig" :rules="editRules" label-width="200px">
      <el-form-item label="页面名称（用于导航按钮）" prop="name">
        <el-input v-model="editPageConfig.name" />
      </el-form-item>
      <el-form-item label="页面路由（需以“/”开头）" prop="routePath">
        <el-input v-model="editPageConfig.routePath" />
      </el-form-item>
      <el-form-item label="是否禁用">
        <el-checkbox v-model="editPageConfig.disabled">禁用</el-checkbox>
      </el-form-item>
      <el-form-item label="显示顺序（数字小靠前）">
        <el-input-number v-model="editPageConfig.order" :min="1" :max="10" controls-position="right" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelEditPage">取消</el-button>
        <el-button v-if="editPageIndex !== null" type="danger" @click="deleteEditPage">
          删除
        </el-button>
        <el-button type="primary" @click="saveEditPage(editForm)">
          保存
        </el-button>
      </span>
    </template>
  </el-dialog>
  <Teleport v-if="route.name === 'PagePreview'" to="body">
    <el-button type="success" size="small" @click="exitPreview" class="exit-preview">退出预览</el-button>
  </Teleport>
</template>

<script setup>
import { h, ref, reactive, computed, toRaw, onMounted, defineAsyncComponent, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useEventListener } from "@/composables/event";
import { pagesConfig, PageTemplate, previewPage, lsid } from "./utils";
import { ElMessageBox, ElMessage, ElRadioGroup, ElRadio } from "element-plus";
import { Delete, CaretLeft, CaretRight, CaretBottom, Plus, Edit, CopyDocument, Search } from '@element-plus/icons-vue'
import { CompsConfig, Modules, Categorys } from "./config"
import { syncDynamicPage } from "@/api"
import JSONEditor from './jsonEditor.vue'

const router = useRouter();
const route = useRoute();

const mainDom = ref(null)

const Animations = [
  {
    value: 'LeftSlide',
    label: '左侧滑入',
  },
  {
    value: 'RightSlide',
    label: '右侧滑入',
  },
  {
    value: 'TopSlide',
    label: '上方滑入',
  },
  {
    value: 'BottomSlide',
    label: '下方滑入',
  },
  {
    value: 'HideDisplay',
    label: '渐显',
  },
  {
    value: 'CenterGrow',
    label: '中心扩大',
  }
]

const Components = {}

for (const key in CompsConfig) {
  if (Object.hasOwnProperty.call(CompsConfig, key)) {
    if (Modules[key]) {
      Components[key] = defineAsyncComponent(() =>
        Modules[key]()
      )
    }
  }
}

const pages = reactive(JSON.parse(JSON.stringify(pagesConfig)))
const pageIndex = ref(0)

const pageLayer = ref('firstLayer');

const clientWidth = ref(document.documentElement.clientWidth)
const clientHeight = ref(document.documentElement.clientHeight)

const boxStyle = computed(() => {
  if (pageLayer.value === 'firstLayer') {
    return {
      width: '100%',
      height: '100%'
    }
  }
  const pageSize = pages[pageIndex.value].pageSize
  if (pages[pageIndex.value].layout === 'adapt') {
    const aspectRatio = clientWidth.value / clientHeight.value
    const adaptScale = clientHeight.value / pageSize.height
    return {
      width: `${pageSize.height * aspectRatio}px`,
      height: `${pageSize.height}px`,
      transform: `scale(${adaptScale})`,
    }
  }
  const scale = Math.min(clientWidth.value / pageSize.width, clientHeight.value / pageSize.height)
  return {
    width: `${pageSize.width}px`,
    height: `${pageSize.height}px`,
    transform: `scale(${scale})`,
  }
})

let running = false;

useEventListener(window, "resize", () => {
  if (running) {
    return;
  }
  running = true;
  requestAnimationFrame(() => {
    clientWidth.value = document.documentElement.clientWidth
    clientHeight.value = document.documentElement.clientHeight
    running = false;
  });
});

const drawer = ref("")

const editBottom = ref(false)

const mouseHover = (key) => {
  exitFocus()
  if (dragIndex !== null || activeCaret) {
    return
  }
  drawer.value = key
}

const hidePanel = () => {
  drawer.value = ""
}

const pageChange = () => {
  pageLayer.value = 'firstLayer'
}

const editPageVisible = ref(false)
const editPageIndex = ref(null)

const editPageConfig = ref({})

const editForm = ref(null)
const editRules = {
  name: [{ required: true, message: "页面名称不能为空", trigger: "blur" }],
  routePath: [
    { required: true, message: "页面路由不能为空", trigger: "blur" },
    { pattern: /^\/.+$/, message: "以“/”开头", trigger: "blur" }
  ],
}

const editPage = (index, e) => {
  e.preventDefault()
  editPageIndex.value = index
  editPageConfig.value = {
    name: pages[index].name,
    routePath: pages[index].routePath,
    disabled: pages[index].disabled,
    order: pages[index].order,
  }
  editPageVisible.value = true
}

const copyPage = (index, e) => {
  e.preventDefault()
  const copyIndex = ref(null)
  ElMessageBox({
    title: '请选择要复制的页面',
    message: () =>
      h(
        ElRadioGroup,
        {
          modelValue: copyIndex.value,
          'onUpdate:modelValue': (val) => {
            copyIndex.value = val
          },
        },
        () => {
          return pages.map((p, pIndex) => h(ElRadio,
            {
              label: pIndex,
              disabled: index === pIndex,
            },
            () => p.name
          )
          )
        }
      ),
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  })
    .then(() => {
      if (copyIndex.value !== null) {
        const name = pages[index].name
        const routePath = pages[index].routePath
        pages[index] = JSON.parse(JSON.stringify(
          {
            ...pages[copyIndex.value],
            name,
            routePath
          }
        ))
      }
    })
    .catch(() => {
    })
}

const addPage = () => {
  editPageIndex.value = null
  editPageConfig.value = JSON.parse(JSON.stringify(PageTemplate))
  editPageVisible.value = true
}

const cancelEditPage = () => {
  editPageVisible.value = false
}

const deleteEditPage = () => {
  ElMessageBox.confirm(
    '如需保存，请手动“同步配置”',
    '删除操作不会自动保存',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      editPageVisible.value = false
      deletePage(editPageIndex.value)
    })
    .catch(() => {
    })
}

function deletePage(index) {
  if (pageIndex.value > index) {
    pageIndex.value -= 1
  } else if (pageIndex.value === index) {
    if (pageIndex.value + 1 === pages.length && pageIndex.value !== 0) {
      pageIndex.value -= 1
    }
  }
  if (index > 0 || pages.length > 1) {
    pages.splice(index, 1)
  } else {
    pages.splice(index, 1, JSON.parse(JSON.stringify(PageTemplate)))
  }
}

const saveEditPage = (ref) => {
  ref.validate((valid, fields) => {
    if (valid) {
      if (editPageIndex.value === null) {
        pages.push(JSON.parse(JSON.stringify(editPageConfig.value)))
      } else {
        pages[editPageIndex.value].name = editPageConfig.value.name
        pages[editPageIndex.value].routePath = editPageConfig.value.routePath
        pages[editPageIndex.value].disabled = editPageConfig.value.disabled
        pages[editPageIndex.value].order = editPageConfig.value.order
      }
      editPageVisible.value = false
    } else {
      console.log('validate fail!')
    }
  })
}


let sourceCompKey = null
let sourceCompType = null
let dragIndex = null

const focusIndex = ref(null)
const caretDom = ref()

document.addEventListener('mouseup', (e) => {
  sourceCompKey = null
  dragIndex = null
  activeCaret = null
})

document.addEventListener('keydown', (e) => {
  if (e.key === 'Enter' || e.keyCode === 13) {
    // 当按下 Enter 键时执行的操作
    changeFocus()
  } else if (e.key === 'Delete' || e.keyCode === 46) {
    deleteComp()
  }
})

const onSourceMousedown = (compKey, compType) => {
  sourceCompKey = compKey
  sourceCompType = compType
}

let isPressTimeLimit = false

const onCompMousedown = (compIndex, e) => {
  e.stopPropagation()
  dragIndex = compIndex
  isPressTimeLimit = true
  setTimeout(() => { isPressTimeLimit = false }, 200)
}

const onCompMouseup = (index, e) => {
  if (isPressTimeLimit) {
    if (e.button === 0) {
      enterFocus(index, e)
    } else if (e.button === 2) {
      exitFocus()
    }
  }
}

const onEditMouseenter = (e) => {
  hidePanel()
  if (sourceCompKey) {
    loadComp(sourceCompKey, e)
    sourceCompKey = null
  }
}

const onEditMousedown = (e) => {
  exitFocus()
}

function enterFocus(index, e) {
  focusIndex.value = index
  updateCompAspectRatio()
  caretDom.value = e.currentTarget
}

function changeFocus() {
  if (!pages[pageIndex.value].layers[pageLayer.value] || !pages[pageIndex.value].layers[pageLayer.value].length) {
    return
  }
  if (focusIndex.value === null || focusIndex.value === mainDom.value.children.length - 1) {
    focusIndex.value = 0
  } else {
    focusIndex.value += 1
  }
  updateCompAspectRatio()
  caretDom.value = mainDom.value.children[focusIndex.value]
}

function exitFocus() {
  focusIndex.value = null
  caretDom.value = null
}

const onEditMousemove = (e) => {
  if (isPressTimeLimit) {
    return
  }
  if (activeCaret) {
    updateCompSize(e)
  } else if (dragIndex !== null) {
    updateCompPosition(e)
  }
}

function loadComp(compKey, e) {
  let comp
  if (sourceCompType === 'map' || sourceCompType === 'fullScreen' || sourceCompType === 'navigation') {
    comp = {
      key: compKey,
      style: { top: "0px", left: "0px" },
      animation: {},
    }
  } else {
    const panelRect = e.currentTarget.getBoundingClientRect();
    const panelSize = { clientWidth: e.currentTarget.clientWidth, clientHeight: e.currentTarget.clientHeight }
    comp = {
      key: compKey,
      style: getPositionStyle(e, panelRect, panelSize),
      animation: {},
    }
  }

  if (pageLayer.value === 'firstLayer' || !pages[pageIndex.value].layers[pageLayer.value]) {
    pages[pageIndex.value].layers[pageLayer.value] = [comp]
  } else {
    pages[pageIndex.value].layers[pageLayer.value].push(comp)
  }

  if (sourceCompType !== 'map' && sourceCompType !== 'fullScreen' && sourceCompType !== 'navigation') {
    dragIndex = pages[pageIndex.value].layers[pageLayer.value].length - 1
  }
}

let adjusting = false

function updateCompPosition(e) {
  if (adjusting) {
    return
  }
  adjusting = true;
  const panelRect = e.currentTarget.getBoundingClientRect();
  const panelSize = { clientWidth: e.currentTarget.clientWidth, clientHeight: e.currentTarget.clientHeight }
  requestAnimationFrame(() => {
    adjusting = false;
    if (dragIndex !== null) {
      pages[pageIndex.value].layers[pageLayer.value][dragIndex].style = {
        ...getNonPositionStyle(pages[pageIndex.value].layers[pageLayer.value][dragIndex]),
        ...getPositionStyle(e, panelRect, panelSize)
      }
    }
  })
}

function getPositionStyle(e, panelRect, panelSize) {
  const { clientX, clientY } = e

  const top = Math.floor(clientY / panelRect.height * panelSize.clientHeight)
  const style = { top: `${top}px` }

  if (clientX <= panelRect.left) {
    style.left = '0px'
    return style
  }

  if (clientX >= panelRect.right) {
    style.right = '0px'
    return style
  }

  if (clientX < (panelRect.width / 5 * 2 + panelRect.left)) {
    const left = Math.floor((clientX - panelRect.left) / panelRect.width * panelSize.clientWidth)
    style.left = `${left}px`
    return style
  }

  if (clientX > (panelRect.width / 5 * 3 + panelRect.left)) {
    const right = Math.floor((panelRect.right - clientX) / panelRect.width * panelSize.clientWidth)
    style.right = `${right}px`
    return style
  }
  return style
}

const PositionKeys = ['top', 'bottom', 'left', 'right']

function getNonPositionStyle(comp) {
  const style = {}
  if (comp.style) {
    for (const key in comp.style) {
      if (Object.hasOwnProperty.call(comp.style, key)) {
        if (!PositionKeys.includes(key)) {
          style[key] = comp.style[key];
        }
      }
    }
  }
  return style
}

const compStyle = (item, index) => {
  const { style } = item
  let focusStyle = {}
  if (focusIndex.value === index) {
    focusStyle = {
      border: '3px solid #ffffff'
    }
  }
  return {
    position: 'absolute',
    ...style,
    ...focusStyle
  }
}

let activeCaret = null

let startX = 0, startY = 0;
let domWidth = 0, domHeight = 0;
let scaleValue

const onCaretMousedown = (type, e) => {
  e.stopPropagation()
  activeCaret = type
  startX = e.clientX;
  startY = e.clientY;
  const domRect = caretDom.value.getBoundingClientRect()
  domWidth = domRect.width
  domHeight = domRect.height
  scaleValue = caretDom.value.clientHeight / domRect.height
  isPressTimeLimit = true
  setTimeout(() => { isPressTimeLimit = false }, 200)
}

function updateCompSize(e) {
  if (activeCaret === 'bottom') {
    domHeight += e.clientY - startY;
    startY = e.clientY;
  } else if (activeCaret === 'left') {
    const change = positionType.value === 'center' ? (startX - e.clientX) * 2 : (startX - e.clientX)
    domWidth += change;
    startX = e.clientX;
  } else if (activeCaret === 'right') {
    const change = positionType.value === 'center' ? (e.clientX - startX) * 2 : (e.clientX - startX)
    domWidth += change;
    startX = e.clientX;
  }
  if (adjusting) {
    return;
  }
  adjusting = true;
  requestAnimationFrame(() => {
    adjusting = false;
    if (activeCaret === 'bottom') {
      pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.height = `${Math.floor(domHeight * scaleValue)}px`
      if (bindAspectRatio.value) {
        pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.width = `${Math.floor(domHeight * scaleValue * compAspectRatio.value)}px`
      }
    } else {
      pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.width = `${Math.floor(domWidth * scaleValue)}px`
      if (bindAspectRatio.value) {
        pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.height = `${Math.floor(domWidth * scaleValue / compAspectRatio.value)}px`
      }
    }
  });
}


const positionType = computed(() => {
  if (pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.left) {
    return "left"
  } else if (pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.right) {
    return "right"
  } else {
    return "center"
  }
})

const positionTypeChange = (type) => {
  if (type === 'center') {
    pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.left = ""
    pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.right = ""
  } else if (type === 'right') {
    if (!pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.right) {
      pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.right = pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.left || '10px'
    }
    pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.left = ""
  } else if (type === 'left') {
    if (!pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.left) {
      pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.left = pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.right || '10px'
    }
    pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.right = ""
  }
}

const bindAspectRatio = ref(false)

const compAspectRatio = ref(null)

function updateCompAspectRatio() {
  const comp = pages[pageIndex.value].layers[pageLayer.value][focusIndex.value]
  const compConfig = CompsConfig[comp.key]
  compAspectRatio.value = compConfig.aspectRatio
}

const sizeChange = async (type) => {
  if (!bindAspectRatio.value) {
    return
  }
  await nextTick()
  if (type === 'width') {
    const height = Math.floor(caretDom.value.clientWidth / compAspectRatio.value)
    pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.height = `${height}px`
  } else if (type === 'height') {
    const width = Math.floor(caretDom.value.clientHeight * compAspectRatio.value)
    pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style.width = `${width}px`
  }
}

const SizeKeys = ['width', 'height']

function getCustomStyle(comp) {
  const style = {}
  if (comp.style) {
    for (const key in comp.style) {
      if (Object.hasOwnProperty.call(comp.style, key)) {
        if (!PositionKeys.includes(key) && !SizeKeys.includes(key)) {
          style[key] = comp.style[key];
        }
      }
    }
  }
  return style
}

const customStyleChange = (style) => {
  const standardStyle = getStandardStyle(pages[pageIndex.value].layers[pageLayer.value][focusIndex.value])
  pages[pageIndex.value].layers[pageLayer.value][focusIndex.value].style = { ...standardStyle, ...style }
}

function getStandardStyle(comp) {
  const style = {}
  if (comp.style) {
    for (const key in comp.style) {
      if (Object.hasOwnProperty.call(comp.style, key)) {
        if (PositionKeys.includes(key) || SizeKeys.includes(key)) {
          style[key] = comp.style[key];
        }
      }
    }
  }
  return style
}

function deleteComp() {
  caretDom.value = null
  if (focusIndex.value !== null) {
    pages[pageIndex.value].layers[pageLayer.value].splice(focusIndex.value, 1)
  }
  focusIndex.value = null
}

const syncConfig = () => {
  if (!lsid) {
    ElMessage.error('大屏id不存在，无法同步配置信息')
    return
  }
  ElMessageBox.confirm(
    '请仔细检查各项配置，确定执行点击确定',
    '此操作会覆盖数据库配置信息',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      syncDynamicPage(lsid, pages).then((res) => {
        ElMessage.success('同步配置成功')
      }).catch(() => {
        ElMessage.error('同步配置失败')
      })
    })
    .catch(() => {
    })
}

const downloadConfig = () => {
  const jsonString = JSON.stringify(pages);

  const blob = new Blob([jsonString], { type: 'application/json' });

  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = 'dynamicPage.json';

  link.click();

  URL.revokeObjectURL(link.href);
}

const onDragstart = (e) => {
  e.preventDefault();
}

const category = ref("")

const compType = ref("")

const compName = ref("")

function showComp(comp) {
  if (compName.value && !comp.name.includes(compName.value)) {
    return false
  }
  if (compType.value && compType.value === 'common' && comp.type) {
    return false
  }
  if (compType.value && compType.value !== 'common' && compType.value !== comp.type) {
    return false
  }
  if (category.value && category.value !== comp.category) {
    return false
  }
  if (pageLayer.value === 'firstLayer' && comp.type !== 'map' && comp.type !== 'fullScreen') {
    return false
  }
  return true
}

const previewConfig = () => {
  previewPage(pages[pageIndex.value], router)
}

const exitPreview = () => {
  router.replace({
    name: "PageEdit"
  })
}

onMounted(() => {
})

</script>

<style scoped lang="scss">
.main-panel {
  position: relative;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #e7e7e7;
  user-select: none;

  .drop-panel {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0px;
    top: 0px;
    z-index: 100;
  }
}

.tip-text {
  font-size: 25px;
  color: #2f2f2f;
  position: absolute;
}

.right-hover {
  width: 3px;
  height: 100vh;
  position: fixed;
  right: 0px;
  top: 0px;
}

.left-hover {
  width: 3px;
  height: 100vh;
  position: fixed;
  left: 0px;
  top: 0px;
}

.right-panel {
  position: fixed;
  right: -500px;
  top: 0px;
  width: 500px;
  height: 100vh;
  overflow-y: auto;
  padding: 20px 5px;
  background-color: #fff;
  transition-duration: 0.3s;
  transition-property: right;

  .tip {
    display: flex;
    flex-direction: column;
    margin-top: 50px;
  }

  .route-radio {
    width: 350px;
    margin-bottom: 5px;

    :deep(.el-radio__label) {
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
}

.right-active {
  right: 0px;
  box-shadow: -6px 0 16px 0 rgba(0, 0, 0, 0.08), -3px 0 6px -4px rgba(0, 0, 0, 0.12), -9px 0 28px 8px rgba(0, 0, 0, 0.05);
}

.left-panel {
  position: fixed;
  left: -380px;
  top: 0px;
  width: 380px;
  height: 100vh;
  background-color: #fff;
  transition-duration: 0.3s;
  transition-property: left;
  display: flex;
  flex-direction: column;
  visibility: hidden;
  user-select: none;

  .action {
    width: 100%;
    height: 40px;
    padding: 0px 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .content {
    width: 100%;
    height: calc(100vh - 80px);
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-y: auto;

    .component {
      width: 352px;
      margin: 10px 0px;
      display: flex;
      flex-direction: column;
      border: 1px solid #a8a8a8;

      .header {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 5px 10px;
        border-bottom: 1px solid #a8a8a8;
        margin-bottom: 5px;

        .name {
          white-space: nowrap;
          font-weight: bold;
        }

        .category {
          color: #858585;
        }

        .type {
          white-space: nowrap;
          padding: 0px 4px;
          border: 1px solid #000000;
          border-radius: 5px;
        }
      }

      .map-tip {
        width: 350px;
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #7dffb7;
        font-size: 20px;
      }
    }

  }
}

.left-active {
  visibility: visible;
  left: 0px;
  box-shadow: 6px 0 16px 0 rgba(0, 0, 0, 0.08), 3px 0 6px -4px rgba(0, 0, 0, 0.12), 9px 0 28px 8px rgba(0, 0, 0, 0.05);
}

.pageSize-input {
  width: 150px;
  margin-right: 10px;

  :deep(.ant-input-group-addon) {
    width: 65px
  }
}

.edit-panel {
  position: fixed;
  box-shadow: 0 0 16px 0 rgba(0, 0, 0, 0.08), 0 0 6px -4px rgba(0, 0, 0, 0.12), 0 0 28px 8px rgba(0, 0, 0, 0.05);
  padding: 0px 10px;
  border-radius: 5px;
  background-color: #fff;
  display: flex;
  justify-content: space-between;

  :deep(.el-form-item) {
    margin-bottom: 6px
  }

  .box {
    display: flex;
    flex-direction: column;
    align-items: center;

    span {
      font-size: 15px;
      color: #419efa;
      line-height: 37px;
      text-align: center;
      width: 100%;
      font-weight: bold;
    }
  }

  .box-border {
    margin-left: 10px;
    padding-left: 10px;
    border-left: 1px dashed #a8a8a8;
  }
}

.edit-bottom {
  bottom: 10px
}

.edit-top {
  top: 10px;
}

.caret-left {
  position: absolute;
  left: -34px;
  top: 50%;
  transform: translateY(-50%);
  cursor: col-resize;
}

.caret-right {
  position: absolute;
  right: -34px;
  top: 50%;
  transform: translateY(-50%);
  cursor: col-resize;
}

.caret-bottom {
  position: absolute;
  bottom: -34px;
  left: 50%;
  transform: translateX(-50%);
  cursor: s-resize;
}

.caret {
  font-size: 30px;
  --color: #c0c0c0;

  &:hover {
    font-size: 35px;
    --color: #409EFC;
  }
}

.exit-preview {
  z-index: 100;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
