define(["./CylinderGeometry-f1f6b038","./when-b60132fc","./arrayFill-4513d7ad","./Check-7b2a090c","./buildModuleUrl-8958744c","./Cartographic-3309dd0d","./Math-119be1a3","./Cartesian2-47311507","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./CylinderGeometryLibrary-aa453214","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4","./VertexFormat-6446fca0"],(function(e,t,a,r,n,i,d,o,b,y,c,f,u,m,C,l,G,s,p,h){"use strict";return function(a,r){return t.defined(r)&&(a=e.CylinderGeometry.unpack(a,r)),e.CylinderGeometry.createGeometry(a)}}));
